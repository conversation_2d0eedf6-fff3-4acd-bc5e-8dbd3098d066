{% extends "web/app/app_base.html" %}
{% load i18n %}
{% load static %}
{% block app %}
<section class="app-card">
    <div class="pg-columns pg-columns-reversed pg-align-items-center">
        <div class="row">
        <div class="col-8">
            <h2 class="pg-title">{% translate "You're Signed In!" %}</h2>
            <p class="pg-subtitle">
                This is your home page. You can view you existing reports. You 
                can upload new ones <a href="{% url 'reports:home' %}">here</a>
                You can learn how to use the app <a href="https://www.youtube.com/embed/8qvTXGBqQ1E">here</a>
            </p>
        </div>
        <div class="col-4">
            <img class="img-fluid" src="{% static 'images/logos/account-dashboard.png' %}" alt="{% translate "Know more about your home!" %}">
        </div>
        </div>
    </div>
</section>
{% if reports %}
<meta http-equiv="refresh" content="5">
<section class="app-card">
    <h1 class="pg-title">{% translate "Reports" %}</h1>
    <div class="pg-content">
        <p>These are your existing reports. Select any one to view them</p>
        <table class="table table-hover">
            <tr>
                <th>Report</th>
                <th>Summary</th>
                <th>Chat</th>
            </tr>
            {% for report, filename, summary_status, chat_status in reports %}
            <tr>
                <td> <a href="reports/view/{{filename}}">{{report}}</a> </td>
                <td>
                    {% if summary_status != "Done" %}
                    <span class="spinner-border spinner-border-sm" role="status"></span>
                    {% endif %}
                    {{summary_status}}
                </td>
                <td>
                    {% if chat_status != "Done" %}
                    <span class="spinner-border spinner-border-sm" role="status"></span>
                    {% endif %}
                    {{chat_status}}
                 </td>
            </tr>
            {% endfor %}
        </table>
    </div>
</section>
{% else %}
<section class="app-card">
    <h1 class="pg-title">{% translate "Learn how to use the app" %}</h1>
    <div class="pg-content">
        <iframe width="640" height="420" src="https://www.youtube.com/embed/8qvTXGBqQ1E" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe>
    </div>
</section>
{% endif %}
{% endblock %}
