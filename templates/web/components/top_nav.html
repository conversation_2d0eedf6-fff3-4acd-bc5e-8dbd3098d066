{% load i18n %}
<nav class="navbar navbar-light navbar-expand-md bg-white shadow-sm" >
  <div class="container justify-content-space-between">
    <a class="navbar-brand {% if request.path == '/' %}active{% endif %}" href="{% url 'web:home' %}">
      <h4>{{project_meta.NAME}}</h4>
    </a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse justify-content-end" id="navbarSupportedContent">
      <ul class="navbar-nav mr-auto mb-2 mb-lg-0">
        {% if user.is_authenticated %}
          <div class="navbar-item has-dropdown is-hoverable">
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                <img class="navbar-avatar" src="{{ user.avatar_url}}"> {% translate "My Account" %}
              </a>
              <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
                <li>
                  <a class="dropdown-item" href="{% url 'users:user_profile' %}" >
                    <span class="pg-icon"><i class="fa fa-user"></i></span><span>{% translate "Profile" %}</span>
                  </a>
                </li>
                {% if user.has_usable_password %}
                  <li>
                    <a class="dropdown-item" href="{% url 'account_change_password' %}">
                      <span class="pg-icon"><i class="fa fa-unlock-alt"></i></span><span>{% translate "Change Password" %}</span>
                    </a>
                  </li>
                {% endif %}
                <li><hr class="dropdown-divider"></li>
                <li>
                  <a class="dropdown-item" href="{% url 'account_logout' %}" >
                    <span class="pg-icon"><i class="fa fa-sign-out"></i></span><span>{% translate "Sign out" %}</span>
                  </a>
                </li>
                {% if user.is_superuser %}
                <li><hr class="dropdown-divider"></li>
                <li>
                  <a class="dropdown-item" href="{% url 'support:hijack_user' %}" >
                    <span class="pg-icon"><i class="fa fa-user-secret"></i></span><span>{% translate "Impersonate a User" %}</span>
                  </a>
                </li>
                {% endif %}
              </ul>
            </li>
          </div>
        {% else %}
        <li class="nav-item">
          <a class="nav-link" href="{% url 'account_signup' %}">{% translate "Sign Up" %}</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="{% url 'account_login' %}">{% translate "Sign In" %}</a>
        </li>
        {% endif %}
      </ul>
    </div>
  </div>
</nav>
