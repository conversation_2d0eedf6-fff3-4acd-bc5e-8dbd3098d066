{% extends "socialaccount/base.html" %}

{% load i18n %}
{% load form_tags %}

{% block page_title %}{% translate "Sign Up" %}{% endblock %}

{% block content %}
    <h1 class="pg-title">{% translate "Sign Up" %}</h1>

<p>{% blocktranslate with provider_name=account.get_provider.name site_name=site.name %}You are about to use your {{provider_name}} account to login to
{{site_name}}. As a final step, please complete the following form:{% endblocktranslate %}</p>

<form class="signup" id="signup_form" method="post" action="{% url 'socialaccount_signup' %}">
  {% csrf_token %}
  {{ form.non_field_errors }}
  {% render_form_fields form %}
  {% if redirect_field_value %}
  <input type="hidden" name="{{ redirect_field_name }}" value="{{ redirect_field_value }}" />
  {% endif %}
  <button class="pg-button-primary" type="submit">{% translate "Sign Up" %} &raquo;</button>
</form>
{% endblock %}
