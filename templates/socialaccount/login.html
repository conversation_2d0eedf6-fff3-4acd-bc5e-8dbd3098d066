{% extends "socialaccount/base.html" %}
{% load i18n %}

{% block content %}
{% if process == "connect" %}
<h1 class="pg-title">{% blocktranslate with provider.name as provider %}Connect {{ provider }}{% endblocktranslate %}</h1>

<p>{% blocktranslate with provider.name as provider %}You are about to connect a new third party account from {{ provider }}.{% endblocktranslate %}</p>
{% else %}
<h1>{% blocktranslate with provider.name as provider %}Sign In Via {{ provider }}{% endblocktranslate %}</h1>

<p>{% blocktranslate with provider.name as provider %}You are about to sign in using a third party account from {{ provider }}.{% endblocktranslate %}</p>
{% endif %}

<form method="post" class="mt-2">
  {% csrf_token %}
  <button class="pg-button-primary" type="submit">{% translate "Continue" %}</button>
</form>
{% endblock %}
