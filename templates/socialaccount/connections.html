{% extends "socialaccount/base.html" %}

{% load i18n %}

{% block page_title %}{% translate "Account Connections" %}{% endblock %}

{% block content %}
<p class="pg-text-right"><a href="{% url 'users:user_profile' %}">{% translate '← Return to Profile' %}</a></p>

<h1 class="pg-title">{% translate "Account Connections" %}</h1>

{% if form.accounts %}
<p>{% blocktranslate %}You can sign in to your account using any of the following third party accounts:{% endblocktranslate %}</p>


<form class="my-2" method="post" action="{% url 'socialaccount_connections' %}">
{% csrf_token %}

<fieldset>
{% if form.non_field_errors %}
<div id="errorMsg">{{ form.non_field_errors }}</div>
{% endif %}

{% for base_account in form.accounts %}
{% with base_account.get_provider_account as account %}
<div>
<label class="pg-label" for="id_account_{{ base_account.id }}">
<input id="id_account_{{ base_account.id }}" type="radio" name="account" value="{{ base_account.id }}"/>
<span class="socialaccount_provider {{ base_account.provider }} {{ account.get_brand.id }}">{{account.get_brand.name}}</span>
{{ account }}
</label>
</div>
{% endwith %}
{% endfor %}
<button type="submit" class="pg-button-danger mt-2">{% translate 'Remove' %}</button>

</fieldset>

</form>
<hr>
{% else %}
<p>{% translate 'You currently have no social network accounts connected to this account.' %}</p>
{% endif %}

<h2 class="mt-3 pg-subtitle">{% translate 'Add a 3rd Party Account' %}</h2>

<ul class="socialaccount_providers pg-content">
{% include "socialaccount/snippets/provider_list.html" with process="connect" %}
</ul>

{% include "socialaccount/snippets/login_extra.html" %}

{% endblock %}
