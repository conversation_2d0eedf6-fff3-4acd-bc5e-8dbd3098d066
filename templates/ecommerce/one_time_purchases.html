{% extends "web/app/app_base.html" %}
{% load static %}
{% block app %}
<section class="app-card">
    <div class="row">
        <div class="col-md-4 order-md-last">
                <img class="img-fluid" src="{% static 'images/undraw/undraw_shopping.svg' %}" alt="Purchases">
        </div>
        <div class="col-md">
            <h3>My One-Time Purchases</h3>
            <p class="lead">Here are the products you've purchased.</p>
            {% if purchases %}
                <h4 class="lead">Purchase Details</h4>
                <ul class="list-group">
                    {% for purchase in purchases %}
                        <li class="list-group-item">
                            <h5>{{ purchase.stripe_product.name }}</h5>
                            <p>{{ purchase.stripe_product.description }}</p>
                            <p>Purchased on: {{ purchase.purchased_at|date:"F j, Y" }}</p>
                            {% if purchase.charge %}
                                <p>Amount paid: ${{ purchase.charge.amount|floatformat:2 }}</p>
                            {% endif %}
                        </li>
                    {% endfor %}
                </ul>
            {% else %}
                <p>You have no recent purchases.</p>
            {% endif %}
        </div>
    </div>
</section>
{% endblock %}
