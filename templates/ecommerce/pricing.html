<!-- templates/ecommerce/pricing.html -->
<!DOCTYPE html>
<html>
<head>
    <title>One-Time Purchase</title>
    <script async src="https://js.stripe.com/v3/pricing-table.js"></script>
    </head>
    <body>
    <div id="error-message" style="color: red; display: none;"></div>
    {% csrf_token %}

    {% if one_time_pricing_table_id and stripe_public_key and client_reference_id %}
    <stripe-pricing-table
    pricing-table-id="{{one_time_pricing_table_id}}"
    publishable-key="{{stripe_public_key}}"
    client-reference-id="{{client_reference_id}}">
    </stripe-pricing-table>
    {% else %}
        <div class="error">Configuration error: Missing required parameters</div>
    {% endif %}

    <script>
    {% if debug %}
    // Debug logging
    console.log('Pricing table ID:', '{{one_time_pricing_table_id}}');
    {% endif %}

    // Error handling for the pricing table
    document.querySelector('stripe-pricing-table').addEventListener('error', function(event) {
        console.error('Stripe pricing table error:', event.detail);
        document.getElementById('error-message').style.display = 'block';
        document.getElementById('error-message').textContent = 
        'Error loading pricing table. Please check the console for details.';
    });

// Listen for the price-selected event
document.addEventListener('stripe-price-selected', function(e) {
    console.log('Price selected:', e.detail);
    const priceId = e.detail.priceId;
    
    // Create checkout session
    fetch('/ecommerce/create-checkout-session/', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
    },
    body: JSON.stringify({
        priceId: priceId,
        isSubscription: false
    })
    })
    .then(r => r.json())
    .then(data => {
    if (data.error) {
        throw new Error(data.error);
    }
    window.location.href = data.url;
    })
    .catch(error => {
    console.error('Checkout error:', error);
    document.getElementById('error-message').style.display = 'block';
    document.getElementById('error-message').textContent = 
    'Error creating checkout session: ' + error.message;
    });
});
</script>
</body>
</html>
