{% extends "web/app/app_base.html" %}
{% load static %}
{% load i18n %}
{% block app %}
<div class="app-card">
  <div class="flex justify-between">
    <div>
      <h1 class="pg-title">{% translate "My Chats" %}</h1>
      <h2 class="pg-subtitle">{% translate "Manage your chats here." %}</h2>
    </div>
  </div>
  {% if chats.count %}
    <div class="table-responsive">
        <table class="table pg-table">
          <thead>
          <tr>
            <th class="pg-text-left">{% translate "Chat" %}</th>
            <th class="pg-text-left">{% translate "Started On" %}</th>
          </tr>
          </thead>
          <tbody>
          {% for chat in chats.all %}
            <tr>
              <td><a href="{% url 'chat:single_chat' chat.id %}">{{ chat.name }}</a></td>
              <td>{{ chat.created_at }}</td>
            </tr>
          {% endfor %}
          </tbody>
        </table>
    </div>
  {% endif %}
</div>
{% endblock %}
