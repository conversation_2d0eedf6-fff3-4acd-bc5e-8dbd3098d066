{% extends "allauth_2fa/2fa_base.html" %}
{% load i18n %}
{% load form_tags %}

{% block app %}
  <section class="app-card">
    <h2 class="pg-title">{% translate "Setup Two-Factor Authentication" %}</h2>
    <h3 class="pg-subtitle"><strong>{% translate 'Step 1' %}:</strong></h3>
    <p>
      {% translate 'Scan the QR code below with a token generator of your choice (e.g. <a href="https://support.google.com/accounts/answer/1066447" target="_blank">Google Authenticator</a>).' %}
    </p>
    <img src="{{ qr_code_url }}"/>

    <h3 class="pg-subtitle"><strong>{% translate 'Step 2' %}:</strong></h3>
    <p>{% translate 'Input a token generated by the app:' %}</p>
    <div class="pg-columns">
      <div class="pg-column-one-third">
        <form method="post" class="mt-3">
          {% csrf_token %}
          {{ form.non_field_errors }}
          {% render_text_input form.token %}
          <div class="mt-2">
            <input class="pg-button-primary" type="submit" value="{% translate "Verify" %}">
          </div>
        </form>
      </div>
    </div>
  </section>
{% endblock %}
