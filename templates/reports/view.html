{% extends "web/app/app_base.html" %}
{% load static %}
{% load i18n %}
{% block reports_block %}
  <div id="issues-chat" class="px-4" data-report={{report.name}} data-address="{{report.address}}" data-url="{{url}}"></div>
 {% endblock %}
{% block page_js %}
  {{ issues|json_script:"issues-data" }}
  {{ preferences|json_script:"preferences-data" }}
  {{ serialized_chat|json_script:'chat' }}
  {{ api_urls|json_script:'api-urls' }}
  {{ base_url|json_script:'base-url' }}
  <script src="{% static 'js/issueschat-bundle.js' %}"></script>
{% endblock %}


