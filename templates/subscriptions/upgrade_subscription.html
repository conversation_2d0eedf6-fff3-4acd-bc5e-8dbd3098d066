{% extends "web/app/app_base.html" %}
{% load static %}
{% load i18n %}
{% block app %}
<section class="app-card"
  x-data="{selectedProduct: {{ default_product.to_json }}, selectedInterval: '{{ default_interval }}' }" x-cloak>
  {% include 'subscriptions/components/subscription_hero.html' %}
  <hr>
  {% include 'subscriptions/components/plan_selector.html' %}
  <form action="{{ subscription_urls.create_checkout_session }}" method="POST" id="checkout-form">
    {% csrf_token %}
    <script async src="https://js.stripe.com/v3/pricing-table.js"></script>
    <stripe-pricing-table pricing-table-id="{{stripe_pricing_table_id}}" publishable-key="{{stripe_public_key}}">
    </stripe-pricing-table>
  </form>
</section>
{% endblock %}