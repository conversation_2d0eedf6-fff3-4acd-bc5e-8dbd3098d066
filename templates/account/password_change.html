{% extends "web/app/app_base.html" %}
{% load static %}
{% load i18n %}
{% load account %}
{% load form_tags %}
{% block app %}
<section class="app-card">
  <h1 class="pg-subtitle">{% translate "Change Password" %}</h1>
  <form method="POST" action="{% url 'account_change_password' %}" class="password_change">
    {% csrf_token %}
    {{ form.non_field_errors }}
    {% render_field form.oldpassword %}
    {% render_field form.password1 %}
    {% render_field form.password2 %}
    <div class="mt-2">
      <input class="btn btn-primary btn-block" type="submit" value="{% translate 'Change Password' %}">
    </div>
  </form>
</section>
{% endblock %}
