{% extends "account/base.html" %}
{% load i18n %}
{% load form_tags %}
{% block content %}
  <h1 class="pg-title">{% if token_fail %}{% translate "Bad Token" %}{% else %}{% translate "Change Password" %}{% endif %}</h1>
  {% if token_fail %}
    {% url 'account_reset_password' as passwd_reset_url %}
  <div class="pg-content">
    <p>
      {% blocktranslate %}
        The password reset link was invalid, possibly because it has already been used.
      {% endblocktranslate %}
    </p>
    <p>
      {% blocktranslate %}
        Please request a <a href="{{ passwd_reset_url }}">new password reset</a>.
      {% endblocktranslate %}
    </p>
  </div>
  {% else %}
    {% if form %}
      <form method="POST" action="{{ action_url }}">
        {% csrf_token %}
        {{ form.non_field_errors }}
        {% render_field form.password1 %}
        {% render_field form.password2 %}
        <div class="mt-2">
          <input class="pg-button-primary" type="submit" value="{% translate 'Change Password' %}">
        </div>
      </form>
    {% else %}
      <p>{% translate 'Your password is now changed.' %}</p>
    {% endif %}
  {% endif %}
{% endblock %}
