{% extends "account/base.html" %}
{% load i18n %}
{% load account %}
{% block content %}
<h1 class="pg-title">{% translate "Confirm E-mail Address" %}</h1>
{% if confirmation %}
{% user_display confirmation.email_address.user as user_display %}
<p class="pg-content">
    {% blocktranslate with confirmation.email_address.email as email %}
    Please confirm that <a href="mailto:{{ email }}">{{ email }}</a>
    is an e-mail address for user {{ user_display }}.
    {% endblocktranslate %}
</p>
<form method="post" action="{% url 'account_confirm_email' confirmation.key %}">
  {% csrf_token %}
  <div class="mt-2">
    <button class="pg-button-primary" type="submit">{% translate 'Confirm' %}</button>
  </div>
</form>
{% else %}
{% url 'account_email' as email_url %}
<p class="pg-content">
    {% blocktranslate %}
    This e-mail confirmation link expired or is invalid.
    Please <a href="{{ email_url }}">issue a new e-mail confirmation request</a>.
    {% endblocktranslate %}
</p>
{% endif %}
{% endblock %}
