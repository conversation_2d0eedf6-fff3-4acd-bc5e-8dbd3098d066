{% extends "account/base.html" %}
{% load static %}
{% load i18n %}
{% load account %}
{% load form_tags %}
{% block content %}
  <div>
    <div class="row">
      <div class="col">
        <h1 class="pg-title">{% translate "Password Reset" %}</h1>
        {% if user.is_authenticated %}
        {% include "account/snippets/already_logged_in.html" %}
        {% endif %}
        <h2 class="pg-subtitle">{% translate "Forgot your password? Enter your e-mail address below, and we'll send you an e-mail allowing you to reset it." %}</h2>
        <form method="POST" action="{% url 'account_reset_password' %}" class="password_reset">
          {% csrf_token %}
          {{ form.non_field_errors }}
          {% render_field form.email %}
          <div class="mt-2">
            <input class="btn btn-primary btn-block" type="submit" value="{% translate 'Send Password Reset' %}">
          </div>
          <div class="mt-2 pg-help">
            {% blocktranslate %}
              Please contact us if you have any trouble resetting your password.
            {% endblocktranslate %}
          </div>
        </form>
    </div>
    <div class="col">
      <img class="img-fluid" src="{% static 'images/logos/home01.png' %}">
    </div>
 </div>
{% endblock %}
