{% extends "web/app/app_base.html" %}
{% load static %}
{% load i18n %}
{% load account %}
{% load form_tags %}
{% block app %}
<section class="app-card">
  <h3 class="pg-subtitle">{% translate "Set Password" %}</h3>
  <form method="POST" action="{% url 'account_set_password' %}" class="password_set">
    {% csrf_token %}
    {{ form.non_field_errors }}
    {% render_form_fields form %}
    <div class="mt-2">
      <input class="pg-button-primary" type="submit" value="{% translate 'Set Password' %}">
    </div>
  </form>
</section>
{% endblock %}
