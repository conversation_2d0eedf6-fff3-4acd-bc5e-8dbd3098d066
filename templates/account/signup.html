{% extends "web/base.html" %}
{% load static %}
{% load form_tags %}
{% block body %}
<div class="home_body_section">
  <div class="container">
    <div class="home_body_inner">
      <div class="row">
        <div class="col-lg-5" style="padding-top: 40px;">
          <div class="home_body_content">
            <h3>Sign Up</h3>
            <form method="post" class="mt-3">
              {% csrf_token %}
              {{ form.non_field_errors }}
              {% render_text_input form.email %}
              {% render_text_input form.password1 %}
              {% if project_settings.ACCOUNT_SIGNUP_PASSWORD_ENTER_TWICE %}
              {% render_text_input form.password2 %}
              {% endif %}
              <input class="btn btn-primary w-100" type="submit" value="Sign Up">
              <hr>
              <div class="has-text-grey">
                Already have account?
                <a class="muted-link" href="{% url 'account_login' %}">Go to sign in.</a>
              </div>
            </form>
          </div>
        </div>
        <div class="col-lg-7">
          <div class="home_body_img">
            <img class="img-fluid" src="{% static 'images/logos/home01.png' %}" alt="home image">
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}