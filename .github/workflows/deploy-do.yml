name: Deploy to Digital Ocean

on:
  workflow_run:
    workflows: ["Homescore Backend CI"]
    types: [completed]
    branches: ["dev"]
  workflow_dispatch:
    inputs:
      environment:
        description: "Environment to deploy to"
        type: choice
        options: [dev, prod]
        default: "dev"

# Keep concurrency to prevent parallel deployments
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read
  packages: write
  id-token: write

# TODO: Add timeout constraints for the deployment job to prevent stuck deployments
jobs:
  prepare-deployment:
    runs-on: ubuntu-latest
    if: ${{ github.event.workflow_run.conclusion == 'success' || github.event_name == 'workflow_dispatch' }}
    outputs:
      environment: ${{ steps.set-env.outputs.environment }}
      sha: ${{ steps.get-sha.outputs.sha }}
      branch: ${{ steps.set-env.outputs.branch }}
    steps:
      - id: set-env
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            # For manual triggers, check if it's a feature branch
            echo "environment=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
            if [ "${{ github.ref_name }}" = "main" ]; then
              echo "branch=main" >> $GITHUB_OUTPUT
            elif [ "${{ github.ref_name }}" = "dev" ]; then
              echo "branch=dev" >> $GITHUB_OUTPUT
            else
              # For feature branches, use the branch name
              echo "branch=$(echo ${{ github.ref_name }} | sed 's/\//-/g')" >> $GITHUB_OUTPUT
            fi
          elif [ "${{ github.event.workflow_run.head_branch }}" = "main" ]; then
            echo "environment=prod" >> $GITHUB_OUTPUT
            echo "branch=main" >> $GITHUB_OUTPUT
          else
            echo "environment=dev" >> $GITHUB_OUTPUT
            # Sanitize branch name for tag
            echo "branch=$(echo ${{ github.event.workflow_run.head_branch }} | sed 's/\//-/g')" >> $GITHUB_OUTPUT
          fi
          
          echo "Using environment: $(cat $GITHUB_OUTPUT | grep environment)"
          echo "Using branch for tags: $(cat $GITHUB_OUTPUT | grep branch)"

      # Get the SHA from the workflow that triggered this one
      - id: get-sha
        if: ${{ github.event_name == 'workflow_run' }}
        run: |
          echo "sha=${{ github.event.workflow_run.head_sha }}" >> $GITHUB_OUTPUT

  deploy-to-do:
    needs: prepare-deployment
    runs-on: ubuntu-latest
    environment: ${{ needs.prepare-deployment.outputs.environment }}
    steps:
      - uses: actions/checkout@v4

      # Login to GitHub Container Registry to pull images
      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3.3.0
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      # Install and authenticate doctl before running debug script
      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.MY_DO_ACCESS_TOKEN }}

      - name: Debug Deployment
        env:
          DO_APP_ID: ${{ vars.DO_APP_ID }}
          MY_DO_ACCESS_TOKEN: ${{ secrets.MY_DO_ACCESS_TOKEN }}
        run: |
          echo "Running deployment debug script..."
          chmod +x ./deploy/debug_deployment.sh
          ./deploy/debug_deployment.sh ${{ needs.prepare-deployment.outputs.environment }}

      - name: Deploy to DigitalOcean
        uses: digitalocean/app_action/deploy@v2
        env:
          # Required env vars for image deployment
          BUILD_LOGS: true
          DEPLOY_LOGS: true
          REPOSITORY: ${{ github.repository }}
          # Use sanitized branch names for tags
          TAG_WEB: ${{ format('ghcr.io/homescore-ai/homescore-web:{0}', needs.prepare-deployment.outputs.branch) }}
          TAG_CELERY: ${{ format('ghcr.io/homescore-ai/homescore-celery:{0}', needs.prepare-deployment.outputs.branch) }}
          # Pass the sanitized tag suffix to Digital Ocean
          TAG_SUFFIX: ${{ needs.prepare-deployment.outputs.branch }}
          USERNAME: ${{ github.actor }}
          PASSWORD: ${{ secrets.GITHUB_TOKEN }}
          # Application environment variables
          AZURE_OPENAI_API_ENDPOINT: ${{ vars.AZURE_OPENAI_API_ENDPOINT }}
          AZURE_OPENAI_API_VERSION: ${{ vars.AZURE_OPENAI_API_VERSION || '2024-08-01-preview' }}
          AZURE_OPENAI_EMBEDDINGS_MODEL: ${{ vars.AZURE_OPENAI_EMBEDDINGS_MODEL || 'text-embedding-ada-002' }}
          AZURE_OPENAI_ENDPOINT: ${{ vars.AZURE_OPENAI_ENDPOINT }}
          ENABLE_DEBUG_TOOLBAR: ${{ vars.ENABLE_DEBUG_TOOLBAR || 'False' }}
          FR_ENDPOINT: ${{ vars.FR_ENDPOINT }}
          MODEL: ${{ vars.MODEL || 'gpt-4o' }}
          OPENAI_API_TYPE: ${{ vars.OPENAI_API_TYPE || 'azure' }}
          OPENAI_MAX_TOKENS: ${{ vars.OPENAI_MAX_TOKENS || 4096 }}
          OPENAI_MODEL: ${{ vars.OPENAI_MODEL || 'gpt-4o' }}
          OPENAI_TURBO: ${{ vars.OPENAI_TURBO || 'gpt-4o' }}
          OPENAI_QUIZ_MODEL: ${{ vars.OPENAI_QUIZ_MODEL || 'gpt-4o' }}
          OPENAI_IMAGE_MODEL: ${{ vars.OPENAI_IMAGE_MODEL || 'gpt-4o' }}
          PINECONE_ENV: ${{ vars.PINECONE_ENV }}
          PINECONE_INDEX: ${{ vars.PINECONE_INDEX || 'langchain-demo' }}
          PINECONE_INFOPAY_ENV: ${{ vars.PINECONE_INFOPAY_ENV }}
          PINECONE_INFOPAY_INDEX: ${{ vars.PINECONE_INFOPAY_INDEX || 'homescore' }}
          REPORT_BUCKET: ${{ vars.REPORT_BUCKET || 'reports' }}
          SAMPLE_ADDRESS: ${{ vars.SAMPLE_ADDRESS || '109 Wendell Ave, Pittsfield, MA 01201' }}
          SENTRY_ENV: ${{ vars.SENTRY_ENV || 'production' }}
          SIMILARITY_THRESHOLD: ${{ vars.SIMILARITY_THRESHOLD || 0.75 }}
          STRIPE_LIVE_MODE: ${{ vars.STRIPE_LIVE_MODE || 'False' }}
          STRIPE_LIVE_PUBLIC_KEY: ${{ vars.STRIPE_LIVE_PUBLIC_KEY }}
          STRIPE_ONE_TIME_PRICING_TABLE_ID: ${{ vars.STRIPE_ONE_TIME_PRICING_TABLE_ID || '' }}
          STRIPE_PROD_PRICING_TABLE_ID: ${{ vars.STRIPE_PROD_PRICING_TABLE_ID || '' }}
          STRIPE_SUBSCRIPTION_PRICING_TABLE_ID: ${{ vars.STRIPE_SUBSCRIPTION_PRICING_TABLE_ID || '' }}
          STRIPE_TEST_PUBLIC_KEY: ${{ vars.STRIPE_TEST_PUBLIC_KEY }}
          SUMMARY_BUCKET: ${{ vars.SUMMARY_BUCKET || 'summary' }}
          SUPABASE_URL: ${{ vars.SUPABASE_URL }}
          TOPK: ${{ vars.TOPK || 5 }}
          USER_BUCKET: ${{ vars.USER_BUCKET || 'reports' }}
          INFOPAY_BUCKET: ${{ vars.INFOPAY_BUCKET || 'infopay' }}
          HOMESCORE_LOG_LEVEL: ${{ vars.HOMESCORE_LOG_LEVEL || 'DEBUG' }}
          DJANGO_LOG_LEVEL: ${{ vars.DJANGO_LOG_LEVEL || 'INFO' }}
          # Application secrets
          ANYMAIL_SENDGRID_API_KEY: ${{ secrets.ANYMAIL_SENDGRID_API_KEY }}
          APIFY_KEY: ${{ secrets.APIFY_KEY }}
          AZURE_OPENAI_API_KEY: ${{ secrets.AZURE_OPENAI_API_KEY }}
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          DJSTRIPE_WEBHOOK_SECRET: ${{ secrets.DJSTRIPE_WEBHOOK_SECRET }}
          FR_KEY: ${{ secrets.FR_KEY }}
          GOOGLE_API_KEY: ${{ secrets.GOOGLE_API_KEY }}
          GOOGLE_OAUTH_CLIENT_ID: ${{ secrets.GOOGLE_OAUTH_CLIENT_ID }}
          GOOGLE_OAUTH_SECRET: ${{ secrets.GOOGLE_OAUTH_SECRET }}
          PINECONE_API_KEY: ${{ secrets.PINECONE_API_KEY }}
          PINECONE_INFOPAY_API_KEY: ${{ secrets.PINECONE_INFOPAY_API_KEY }}
          REDIS_URL: ${{ secrets.REDIS_URL }}
          SENTRY_DSN: ${{ secrets.SENTRY_DSN }}
          STRIPE_LIVE_SECRET_KEY: ${{ secrets.STRIPE_LIVE_SECRET_KEY }}
          STRIPE_TEST_SECRET_KEY: ${{ secrets.STRIPE_TEST_SECRET_KEY }}
          SUPABASE_KEY: ${{ secrets.SUPABASE_KEY }}
          # Digital Ocean configuration
          POSTGRES_URL: ${{ secrets.POSTGRES_URL }}
          POSTGRES_CLUSTER_NAME: ${{ needs.prepare-deployment.outputs.environment == 'prod' && vars.PROD_POSTGRES_CLUSTER_NAME || vars.STAGING_POSTGRES_CLUSTER_NAME }}
          REDIS_CLUSTER_NAME: ${{ needs.prepare-deployment.outputs.environment == 'prod' && vars.PROD_REDIS_CLUSTER_NAME || vars.STAGING_REDIS_CLUSTER_NAME }}
          CELERY_BROKER_URL: ${{ secrets.CELERY_BROKER_URL }}
          LOGTAIL_TOKEN: ${{ secrets.LOGTAIL_TOKEN }}
          # Deployment metadata
          DEPLOYMENT_SHA: ${{ needs.prepare-deployment.outputs.sha || github.sha }}
          DEPLOYMENT_ENVIRONMENT: ${{ needs.prepare-deployment.outputs.environment }}
        with:
          token: ${{ secrets.MY_DO_ACCESS_TOKEN }}
          app_spec_location: ${{ needs.prepare-deployment.outputs.environment == 'prod' && '.do/production.yaml' || '.do/staging.yaml' }}
