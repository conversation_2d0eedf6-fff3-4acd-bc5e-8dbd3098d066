name: Homescore Backend CI

on:
  push:
    branches:
      - "**" # Run on all branch pushes
    paths-ignore:
      - "*.md"
      - "docs/**"
      - ".github/*.md"

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    environment: dev
    # Add more complete permissions needed for GitHub Container Registry
    permissions:
      contents: read
      packages: write
      id-token: write
    strategy:
      max-parallel: 4
    outputs:
      branch: ${{ github.ref_name }}
      sha: ${{ github.sha }}
    timeout-minutes: 15

    env:
      ACTIONS_RUNNER_DEBUG: true
      ACTIONS_STEP_DEBUG: true
      # Application environment variables
      AZURE_OPENAI_API_ENDPOINT: ${{ vars.AZURE_OPENAI_API_ENDPOINT }}
      AZURE_OPENAI_API_VERSION: ${{ vars.AZURE_OPENAI_API_VERSION || '2024-08-01-preview' }}
      AZURE_OPENAI_EMBEDDINGS_MODEL: ${{ vars.AZURE_OPENAI_EMBEDDINGS_MODEL || 'text-embedding-ada-002' }}
      DEBUG: ${{ vars.DEBUG || 'True' }}  # Set to True for tests
      ENABLE_DEBUG_TOOLBAR: ${{ vars.ENABLE_DEBUG_TOOLBAR || 'False' }}
      FR_ENDPOINT: ${{ vars.FR_ENDPOINT }}
      MODEL: ${{ vars.MODEL || 'gpt-4o' }}
      OPENAI_API_TYPE: ${{ vars.OPENAI_API_TYPE || 'azure' }}
      OPENAI_MAX_TOKENS: ${{ vars.OPENAI_MAX_TOKENS || '4096' }}
      OPENAI_MODEL: ${{ vars.OPENAI_MODEL || 'gpt-4o' }}
      OPENAI_TURBO: ${{ vars.OPENAI_TURBO || 'gpt-4o' }}
      OPENAI_QUIZ_MODEL: ${{ vars.OPENAI_QUIZ_MODEL || 'gpt-4o' }}
      OPENAI_IMAGE_MODEL: ${{ vars.OPENAI_IMAGE_MODEL || 'gpt-4o' }}
      PINECONE_ENV: ${{ vars.PINECONE_ENV }}
      PINECONE_INDEX: ${{ vars.PINECONE_INDEX || 'langchain-demo' }}
      PINECONE_INFOPAY_ENV: ${{ vars.PINECONE_INFOPAY_ENV }}
      PINECONE_INFOPAY_INDEX: ${{ vars.PINECONE_INFOPAY_INDEX || 'homescore' }}
      REPORT_BUCKET: ${{ vars.REPORT_BUCKET || 'reports' }}
      STRIPE_LIVE_MODE: ${{ vars.STRIPE_LIVE_MODE || 'False' }}
      STRIPE_LIVE_PUBLIC_KEY: ${{ vars.STRIPE_LIVE_PUBLIC_KEY }}
      STRIPE_ONE_TIME_PRICING_TABLE_ID: ${{ vars.STRIPE_ONE_TIME_PRICING_TABLE_ID || '' }}
      STRIPE_PROD_PRICING_TABLE_ID: ${{ vars.STRIPE_PROD_PRICING_TABLE_ID || '' }}
      STRIPE_SUBSCRIPTION_PRICING_TABLE_ID: ${{ vars.STRIPE_SUBSCRIPTION_PRICING_TABLE_ID || '' }}
      STRIPE_TEST_PUBLIC_KEY: ${{ vars.STRIPE_TEST_PUBLIC_KEY }}
      SUMMARY_BUCKET: ${{ vars.SUMMARY_BUCKET || 'summary' }}
      SUPABASE_URL: ${{ vars.SUPABASE_URL }}
      USER_BUCKET: ${{ vars.USER_BUCKET || 'reports' }}
      INFOPAY_BUCKET: ${{ vars.INFOPAY_BUCKET || 'infopay' }}
      HOMESCORE_LOG_LEVEL: ${{ vars.HOMESCORE_LOG_LEVEL || 'DEBUG' }}
      DJANGO_LOG_LEVEL: ${{ vars.DJANGO_LOG_LEVEL || 'INFO' }}
      # Application secrets
      ANYMAIL_SENDGRID_API_KEY: ${{ secrets.ANYMAIL_SENDGRID_API_KEY }}
      APIFY_KEY: ${{ secrets.APIFY_KEY }}
      AZURE_OPENAI_API_KEY: ${{ secrets.AZURE_OPENAI_API_KEY }}
      # Override DATABASE_URL for CI testing, actual deployment uses the secret value
      DATABASE_URL: ************************************/homescore
      REDIS_URL: redis://redis:6379
      DJSTRIPE_WEBHOOK_SECRET: ${{ secrets.DJSTRIPE_WEBHOOK_SECRET }}
      FR_KEY: ${{ secrets.FR_KEY }}
      GOOGLE_API_KEY: ${{ secrets.GOOGLE_API_KEY }}
      PINECONE_API_KEY: ${{ secrets.PINECONE_API_KEY }}
      SECRET_KEY: ${{ secrets.SECRET_KEY }}
      SENTRY_DSN: ${{ secrets.SENTRY_DSN }}
      STRIPE_LIVE_SECRET_KEY: ${{ secrets.STRIPE_LIVE_SECRET_KEY }}
      STRIPE_TEST_SECRET_KEY: ${{ secrets.STRIPE_TEST_SECRET_KEY }}
      SUPABASE_KEY: ${{ secrets.SUPABASE_KEY }}

    steps:
      - uses: actions/checkout@v4

      - name: Check system resources
        run: |
          echo "=== System Information ==="
          df -h
          free -h
          docker system df
          docker system info
          echo "========================="

      - name: Create env.docker
        run: |
          echo "Starting env file creation at $(date)"
          echo "SUPABASE_URL=$SUPABASE_URL" >> .env.docker
          echo "SUPABASE_KEY=$SUPABASE_KEY" >> .env.docker
          echo "PINECONE_INDEX=$PINECONE_INDEX" >> .env.docker
          echo "PINECONE_ENV=$PINECONE_ENV" >> .env.docker
          echo "PINECONE_API_KEY=$PINECONE_API_KEY" >> .env.docker
          echo "AZURE_OPENAI_API_KEY=$AZURE_OPENAI_API_KEY" >> .env.docker
          echo "AZURE_OPENAI_API_VERSION=$AZURE_OPENAI_API_VERSION" >> .env.docker
          echo "AZURE_OPENAI_API_ENDPOINT=$AZURE_OPENAI_API_ENDPOINT" >> .env.docker
          echo "AZURE_OPENAI_EMBEDDINGS_MODEL=$AZURE_OPENAI_EMBEDDINGS_MODEL" >> .env.docker
          echo "GOOGLE_API_KEY=$GOOGLE_API_KEY" >> .env.docker
          echo "FR_KEY=$FR_KEY" >> .env.docker
          echo "FR_ENDPOINT=$FR_ENDPOINT" >> .env.docker
          echo "APIFY_KEY=$APIFY_KEY" >> .env.docker
          echo "ANYMAIL_SENDGRID_API_KEY=$ANYMAIL_SENDGRID_API_KEY" >> .env.docker
          echo "DATABASE_URL=$DATABASE_URL" >> .env.docker
          echo "REDIS_URL=$REDIS_URL" >> .env.docker
          echo "DJSTRIPE_WEBHOOK_SECRET=$DJSTRIPE_WEBHOOK_SECRET" >> .env.docker
          echo "ENABLE_DEBUG_TOOLBAR=$ENABLE_DEBUG_TOOLBAR" >> .env.docker
          echo "REPORT_BUCKET=$REPORT_BUCKET" >> .env.docker
          echo "SECRET_KEY=$SECRET_KEY" >> .env.docker
          echo "SENTRY_DSN=$SENTRY_DSN" >> .env.docker
          echo "STRIPE_TEST_PUBLIC_KEY=$STRIPE_TEST_PUBLIC_KEY" >> .env.docker
          echo "STRIPE_TEST_SECRET_KEY=$STRIPE_TEST_SECRET_KEY" >> .env.docker
          echo "SUMMARY_BUCKET=$SUMMARY_BUCKET" >> .env.docker
          echo "STRIPE_LIVE_MODE=$STRIPE_LIVE_MODE" >> .env.docker
          echo "DEBUG=$DEBUG" >> .env.docker  # Use same DEBUG var consistently
          cp .env.docker .env
          echo "Env file creation completed at $(date)"

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # Updated GitHub Container Registry login for better authentication
      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3.3.0
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      # Add debug information
      - name: Debug information
        run: |
          echo "GitHub ref: ${{ github.ref }}"
          echo "GitHub ref name: ${{ github.ref_name }}"
          echo "GitHub repository: ${{ github.repository }}"
          echo "GitHub actor: ${{ github.actor }}"
          echo "GitHub workspace: ${{ github.workspace }}"
          docker --version
          docker compose version
          ls -la
          ls -la deploy/
          stat deploy/docker_startup_do.sh || echo "File not found or not accessible"
          grep -q "npm-build" Makefile && echo "npm-build still in Makefile" || echo "npm-build removed from Makefile"

      # Define sanitized branch name (replace / with -)
      - name: Set sanitized branch name
        id: vars
        run: echo "sanitized_branch=$(echo ${GITHUB_REF_NAME} | sed 's/\//-/g')" >> $GITHUB_OUTPUT

      # Set environment variable for the environment name (dev or prod)
      - name: Set environment name and tag
        id: env
        run: |
          if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "env_name=prod" >> $GITHUB_OUTPUT
            echo "tag_name=main" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/dev" ]]; then
            echo "env_name=dev" >> $GITHUB_OUTPUT
            echo "tag_name=dev" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == *"/do-prod-"* ]]; then
            # Special case for testing production config
            echo "env_name=prod" >> $GITHUB_OUTPUT
            echo "tag_name=${{ steps.vars.outputs.sanitized_branch }}" >> $GITHUB_OUTPUT
          else
            echo "env_name=feature" >> $GITHUB_OUTPUT
            echo "tag_name=${{ steps.vars.outputs.sanitized_branch }}" >> $GITHUB_OUTPUT
          fi

      # Build web image - Updated with improved tagging strategy
      # Only push when on dev/main branch or a branch with do-prod in the name
      - name: Build web image for testing
        uses: docker/build-push-action@v6.5.0
        with:
          context: .
          file: ./Dockerfile.web
          # Only push when on dev/main branch or a branch with do-prod in the name
          push: ${{ github.event_name == 'push' && (github.ref == 'refs/heads/dev' || github.ref == 'refs/heads/main' || contains(github.ref, 'do-prod')) }}
          tags: |
            ghcr.io/homescore-ai/homescore-web:${{ steps.env.outputs.tag_name }}
            ghcr.io/homescore-ai/homescore-web:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          load: ${{ !(github.event_name == 'push' && (github.ref == 'refs/heads/dev' || github.ref == 'refs/heads/main' || contains(github.ref, 'DO'))) }}

      # Build celery image - Updated with improved tagging strategy
      - name: Build celery image for testing
        uses: docker/build-push-action@v6.5.0
        with:
          context: .
          file: ./Dockerfile.celery
          # Only push when on dev/main branch or a branch with do-prod in the name
          push: ${{ github.event_name == 'push' && (github.ref == 'refs/heads/dev' || github.ref == 'refs/heads/main' || contains(github.ref, 'do-prod')) }}
          tags: |
            ghcr.io/homescore-ai/homescore-celery:${{ steps.env.outputs.tag_name }}
            ghcr.io/homescore-ai/homescore-celery:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          load: ${{ !(github.event_name == 'push' && (github.ref == 'refs/heads/dev' || github.ref == 'refs/heads/main' || contains(github.ref, 'DO'))) }}

      - name: Build the app
        run: |
          echo "=== Starting app build at $(date) ==="
          
          # Directory setup
          echo "Setting up directories..."
          mkdir -p ./apps/subscriptions
          sudo chown -R 33:33 ./apps/subscriptions  # 33 is the standard uid for www-data
          sudo chmod -R 775 ./apps/subscriptions
          
          # Start containers and capture logs
          echo "Starting containers with make init at $(date)..."
          time make init
          
          echo "Checking container status..."
          docker compose ps
          
          echo "Container logs:"
          docker compose logs
          
          echo "=== App build completed at $(date) ==="

      - name: Execute tests in the running services
        run: make test

      - name: CI Status
        run: |
          echo "CI completed successfully"
          if [[ "${{ github.event_name }}" == "push" && ("${{ github.ref }}" == "refs/heads/dev" || "${{ github.ref }}" =~ "DO") ]]; then
            echo "Images have been built and pushed to GitHub Container Registry"
            echo "Deployment workflow will be triggered"
          fi
