name: Container Registry Cleanup

on:
  # Run every 2 weeks on the 1st and 15th of the month
  schedule:
    - cron: "0 0 1,15 * *"

  # Allow manual trigger from GitHub Actions tab
  workflow_dispatch:

jobs:
  cleanup-images:
    runs-on: ubuntu-latest
    permissions:
      packages: write
    steps:
      - name: Delete old homescore-web images
        uses: actions/delete-package-versions@v4.1.0
        with:
          package-name: "homescore-web"
          package-type: "container"
          min-versions-to-keep: 10
          delete-only-untagged-versions: false
          token: ${{ secrets.GITHUB_TOKEN }}
          ignore-versions: "prod,dev"
      - name: Delete old homescore-celery images
        uses: actions/delete-package-versions@v4.1.0
        with:
          package-name: "homescore-celery"
          package-type: "container"
          min-versions-to-keep: 10
          delete-only-untagged-versions: false
          token: ${{ secrets.GITHUB_TOKEN }}
          ignore-versions: "latest,dev"

      - name: Log cleanup completion
        run: echo "Container registry cleanup completed successfully"
