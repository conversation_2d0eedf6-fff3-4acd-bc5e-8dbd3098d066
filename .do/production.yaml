name: homescore-production
region: nyc3
# =======================
# App-Level Alerts
# =======================
alerts:
  - rule: DEPLOYMENT_FAILED    # Trigger on any failed deployment attempt
  - rule: DEPLOYMENT_STARTED  # Trigger when a new deployment begins

# Shared configurations
shared_config:
  
  # Database Configuration
  databases: &shared_db_config
    - name: postgres
      production: true
      version: "16"
    - name: redis-cache
      production: true
      version: "7"

  # Logging configuration
  logging: &shared_logging
    log_destinations:
      - name: better Stack
        logtail:
          token: ${LOGTAIL_TOKEN}

  # Image configuration for production environment
  image_config: &shared_image_config
    registry_type: GHCR
    registry: ghcr.io
    registry_credentials: "${USERNAME}:${PASSWORD}"
    # Tag is dynamically set based on branch/environment
    tag: ${TAG_SUFFIX}

# Note: Database security settings (trusted sources, SSL) must be configured
# post-deployment via DO's control panel. App Platform does not support
# trusted sources during build/deployment.
databases:
  - name: postgres  # Keep this for service references
    engine: PG
    version: "16"
    production: true
    cluster_name: homescore-prod-db
    db_name: homescore
    db_user: homescore_prod_user  # More secure user name for prod
  
  - name: redis-cache  # Keep consistent naming
    engine: REDIS
    version: "7"
    production: true
    cluster_name: homescore-prod-redis

# Global environment variables defined once
envs:
  # Required configuration variables with validation
  - key: PORT
    value: "8000"
    # Required for application
  - key: DJANGO_SETTINGS_MODULE
    value: homescore.settings_production
    # Required for application
  - key: POETRY_VERSION
    value: "1.8.0"
    # Required for application

  # Database connections (required)
  - key: REDIS_URL
    scope: RUN_TIME
    value: ${redis-cache.DATABASE_URL}
    # Required for application
  - key: CELERY_BROKER_URL
    value: ${redis-cache.DATABASE_URL}
    # Required for application
  - key: DATABASE_URL
    value: ${postgres.DATABASE_URL}
    # Required for application

  # Feature flags and debug settings - specifically set to False for production
  - key: DEBUG
    value: "False"
  - key: ENABLE_DEBUG_TOOLBAR
    value: "False"

  # Azure OpenAI Configuration
  - key: AZURE_OPENAI_ENDPOINT  # Single endpoint configuration
    value: ${AZURE_OPENAI_ENDPOINT}
    # Required for application
  - key: AZURE_OPENAI_API_VERSION
    value: ${AZURE_OPENAI_API_VERSION}
    # Default: 2024-08-01-preview
    # Required for application
  - key: AZURE_OPENAI_EMBEDDINGS_MODEL
    value: ${AZURE_OPENAI_EMBEDDINGS_MODEL}
    # Default: text-embedding-ada-002
    # Required for application
  - key: MODEL
    value: ${MODEL}
    # Default: gpt-4o
    # Required for application
  - key: OPENAI_API_TYPE
    value: ${OPENAI_API_TYPE}
    # Default: azure
    # Required for application
  - key: OPENAI_MODEL
    value: ${OPENAI_MODEL}
    # Default: gpt-4o
    # Required for application
  - key: OPENAI_TURBO
    value: ${OPENAI_TURBO}
    # Default: gpt-4o
  - key: OPENAI_QUIZ_MODEL
    value: ${OPENAI_QUIZ_MODEL}
    # Default: gpt-4o
  - key: OPENAI_IMAGE_MODEL
    value: ${OPENAI_IMAGE_MODEL}
    # Default: gpt-4o
  - key: OPENAI_MAX_TOKENS
    value: ${OPENAI_MAX_TOKENS}
    # Default: 4096

  # Pinecone Configuration
  - key: PINECONE_ENV
    value: ${PINECONE_ENV}
    # Required for application
  - key: PINECONE_INDEX
    value: ${PINECONE_INDEX}
    # Required for application
  - key: PINECONE_INFOPAY_ENV
    value: ${PINECONE_INFOPAY_ENV}
    # Required for application
  - key: PINECONE_INFOPAY_INDEX
    value: ${PINECONE_INFOPAY_INDEX}
    # Required for application

  # Storage Configuration
  - key: REPORT_BUCKET
    value: ${REPORT_BUCKET}
    # Required for application
  - key: SUMMARY_BUCKET
    value: ${SUMMARY_BUCKET}
    # Required for application
  - key: USER_BUCKET
    value: ${USER_BUCKET}
    # Required for application
  - key: INFOPAY_BUCKET
    value: ${INFOPAY_BUCKET}
    # Default: infopay
    # Required for application

  # Stripe Configuration - PRODUCTION settings
  - key: STRIPE_LIVE_MODE
    value: "true"  # Set to true for production
  - key: STRIPE_LIVE_PUBLIC_KEY
    value: ${STRIPE_LIVE_PUBLIC_KEY}  # Use live key in production
  - key: STRIPE_LIVE_SECRET_KEY
    value: ${STRIPE_LIVE_SECRET_KEY}  # Use live key in production
  - key: STRIPE_PROD_PRICING_TABLE_ID
    value: ${STRIPE_PROD_PRICING_TABLE_ID}

  # Application settings
  - key: SAMPLE_ADDRESS
    value: ${SAMPLE_ADDRESS}
  - key: SIMILARITY_THRESHOLD
    value: ${SIMILARITY_THRESHOLD}
    # Default: 0.8
  - key: TOPK
    value: ${TOPK}
    # Default: 5

  # External service endpoints
  - key: FR_ENDPOINT
    value: ${FR_ENDPOINT}
    # Required for application
  - key: SUPABASE_URL
    value: ${SUPABASE_URL}
    # Required for application

  # Monitoring and error tracking
  - key: SENTRY_ENV
    value: "production"  # Environment-specific
    # Required for application
  - key: SENTRY_TRACES_SAMPLE_RATE
    value: "0.2"  # Lower sample rate for production to reduce overhead
  - key: SENTRY_PROFILES_SAMPLE_RATE
    value: "0.1"  # Lower sample rate for production to reduce overhead
    
  # Deployment metadata
  - key: DEPLOYMENT_SHA
    value: ${DEPLOYMENT_SHA}
  - key: DEPLOYMENT_ENVIRONMENT
    value: ${DEPLOYMENT_ENVIRONMENT}

  # Secrets (consider moving to a separate secrets management system)
  - key: ANYMAIL_SENDGRID_API_KEY
    value: ${ANYMAIL_SENDGRID_API_KEY}
    # This is a secret value
  - key: APIFY_KEY
    value: ${APIFY_KEY}
    # This is a secret value
  - key: AZURE_OPENAI_API_KEY
    value: ${AZURE_OPENAI_API_KEY}
    # This is a secret value
  - key: DJSTRIPE_WEBHOOK_SECRET
    value: ${DJSTRIPE_WEBHOOK_SECRET}
    # This is a secret value
  - key: FR_KEY
    value: ${FR_KEY}
    # This is a secret value
  - key: GOOGLE_API_KEY
    value: ${GOOGLE_API_KEY}
    # This is a secret value
  - key: GOOGLE_OAUTH_CLIENT_ID
    value: ${GOOGLE_OAUTH_CLIENT_ID}
    # This is a secret value
  - key: GOOGLE_OAUTH_SECRET
    value: ${GOOGLE_OAUTH_SECRET}
    # This is a secret value
  - key: PINECONE_API_KEY
    value: ${PINECONE_API_KEY}
    # This is a secret value
  - key: PINECONE_INFOPAY_API_KEY
    value: ${PINECONE_INFOPAY_API_KEY}
    # This is a secret value
  - key: SENTRY_DSN
    value: ${SENTRY_DSN}
    # This is a secret value
  - key: SUPABASE_KEY
    value: ${SUPABASE_KEY}
    # This is a secret value

  # Static files configuration
  - key: DJANGO_COLLECT_STATIC
    value: "true"  # Set to true for production
  - key: STATIC_URL
    value: "/static/"
  - key: STATIC_ROOT
    value: "/code/static_root"

# Configure services with production-optimized resources
services:
  - name: web
    run_command: bash /code/deploy/docker_startup_do.sh
    image:
      <<: *shared_image_config
      repository: "homescore-ai/homescore-web"
    http_port: 8000
    instance_size_slug: basic-s  # 1 vCPU, 2GB RAM
    envs: ${envs}
    databases: *shared_db_config
    <<: *shared_logging

    # ===============================
    # Auto-Scaling Configuration: Web
    # ===============================
    # Future Enhancement: Enable auto-scaling for production.
    # Note: Currently disabled as DigitalOcean App Platform requires 
    # 'Professional' or higher instance types (dedicated vCPUs) for auto-scaling.
    # Enable this section when upgrading instance types.
    # -------------------------------
    # autoscaling:
    #   min_instance_count: 1
    #   max_instance_count: 3 # Adjust max based on expected load
    #   metrics:
    #     cpu:
    #       # Example threshold - adjust based on performance testing
    #       percent: 70 
    # -------------------------------

    # ============================
    # Alert Configuration: Web
    # ============================
    # Defines monitoring alerts based on DigitalOcean App Spec.
    # Note: Only rules verified against the spec reference are enabled here.
    # Other alerts (e.g., custom metrics, specific event triggers) 
    # should be configured via the DigitalOcean UI for flexibility.
    alerts:
      # --------------------------
      # Currently Enabled Alerts (Spec Compliant)
      # --------------------------
      # Monitors core resource utilization.
      - rule: CPU_UTILIZATION
        operator: GREATER_THAN
        value: 80           # Threshold: Percentage
        window: FIVE_MINUTES
      - rule: MEM_UTILIZATION
        operator: GREATER_THAN
        value: 75           # Threshold: Percentage
        window: FIVE_MINUTES
      - rule: RESTART_COUNT
        operator: GREATER_THAN
        value: 2            # Threshold: Number of restarts
        window: ONE_HOUR    # Window: One hour


      # --------------------------
      # Future Enhancements (UI Configuration)
      # --------------------------
      # The following rules require verification for App Spec syntax or are better suited for UI configuration:
      # - rule: DEPLOYMENT_FAILED        # App-level rule per spec, consider moving or UI config
      # - rule: INSTANCE_CRASHED         # Verify spec syntax or use UI
      # - rule: RESPONSE_TIME_HIGH     # Verify spec syntax/params or use UI
      # - rule: HTTP_ERROR_RATE_HIGH   # Verify spec syntax/params or use UI


workers:
  - name: celery-default
    run_command: bash /code/deploy/celery_startup_do.sh
    envs:
      - key: QUEUE_NAME
        value: default
      - key: LOG_LEVEL
        value: INFO
    image:
      <<: *shared_image_config
      repository: "homescore-ai/homescore-celery"
    instance_size_slug: basic-s  # 1 vCPU, 2GB RAM
    databases: *shared_db_config
    <<: *shared_logging

    # =====================================
    # Auto-Scaling Configuration: Celery Default
    # =====================================
    # Future Enhancement: Enable auto-scaling for production.
    # Note: See 'web' service auto-scaling section for explanation.
    # -------------------------------------
    # autoscaling:
    #   min_instance_count: 1
    #   max_instance_count: 2 # Adjust max based on expected load
    #   metrics:
    #     cpu:
    #       # Example threshold - adjust based on performance testing
    #       percent: 70
    # -------------------------------------

    # ====================================
    # Alert Configuration: Celery Default
    # ====================================
    # Defines monitoring alerts based on DigitalOcean App Spec.
    # Note: See 'web' service alerts section for explanation.
    alerts:
      # --------------------------
      # Currently Enabled Alerts (Spec Compliant)
      # --------------------------
      - rule: CPU_UTILIZATION
        operator: GREATER_THAN
        value: 80           # Threshold: Percentage
        window: FIVE_MINUTES
      - rule: MEM_UTILIZATION
        operator: GREATER_THAN
        value: 75           # Threshold: Percentage
        window: FIVE_MINUTES
      - rule: RESTART_COUNT
        operator: GREATER_THAN
        value: 2            # Threshold: Number of restarts
        window: ONE_HOUR    # Time window to count restarts


      # --------------------------
      # Future Enhancements (UI Configuration)
      # --------------------------
      # The following require verification or UI config:
      # - rule: TASK_QUEUE_DEPTH_HIGH # Verify spec syntax/params or use UI
      # - rule: INSTANCE_CRASHED        # Verify spec syntax or use UI
      # - rule: TASK_EXECUTION_FAILED # Verify spec syntax/params or use UI
      # - rule: DEPLOYMENT_FAILED       # App-level rule per spec, consider UI config


  - name: celery-task-queue
    run_command: bash /code/deploy/celery_startup_do.sh
    envs:
      - key: QUEUE_NAME
        value: report_task
      - key: LOG_LEVEL
        value: INFO
    image:
      <<: *shared_image_config
      repository: "homescore-ai/homescore-celery"
    instance_size_slug: basic-s  # 1 vCPU, 2GB RAM
    databases: *shared_db_config
    <<: *shared_logging

    # ===========================================
    # Auto-Scaling Configuration: Celery Task Queue
    # ===========================================
    # Future Enhancement: Enable auto-scaling for production.
    # Note: See 'web' service auto-scaling section for explanation.
    # -------------------------------------------
    # autoscaling:
    #   min_instance_count: 1
    #   max_instance_count: 2 # Adjust max based on expected load
    #   metrics:
    #     cpu:
    #       # Example threshold - adjust based on performance testing
    #       percent: 70
    # -------------------------------------------

    # =======================================
    # Alert Configuration: Celery Task Queue
    # =======================================
    # Defines monitoring alerts based on DigitalOcean App Spec.
    # Note: See 'web' service alerts section for explanation.
    alerts:
      # --------------------------
      # Currently Enabled Alerts (Spec Compliant)
      # --------------------------
      - rule: CPU_UTILIZATION
        operator: GREATER_THAN
        value: 80           # Threshold: Percentage
        window: FIVE_MINUTES
      - rule: MEM_UTILIZATION
        operator: GREATER_THAN
        value: 75           # Threshold: Percentage
        window: FIVE_MINUTES
      - rule: RESTART_COUNT
        operator: GREATER_THAN
        value: 2            # Threshold: Number of restarts
        window: ONE_HOUR    # Window: One hour


      # --------------------------
      # Future Enhancements (UI Configuration)
      # --------------------------
      # The following require verification or UI config:
      # - rule: TASK_QUEUE_DEPTH_HIGH # Verify spec syntax/params or use UI
      # - rule: INSTANCE_CRASHED        # Verify spec syntax or use UI
      # - rule: TASK_EXECUTION_FAILED # Verify spec syntax/params or use UI
      # - rule: DEPLOYMENT_FAILED       # App-level rule per spec, consider UI config


  - name: celery-image-queue
    run_command: bash /code/deploy/celery_startup_do.sh
    envs:
      - key: QUEUE_NAME
        value: image_task
      - key: LOG_LEVEL
        value: INFO
    image:
      <<: *shared_image_config
      repository: "homescore-ai/homescore-celery"
    databases: *shared_db_config
    <<: *shared_logging

    # ===========================================
    # Auto-Scaling Configuration: Celery Image Queue
    # ===========================================
    # Future Enhancement: Enable auto-scaling for production.
    # Note: See 'web' service auto-scaling section for explanation.
    # -------------------------------------------
    # autoscaling:
    #   min_instance_count: 1
    #   max_instance_count: 2 # Adjust max based on expected load
    #   metrics:
    #     cpu:
    #       # Example threshold - adjust based on performance testing
    #       percent: 70
    # -------------------------------------------

    # ========================================
    # Alert Configuration: Celery Image Queue
    # ========================================
    # Defines monitoring alerts based on DigitalOcean App Spec.
    # Note: See 'web' service alerts section for explanation.
    alerts:
      # --------------------------
      # Currently Enabled Alerts (Spec Compliant)
      # --------------------------
      - rule: CPU_UTILIZATION
        operator: GREATER_THAN
        value: 80           # Threshold: Percentage
        window: FIVE_MINUTES
      - rule: MEM_UTILIZATION
        operator: GREATER_THAN
        value: 75           # Threshold: Percentage
        window: FIVE_MINUTES
      - rule: RESTART_COUNT
        operator: GREATER_THAN
        value: 2            # Threshold: Number of restarts
        window: ONE_HOUR    # Window: One hour


      # --------------------------
      # Future Enhancements (UI Configuration)
      # --------------------------
      # The following require verification or UI config:
      # - rule: TASK_QUEUE_DEPTH_HIGH # Verify spec syntax/params or use UI
      # - rule: INSTANCE_CRASHED        # Verify spec syntax or use UI
      # - rule: TASK_EXECUTION_FAILED # Verify spec syntax/params or use UI
      # - rule: DEPLOYMENT_FAILED       # App-level rule per spec, consider UI config