name: homescore-staging
region: nyc3

# TODO: Add more documentation comments about specific configuration choices

# Shared configurations
shared_config:
  # Note: Database security settings (trusted sources, SSL) must be configured
  # post-deployment via DO's control panel. App Platform does not support
  # trusted sources during build/deployment.
  databases: &shared_db_config
    - name: postgres
      production: true
      version: "16"
    - name: redis-cache
      production: true
      version: "7"

  logging: &shared_logging
    log_destinations:
      - name: better Stack
        logtail:
          token: ${LOGTAIL_TOKEN}

  image_config: &shared_image_config
    registry_type: GHCR
    registry: ghcr.io
    registry_credentials: "${USERNAME}:${PASSWORD}"
    tag: ${TAG_SUFFIX} # Use the dynamic tag passed from the workflow

databases:
  - name: postgres  # Keep this for service references
    engine: PG
    version: "16"
    production: true
    cluster_name: ${POSTGRES_CLUSTER_NAME}
    db_name: homescore
    db_user: homescore_staging_user  # Different user for staging

  - name: redis-cache
    engine: REDIS
    version: "7"
    production: true
    cluster_name: ${REDIS_CLUSTER_NAME}

# Global environment variables defined once
envs:
  # Required configuration variables with validation
  - key: PORT
    value: "8000"
    # Required for application
  - key: DJANGO_SETTINGS_MODULE
    value: homescore.settings_production
    # Required for application
  - key: POETRY_VERSION
    value: "1.8.0"
    # Required for application

  # Database connections (required)
  - key: REDIS_URL
    scope: RUN_TIME
    value: ${redis-cache.DATABASE_URL}
    # Required for application
  - key: CELERY_BROKER_URL
    value: ${redis-cache.DATABASE_URL}
    # Required for application
  - key: DATABASE_URL
    value: ${postgres.DATABASE_URL}
    # Required for application

  # Feature flags and debug settings
  - key: DEBUG
    value: ${DEBUG}
    # Default: False
  - key: ENABLE_DEBUG_TOOLBAR
    value: ${ENABLE_DEBUG_TOOLBAR}
    # Default: False

  # Azure OpenAI Configuration
  - key: AZURE_OPENAI_ENDPOINT  # Single endpoint configuration
    value: ${AZURE_OPENAI_ENDPOINT}
    # Required for application
  - key: AZURE_OPENAI_API_VERSION
    value: ${AZURE_OPENAI_API_VERSION}
    # Default: 2024-08-01-preview
    # Required for application
  - key: AZURE_OPENAI_EMBEDDINGS_MODEL
    value: ${AZURE_OPENAI_EMBEDDINGS_MODEL}
    # Default: text-embedding-ada-002
    # Required for application
  - key: MODEL
    value: ${MODEL}
    # Default: gpt-4o
    # Required for application
  - key: OPENAI_API_TYPE
    value: ${OPENAI_API_TYPE}
    # Default: azure
    # Required for application
  - key: OPENAI_MODEL
    value: ${OPENAI_MODEL}
    # Default: gpt-4o
  - key: OPENAI_TURBO
    value: ${OPENAI_TURBO}
    # Default: gpt-4o
  - key: OPENAI_QUIZ_MODEL
    value: ${OPENAI_QUIZ_MODEL}
    # Default: gpt-4o
  - key: OPENAI_IMAGE_MODEL
    value: ${OPENAI_IMAGE_MODEL}
    # Default: gpt-4o
  - key: OPENAI_MAX_TOKENS
    value: ${OPENAI_MAX_TOKENS}
    # Default: 4096

  # Pinecone Configuration
  - key: PINECONE_ENV
    value: ${PINECONE_ENV}
    # Required for application
  - key: PINECONE_INDEX
    value: ${PINECONE_INDEX}
    # Default: langchain-demo
    # Required for application
  - key: PINECONE_INFOPAY_ENV
    value: ${PINECONE_INFOPAY_ENV}
    # Required for application
  - key: PINECONE_INFOPAY_INDEX
    value: ${PINECONE_INFOPAY_INDEX}
    # Default: homescore
    # Required for application

  # Storage Configuration
  - key: REPORT_BUCKET
    value: ${REPORT_BUCKET}
    # Default: reports
    # Required for application
  - key: SUMMARY_BUCKET
    value: ${SUMMARY_BUCKET}
    # Default: summary
    # Required for application
  - key: USER_BUCKET
    value: ${USER_BUCKET}
    # Default: reports
    # Required for application
  - key: INFOPAY_BUCKET
    value: ${INFOPAY_BUCKET}
    # Default: infopay

  # Stripe Configuration (environment-specific)
  - key: STRIPE_LIVE_MODE
    value: "false"  # Override in production
  - key: STRIPE_PUBLIC_KEY
    value: ${STRIPE_TEST_PUBLIC_KEY}  # Use test key in staging
  - key: STRIPE_SECRET_KEY
    value: ${STRIPE_TEST_SECRET_KEY}  # Use test key in staging
  - key: STRIPE_PRICING_TABLE_ID
    value: ${STRIPE_ONE_TIME_PRICING_TABLE_ID}

  # Application settings
  - key: SAMPLE_ADDRESS
    value: ${SAMPLE_ADDRESS}
  - key: SIMILARITY_THRESHOLD
    value: ${SIMILARITY_THRESHOLD}
    # Default: 0.8
  - key: TOPK
    value: ${TOPK}
    # Default: 5

  # External service endpoints
  - key: FR_ENDPOINT
    value: ${FR_ENDPOINT}
    # Required for application
  - key: SUPABASE_URL
    value: ${SUPABASE_URL}
    # Required for application

  # Monitoring and error tracking
  - key: SENTRY_ENV
    value: "staging"  # Environment-specific
    # Required for application

  # Deployment metadata
  - key: DEPLOYMENT_SHA
    value: ${DEPLOYMENT_SHA}
  - key: DEPLOYMENT_ENVIRONMENT
    value: ${DEPLOYMENT_ENVIRONMENT}

  # Secrets (consider moving to a separate secrets management system)
  - key: ANYMAIL_SENDGRID_API_KEY
    value: ${ANYMAIL_SENDGRID_API_KEY}
    secret: true
  - key: APIFY_KEY
    value: ${APIFY_KEY}
    secret: true
  - key: AZURE_OPENAI_API_KEY
    value: ${AZURE_OPENAI_API_KEY}
    secret: true
  - key: DJSTRIPE_WEBHOOK_SECRET
    value: ${DJSTRIPE_WEBHOOK_SECRET}
    secret: true
  - key: FR_KEY
    value: ${FR_KEY}
    secret: true
  - key: GOOGLE_API_KEY
    value: ${GOOGLE_API_KEY}
    secret: true
  - key: GOOGLE_OAUTH_CLIENT_ID
    value: ${GOOGLE_OAUTH_CLIENT_ID}
    secret: true
  - key: GOOGLE_OAUTH_SECRET
    value: ${GOOGLE_OAUTH_SECRET}
    secret: true
  - key: PINECONE_API_KEY
    value: ${PINECONE_API_KEY}
    secret: true
  - key: PINECONE_INFOPAY_API_KEY
    value: ${PINECONE_INFOPAY_API_KEY}
    secret: true
  - key: SENTRY_DSN
    value: ${SENTRY_DSN}
    secret: true
  - key: SUPABASE_KEY
    value: ${SUPABASE_KEY}
    secret: true

  # Static files configuration
  - key: DJANGO_COLLECT_STATIC
    value: "false"
  - key: STATIC_URL
    value: "/static/"
  - key: STATIC_ROOT
    value: "/code/static_root"

services:
  - name: web
    run_command: bash /code/deploy/docker_startup_do.sh
    image:
      <<: *shared_image_config
      repository: "homescore-ai/homescore-web"
    http_port: 8000
    instance_size_slug: basic-xxs  # 1 vCPU, 512 MB RAM
    envs: ${envs}
    databases: *shared_db_config
    <<: *shared_logging

workers:
  - name: celery-default
    run_command: bash /code/deploy/celery_startup_do.sh
    envs:
      - key: QUEUE_NAME
        value: default
      - key: LOG_LEVEL
        value: DEBUG
    image:
      <<: *shared_image_config
      repository: "homescore-ai/homescore-celery"
    instance_size_slug: basic-xxs  # 1 vCPU, 512 MB RAM
    databases: *shared_db_config
    <<: *shared_logging

  - name: celery-task-queue
    run_command: bash /code/deploy/celery_startup_do.sh
    envs:
      - key: QUEUE_NAME
        value: report_task
      - key: LOG_LEVEL
        value: DEBUG
    image:
      <<: *shared_image_config
      repository: "homescore-ai/homescore-celery"
    instance_size_slug: basic-xxs  # 1 vCPU, 512 MB RAM
    databases: *shared_db_config
    <<: *shared_logging
          
  - name: celery-image-queue
    run_command: bash /code/deploy/celery_startup_do.sh
    envs:
      - key: QUEUE_NAME
        value: image_task
      - key: LOG_LEVEL
        value: DEBUG
    image:
      <<: *shared_image_config
      repository: "homescore-ai/homescore-celery"
    instance_size_slug: basic-xxs  # 1 vCPU, 512 MB RAM
    databases: *shared_db_config
    <<: *shared_logging
