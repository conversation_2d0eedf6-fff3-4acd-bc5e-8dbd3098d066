.idea
*.pyc
*~
db.sqlite3
.sass-cache/
celerybeat-schedule
homescore/localsettings.py
homescore/local.py
static_root/
node_modules/
openapitools.json
media/
docs/_build
_dist/
.env
.env.docker
.env.production
.DS_Store
.direnv
.envrc
.python-version
static/js/
static/js/*-bundle.js
static/css/site-bootstrap.css
static/css/site-base.css
static/css/*-bundle.css
# Generated during stripe-sync
apps/subscriptions/__products__.py

# uncomment these lines after setting up the front-end build pipeline in dev and production
# static/css/
# static/js/
.env.prod

# VSCode
.vscode/*

