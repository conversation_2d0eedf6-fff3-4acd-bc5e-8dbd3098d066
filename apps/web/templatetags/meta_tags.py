from django import template
from django.templatetags.static import static

from ..meta import absolute_url

register = template.Library()


@register.filter
def get_title(project_meta, page_title=None):
    if page_title:
        return "{} | {}".format(page_title, project_meta["NAME"])
    else:
        return project_meta["TITLE"]


@register.filter
def get_description(project_meta, page_description=None):
    return page_description or project_meta["DESCRIPTION"]


@register.filter
def get_image_url(project_meta, page_image=None):
    if page_image and page_image.startswith("/"):
        page_image = absolute_url(static(page_image))
    return page_image or project_meta["IMAGE"]
