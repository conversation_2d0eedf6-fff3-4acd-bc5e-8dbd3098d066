from allauth.socialaccount.models import SocialAccount
from django.conf import settings
from django.core import serializers
from django.shortcuts import render
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_http_methods

from apps.chat.api_url_helpers import get_chat_api_url_templates
from apps.chat.models import Chat
from apps.chat.serializers import ChatSerializer
from apps.reports.models import Report
from apps.reports.views import UploadForm


@require_http_methods(["GET"])
def home(request):
    if request.user.is_authenticated:
        form = UploadForm()
        user_reports = serializers.serialize("json", Report.objects.filter(user=request.user).order_by("name"))

        reports = {"data": user_reports}

        chat = Chat.objects.create(
            user=request.user,
        )
        serialized_chat = ChatSerializer(chat).data
        GOOGLE_API_KEY = settings.GOOGLE_API_KEY
        return render(
            request,
            "reports/home.html",
            {
                "form": form,
                "reports": reports,
                "user": request.user,
                "active_tab": "reports",
                "page_title": "Reports",
                "active_tab": "openai",
                "chat": chat,
                "serialized_chat": serialized_chat,
                "api_urls": get_chat_api_url_templates(),
                "google_api_key": GOOGLE_API_KEY,
            },
        )
    else:
        return render(request, "web/landing_page.html")


def google_auth(request):
    user = request.user
    social_account = None
    if user.is_authenticated:
        social_account = SocialAccount.objects.filter(user=user, provider="google").first()
    return render(request, "account/google_auth.html", {"social_account": social_account})


def simulate_error(request):
    raise Exception("This is a simulated error.")


# TODO: Enable CSRF back at some point.
# Solution is based on
# https://stackoverflow.com/questions/********/how-to-disable-djangos-csrf-validation
class DisableCSRFMiddleware(object):
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        setattr(request, "_dont_enforce_csrf_checks", True)
        response = self.get_response(request)
        return response
