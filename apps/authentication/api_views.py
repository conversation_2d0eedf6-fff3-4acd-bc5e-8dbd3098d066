import logging
import requests
import uuid
from typing import Any, Dict
from urllib.parse import urlencode

from allauth.mfa.models import Authenticator
from allauth.mfa.totp import TOTP
from allauth.mfa.utils import is_mfa_enabled
from dj_rest_auth.serializers import JWTSerializer
from dj_rest_auth.views import LoginView
from django.conf import settings
from django.contrib.auth import get_user_model, login
from django.core.cache import cache
from django.core.exceptions import ValidationError
from django.shortcuts import redirect
from drf_spectacular.utils import extend_schema
from rest_framework import status
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken

from apps.users.models import CustomUser
from apps.users.signals import handle_sign_up
from allauth.account.signals import user_signed_up
from .serializers import (
    GoogleLoginSerializer,
    LoginResponseSerializer,
    OtpRequestSerializer,
    PasswordResetSerializer,
)

class LoginViewWith2fa(LoginView):
    """
    Custom login view that checks if 2FA is enabled for the user.
    """

    @extend_schema(
        responses={
            status.HTTP_200_OK: LoginResponseSerializer,
        },
    )
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.user = serializer.validated_data["user"]
        if is_mfa_enabled(self.user, [Authenticator.Type.TOTP]):
            # Generate a temporary token and store it with the user object
            temp_token = str(uuid.uuid4())
            cache.set(temp_token, self.user.id, timeout=300)  # set a token that will be valid for 5 minutes
            api_auth_serializer = LoginResponseSerializer(
                data={
                    "status": "otp_required",
                    "detail": "OTP required for 2FA",
                    "temp_otp_token": temp_token,
                }
            )
            api_auth_serializer.is_valid(raise_exception=True)
            # use a different status code to make it easier for API clients to handle this case
            return Response(api_auth_serializer.data, status=200)
        else:
            super_response = super().post(request, *args, **kwargs)
            if super_response.status_code == status.HTTP_200_OK:
                # rewrap login responses to match our serializer schema
                wrapped_jwt_data = {
                    "status": "success",
                    "detail": "User logged in.",
                    "jwt": super_response.data,
                }
                return Response(wrapped_jwt_data, status=200)
            return super_response


@extend_schema(tags=["api"])
class VerifyOTPView(GenericAPIView):
    permission_classes = [AllowAny]
    serializer_class = OtpRequestSerializer

    @extend_schema(
        responses={200: JWTSerializer},
    )
    def post(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        temp_token = serializer.validated_data["temp_otp_token"]
        otp = serializer.validated_data["otp"]

        user_id = cache.get(temp_token)
        if not user_id:
            return Response(
                {"status": "token_expired", "detail": "Invalid temporary token"}, status=status.HTTP_401_UNAUTHORIZED
            )

        user = CustomUser.objects.get(id=user_id)
        if user and TOTP(Authenticator.objects.get(user=user, type=Authenticator.Type.TOTP)).validate_code(otp):
            # OTP is valid, generate JWT tokens
            refresh = RefreshToken.for_user(user)
            return Response(
                JWTSerializer(
                    {
                        "user": user,
                        "refresh": str(refresh),
                        "access": str(refresh.access_token),
                    }
                ).data,
                status=status.HTTP_200_OK,
            )
        else:
            # OTP is invalid
            return Response({"status": "invalid_otp", "detail": "Invalid OTP code"}, status=status.HTTP_400_BAD_REQUEST)


class PasswordResetView(APIView):
    def post(self, request, *args, **kwargs):
        serializer = PasswordResetSerializer(data=request.data, context={"request": request})
        if serializer.is_valid():
            serializer.save()
            return Response({"message": "Password reset email has been sent."}, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# https://medium.com/@rahadianpanjipanji/how-to-django-x-react-authentication-with-google-oauth2-2d9995b7cb91
# Exchange authorization token with access token
def google_get_access_token(code: str, redirect_uri: str) -> str:
    GOOGLE_ACCESS_TOKEN_OBTAIN_URL = "https://oauth2.googleapis.com/token"
    data = {
        "code": code,
        "client_id": settings.GOOGLE_OAUTH_CLIENT_ID,
        "client_secret": settings.GOOGLE_OAUTH_SECRET,
        "redirect_uri": redirect_uri,
        "grant_type": "authorization_code",
    }

    response = requests.post(GOOGLE_ACCESS_TOKEN_OBTAIN_URL, data=data)
    if not response.ok:
        raise ValidationError("Could not get access token from Google.")

    access_token = response.json()["access_token"]

    return access_token


# Get user info from google
def google_get_user_info(access_token: str) -> Dict[str, Any]:
    GOOGLE_USER_INFO_URL = "https://www.googleapis.com/oauth2/v3/userinfo"
    response = requests.get(GOOGLE_USER_INFO_URL, params={"access_token": access_token})

    if not response.ok:
        raise ValidationError("Could not get user info from Google.")

    return response.json()


def get_user_data(validated_data, domain: str):
    logger.info("Domain: %s", domain)
    redirect_uri = f"{domain}/accounts/google/login/callback/"

    code = validated_data.get("code")
    error = validated_data.get("error")

    if error or not code:
        raise Exception("Error in Google login")

    access_token = google_get_access_token(code=code, redirect_uri=redirect_uri)
    user_data = google_get_user_info(access_token=access_token)

    # Creates user in DB if first time login
    User = get_user_model()
    user = User.objects.filter(email=user_data["email"]).first()
    
    if not user:
        logger.info("User not found. Creating new user based on social login.")
        user_object, created = User.objects.get_or_create(
            username=user_data["email"],
            email=user_data["email"],
            defaults={
                "first_name": user_data.get("given_name"),
                "last_name": user_data.get("family_name"),
            }
        )
    else:
        user_object = user
        created = False
    
    if created:
        logger.info("New user created. Triggering handle_sign_up")
        user_signed_up.send(sender=User, request=None, user=user_object)
    else:
        logger.info("User already exists. Skipping handle_sign_up")
        
    profile_data = {
        "email": user_data["email"],
        "first_name": user_data.get("given_name"),
        "last_name": user_data.get("family_name"),
    }
    return profile_data


from django.contrib.auth import login
import logging

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class GoogleLoginApi(APIView):
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        logger.info("Google login callback")
        auth_serializer = GoogleLoginSerializer(data=request.GET)
        auth_serializer.is_valid(raise_exception=True)

        validated_data = auth_serializer.validated_data
        logger.info("Validated data: %s", validated_data)
        domain = request.build_absolute_uri("/")[:-1]
        # get origin from request headers
        origin = request.META.get("HTTP_ORIGIN", request.get_host())
        logger.info("Origin: %s", domain, origin)
        user_data = get_user_data(validated_data, domain)

        User = get_user_model()
        user = User.objects.get(email=user_data["email"])
        login(request, user, backend="django.contrib.auth.backends.ModelBackend")
        logger.info("Google login successful")
        return redirect(f"{settings.FRONT_END_URL}/")
