from urllib.parse import urlparse

from allauth.socialaccount.models import SocialApp
from django.conf import settings
from django.contrib.sites.models import Site
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = "Create Google SocialApp"

    def get_sites(self) -> dict[int, str]:
        metadata_url = settings.PROJECT_METADATA["URL"]
        reactapp_url = settings.REACT_APP_API_URL

        return {
            settings.SITE_ID: urlparse(metadata_url).netloc or metadata_url,
            settings.GOOGLE_SITE_ID: urlparse(reactapp_url).netloc or reactapp_url,
        }

    def handle(self, *args, **options):
        # Create the SocialApp
        app, created_app = SocialApp.objects.get_or_create(
            provider="google",
            name="Google",
            client_id=settings.GOOGLE_OAUTH_CLIENT_ID,
            secret=settings.GOOGLE_OAUTH_SECRET,
        )

        for id, domain in self.get_sites().items():
            # Get or create the site
            try:
                site = Site.objects.get(domain=domain)
            except Site.DoesNotExist:
                site = Site.objects.create(id=id, domain=domain, name=f"HomeScore Site {domain}")

            # Associate the SocialApp with the site
            if not app.sites.filter(id=site.id).exists():
                app.sites.add(site)

        app.save()

        if created_app:
            self.stdout.write("Google SocialApp created successfully")
        else:
            self.stdout.write("Google SocialApp already exists")
