from allauth.socialaccount.models import SocialApp
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = "Print all SocialApp instances and their associated sites"

    def handle(self, *args, **options):
        for app in SocialApp.objects.all():
            self.stdout.write(
                f"Provider: {app.provider}, Name: {app.name}, Client ID: {app.client_id}, Secret: {app.secret}"
            )
            associated_sites = [site.domain for site in app.sites.all()]
            self.stdout.write(f"Associated Sites: {associated_sites}")
