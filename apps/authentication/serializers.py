from dj_rest_auth.serializers import JWTSerializer
from django.conf import settings
from django.contrib.auth.forms import PasswordResetForm
from rest_framework import serializers

from apps.users.models import CustomUser


class LoginResponseSerializer(serializers.Serializer):
    status = serializers.CharField()
    detail = serializers.CharField()
    jwt = JWTSerializer(required=False)
    temp_otp_token = serializers.CharField(required=False)


class OtpRequestSerializer(serializers.Serializer):
    temp_otp_token = serializers.CharField()
    otp = serializers.CharField()


# serializers.py
from dj_rest_auth.serializers import PasswordResetSerializer
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from django.contrib.auth import get_user_model
from django.utils.encoding import force_bytes
import logging

# Determine whether to use Allauth or Django's default token generator and UID decoder
if "allauth" in settings.INSTALLED_APPS:
    from allauth.account.forms import default_token_generator
    from allauth.account.utils import user_pk_to_url_str as uid_encoder
    from allauth.account.utils import url_str_to_user_pk as uid_decoder
else:
    from django.contrib.auth.tokens import default_token_generator
    from django.utils.http import urlsafe_base64_encode as uid_encoder

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class CustomPasswordResetSerializer(PasswordResetSerializer):
    def save(self):
        request = self.context.get("request")  # Get the current request object

        # Retrieve the current domain dynamically from the request
        current_domain = request.META.get("HTTP_ORIGIN", request.get_host())
        frontend_url = f"{current_domain}/reset/password/"

        # Get the User model
        User = get_user_model()

        # Fetch the user(s) by email from the data sent by the user
        email = self.data["email"]
        try:
            user = User.objects.get(email=email)

            # Generate the token and uid for the password reset URL
            # Encode the user's UID correctly based on whether Allauth or Django is used
            if "allauth" in settings.INSTALLED_APPS:
                uid = uid_encoder(user)  # Pass the user object, not the primary key
            else:
                uid = uid_encoder(force_bytes(user.pk))  # Django expects the primary key

            token = default_token_generator.make_token(user)

            # Complete URL with uid and token
            reset_url = f"{frontend_url}{uid}/{token}/"
            logger.info(f"Password reset email sent to {user.email}")

            # Render both text and HTML versions of the email template
            context = {
                "user": user,
                "password_reset_url": reset_url,
                "domain": current_domain,
            }

            # Plain text version
            email_plaintext_message = render_to_string("account/email/password_reset_key_message.txt", context)

            # HTML version
            email_html_message = render_to_string("account/email/password_reset_key_message.html", context)

            # Create email object and attach plain-text and HTML versions
            email = EmailMultiAlternatives(
                subject="Password Reset Request",
                body=email_plaintext_message,  # Plain-text version
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[user.email],
            )

            # Attach the HTML version
            email.attach_alternative(email_html_message, "text/html")

            # Send the email
            try:
                email.send()
            except Exception as e:
                logger.error(f"Failed to send password reset email to {user.email}: {e}")
                # Optionally, inform the user of the failure without exposing details

        except User.DoesNotExist:
            pass  # Do not expose whether the email exists for security reasons


class GoogleLoginSerializer(serializers.Serializer):
    code = serializers.CharField(required=False)
    error = serializers.CharField(required=False)
