from django.core.management.base import BaseCommand
from apps.reports.models import Preferences, Report
from apps.reports.tasks import get_preferences
import logging
from homescore.celery import app

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Update preferences for old reports'

    
    def handle(self, *args, **options):
        for report in Report.objects.all().order_by("id"):
            if report.details_status != "Done":
                logger.info("Skipping report for %s, %s", report.user, report.file_name)
                continue
            preferences_count = Preferences.objects.filter(report_id=report.id).count()
            print(f"Here is the count of preferences for the report {report.id}:",preferences_count)
            
            if preferences_count > 8:
                logger.info("Recreating preferences for %s, %s", report.user, report.file_name)
                app.send_task("apps.reports.tasks.get_preferences", [report.file_name, report.user.id])
            else:
                logger.info("Preferences upto date.Skipping report for %s, %s", report.user, report.file_name)