from django.core.management.base import BaseCommand
from waffle.models import Flag

class Command(BaseCommand):
    help = 'Create initial waffle flags'

    def handle(self, *args, **kwargs):
        flags = [
            {'name': 'disable_purchase_check', 'everyone': False},
            {'name': 'admin_bypass_purchase', 'everyone': False},
        ]

        for flag_data in flags:
            flag, created = Flag.objects.get_or_create(name=flag_data['name'], defaults=flag_data)
            if created:
                self.stdout.write(self.style.SUCCESS(f"Successfully created flag '{flag.name}'"))
            else:
                self.stdout.write(self.style.WARNING(f"Flag '{flag.name}' already exists"))