# Generated by Django 4.2.13 on 2024-09-08 19:10

import apps.reports.models
from django.db import migrations, models
import django.db.models.deletion
import enumfields.fields


class Migration(migrations.Migration):

    dependencies = [
        ("reports", "0019_report_state"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="images",
            name="criterion",
        ),
        migrations.RemoveField(
            model_name="images",
            name="description",
        ),
        migrations.CreateModel(
            name="ImageFeature",
            fields=[
                ("id", models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "image_feature_type",
                    enumfields.fields.EnumField(default="size", enum=apps.reports.models.FeatureType, max_length=50),
                ),
                ("choice", models.CharField(default="average", max_length=255)),
                ("description", models.TextField(default="", max_length=1024)),
                (
                    "image",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, related_name="features", to="reports.images"
                    ),
                ),
            ],
        ),
    ]
