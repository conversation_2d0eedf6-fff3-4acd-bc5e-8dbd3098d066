# Generated by Django 4.2.1 on 2024-04-26 03:34

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("reports", "0014_report_image_details"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="report",
            name="image_details",
        ),
        migrations.RemoveField(
            model_name="report",
            name="image_urls",
        ),
        migrations.CreateModel(
            name="Images",
            fields=[
                ("id", models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("image_urls", models.J<PERSON><PERSON>ield(blank=True, default=list)),
                ("image_details", models.TextField(default="", max_length=2048)),
                ("report", models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="reports.report")),
            ],
        ),
    ]
