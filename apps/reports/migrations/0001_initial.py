# Generated by Django 4.2.1 on 2023-07-14 00:14

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Report",
            fields=[
                ("id", models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("name", models.CharField(max_length=256)),
                ("file_name", models.CharField(default="", max_length=64)),
                ("summary", models.TextField(default="", max_length=1024)),
                ("summary_status", models.Char<PERSON>ield(default="Processing", max_length=256)),
                ("chat_status", models.Char<PERSON>ield(default="Processing", max_length=256)),
                ("summary_version", models.IntegerField(default=0)),
                ("user", models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
