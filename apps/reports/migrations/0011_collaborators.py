# Generated by Django 4.2.1 on 2024-03-20 22:24

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("reports", "0010_issue_category"),
    ]

    operations = [
        migrations.CreateModel(
            name="Collaborators",
            fields=[
                ("id", models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "collaborator",
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
                ),
                ("report", models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="reports.report")),
            ],
        ),
    ]
