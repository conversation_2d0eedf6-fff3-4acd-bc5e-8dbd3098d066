# Generated by Django 4.2.1 on 2024-04-30 04:28

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("reports", "0015_remove_report_image_details_remove_report_image_urls_and_more"),
    ]

    operations = [
        migrations.Remove<PERSON>ield(
            model_name="images",
            name="image_details",
        ),
        migrations.RemoveField(
            model_name="images",
            name="image_urls",
        ),
        migrations.AddField(
            model_name="images",
            name="amenities",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="images",
            name="appliance_color",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="images",
            name="architectural_style",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="images",
            name="counter_space",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="images",
            name="exterior_siding",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="images",
            name="external_hazards",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="images",
            name="finish_materials",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="images",
            name="flooring",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="images",
            name="image_url",
            field=models.URLField(default="", max_length=256),
        ),
        migrations.AddField(
            model_name="images",
            name="internal_hazards",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="images",
            name="kitchen_layout",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="images",
            name="natural_light",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="images",
            name="roof_materials",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="images",
            name="roof_type",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="images",
            name="storage",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="images",
            name="type_of_room",
            field=models.CharField(default="", max_length=255),
        ),
        migrations.AddField(
            model_name="images",
            name="yard_size",
            field=models.IntegerField(blank=True, null=True),
        ),
    ]
