# Generated by Django 4.2.1 on 2023-07-19 15:46
# Merging based on best practices
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("reports", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Issue",
            fields=[
                ("id", models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("issue", models.Char<PERSON>ield(default="", max_length=256)),
                ("source", models.Char<PERSON>ield(default="", max_length=64)),
                ("urgency", models.<PERSON>r<PERSON>ield(default="Low", max_length=64)),
                ("context", models.TextField(default="", max_length=1024)),
                ("recommendation", models.TextField(default="", max_length=1024)),
                ("cost_estimate_low", models.IntegerField(default=0)),
                ("cost_estimate_high", models.IntegerField(default=0)),
                ("report", models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="reports.report")),
            ],
        ),
    ]
