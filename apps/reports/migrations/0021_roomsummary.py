# Generated by Django 4.2.13 on 2024-09-14 14:11

import apps.reports.models
from django.db import migrations, models
import django.db.models.deletion
import enumfields.fields


class Migration(migrations.Migration):

    dependencies = [
        ("reports", "0020_remove_images_criterion_remove_images_description_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="RoomSummary",
            fields=[
                ("id", models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "room_type",
                    enumfields.fields.EnumField(
                        default="living room", enum=apps.reports.models.RoomType, max_length=50
                    ),
                ),
                (
                    "feature_type",
                    enumfields.fields.EnumField(default="size", enum=apps.reports.models.FeatureType, max_length=50),
                ),
                ("summary", models.TextField(default="", max_length=1024)),
                ("values", models.JSONField(default=list)),
                ("report", models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="reports.report")),
            ],
        ),
    ]
