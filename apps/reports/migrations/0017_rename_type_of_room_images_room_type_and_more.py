# Generated by Django 4.2.1 on 2024-05-13 04:38

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("reports", "0016_remove_images_image_details_remove_images_image_urls_and_more"),
    ]

    operations = [
        migrations.RenameField(
            model_name="images",
            old_name="type_of_room",
            new_name="room_type",
        ),
        migrations.RemoveField(
            model_name="images",
            name="amenities",
        ),
        migrations.RemoveField(
            model_name="images",
            name="appliance_color",
        ),
        migrations.RemoveField(
            model_name="images",
            name="architectural_style",
        ),
        migrations.RemoveField(
            model_name="images",
            name="counter_space",
        ),
        migrations.RemoveField(
            model_name="images",
            name="exterior_siding",
        ),
        migrations.RemoveField(
            model_name="images",
            name="external_hazards",
        ),
        migrations.RemoveField(
            model_name="images",
            name="finish_materials",
        ),
        migrations.RemoveField(
            model_name="images",
            name="flooring",
        ),
        migrations.RemoveField(
            model_name="images",
            name="internal_hazards",
        ),
        migrations.RemoveField(
            model_name="images",
            name="kitchen_layout",
        ),
        migrations.RemoveField(
            model_name="images",
            name="natural_light",
        ),
        migrations.RemoveField(
            model_name="images",
            name="roof_materials",
        ),
        migrations.RemoveField(
            model_name="images",
            name="roof_type",
        ),
        migrations.RemoveField(
            model_name="images",
            name="storage",
        ),
        migrations.RemoveField(
            model_name="images",
            name="yard_size",
        ),
        migrations.AddField(
            model_name="images",
            name="criterion",
            field=models.CharField(default="", max_length=255),
        ),
        migrations.AddField(
            model_name="images",
            name="description",
            field=models.TextField(default="", max_length=1024),
        ),
    ]
