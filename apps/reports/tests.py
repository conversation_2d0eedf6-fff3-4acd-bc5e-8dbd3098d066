from django.test import TestCase, Client
from django.urls import reverse
from django.db import transaction
from django.contrib.auth import get_user_model
from .models import Report, ReportState

class ReportTestCase(TestCase):
    def setUp(self):
        self.client = Client()
        self.user = get_user_model().objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='password'
        )
        self.report = Report.objects.create(
            user=self.user,
            name='Test Report',
            file_name='test-file'
        )
        self.client.login(username='testuser', password='password')

        self.client_hacker = Client()

        get_user_model().objects.create_user(
            username='testuser_hacker',
            email='<EMAIL>',
            password='password'
        )
        self.client_hacker.login(username='testuser_hacker', password='password')

    def test_delete_report(self):
        client_not_logged_in = Client()
        response = client_not_logged_in.delete(reverse('reports:delete_report', args=[self.report.id]))
        self.assertEqual(response.status_code, 403)
        self.assertTrue(Report.objects.filter(id=self.report.id).exists())

        response = self.client_hacker.delete(reverse('reports:delete_report', args=[self.report.id]))
        self.assertEqual(response.status_code, 404)
        self.assertTrue(Report.objects.filter(id=self.report.id).exists())

        response = self.client.delete(reverse('reports:delete_report', args=[self.report.id]))
        self.assertEqual(response.status_code, 200)
        self.assertFalse(Report.objects.filter(id=self.report.id).exists())

    # Test Promote based on new state transitions
    def test_promote_report(self):
        # Initial state is Considering
        response = self.client.post(reverse('reports:update_report_state', args=[self.report.id, 'promote']))
        self.assertEqual(response.status_code, 200)
        self.report.refresh_from_db()
        self.assertEqual(self.report.state, ReportState.VIEWED)

        # Now promote from Viewed to Awaiting Details
        response = self.client.post(reverse('reports:update_report_state', args=[self.report.id, 'promote']))
        self.assertEqual(response.status_code, 200)
        self.report.refresh_from_db()
        self.assertEqual(self.report.state, ReportState.AWAITING_DETAILS)

        # Now promote from Awaiting Details to Shortlisted
        response = self.client.post(reverse('reports:update_report_state', args=[self.report.id, 'promote']))
        self.assertEqual(response.status_code, 200)
        self.report.refresh_from_db()
        self.assertEqual(self.report.state, ReportState.SHORTLISTED)

    # Test Demote based on new state transitions
    def test_demote_report(self):
        # First promote the report to Shortlisted
        self.report.state = ReportState.SHORTLISTED
        self.report.save()

        # Then demote it back to Awaiting Details
        response = self.client.post(reverse('reports:update_report_state', args=[self.report.id, 'demote']))
        self.assertEqual(response.status_code, 200)
        self.report.refresh_from_db()
        self.assertEqual(self.report.state, ReportState.AWAITING_DETAILS)

        # Then demote it back to Viewed
        response = self.client.post(reverse('reports:update_report_state', args=[self.report.id, 'demote']))
        self.assertEqual(response.status_code, 200)
        self.report.refresh_from_db()
        self.assertEqual(self.report.state, ReportState.VIEWED)

        # Then demote it back to Considering
        response = self.client.post(reverse('reports:update_report_state', args=[self.report.id, 'demote']))
        self.assertEqual(response.status_code, 200)
        self.report.refresh_from_db()
        self.assertEqual(self.report.state, ReportState.CONSIDERING)

    def test_invalid_action(self):
        response = self.client.post(reverse('reports:update_report_state', args=[self.report.id, 'invalid_action']))
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.json(), {'error': 'Invalid action'})

    # Test case for no state change
    def test_no_state_change(self):
        # Set the report state to Shortlisted
        self.report.state = ReportState.SHORTLISTED
        self.report.save()

        # Attempt to promote again, expect no state change since it's already shortlisted
        response = self.client.post(reverse('reports:update_report_state', args=[self.report.id, 'promote']))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {'message': 'Report is already shortlisted. Cannot be promoted further'})

    def test_no_state_change_on_demote(self):
        # Set the report state to Considering
        self.report.state = ReportState.CONSIDERING
        self.report.save()

        # Attempt to demote again, expect no state change since it's already in the lowest state
        response = self.client.post(reverse('reports:update_report_state', args=[self.report.id, 'demote']))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {'message': 'Report is already in considering state. Cannot be demoted further'})