import datetime
import json
import logging
from hashlib import md5
from typing import Optional


import waffle
from django import forms
from django.conf import settings
from django.contrib.auth.decorators import login_required
from django.core import serializers
from django.db.models.fields import Char<PERSON><PERSON>
from django.db.models.functions import Cast
from django.http import HttpRequest, HttpResponse, HttpResponseForbidden
from django.shortcuts import get_object_or_404, render
from django.urls import reverse
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from rest_framework.authentication import BasicAuthentication, SessionAuthentication
from rest_framework import status

from apps.chat.api_url_helpers import get_chat_api_url_templates
from apps.chat.models import Chat
from apps.chat.serializers import ChatSerializer
from apps.reports.models import Report, RoomSummary
from apps.reports.serializers import IssueSerializer, PreferencesSerializer, ReportSerializer
from apps.reports.tasks import compare_preferences, capitalize_string, get_static_values
from apps.reports.utils import get_report_data_or_404, send_invitation_email, get_report_data
from apps.users.models import CustomUser
from djstripe.models import Subscription, Product, Price
from apps.subscriptions.decorators import active_subscription_required
from apps.subscriptions.views import get_user_plan
from apps.subscriptions.feature_gating import get_feature_gate_check, feature_gate_check, FeatureGateError, PlanNotSupportedError
from apps.subscriptions.models import UserUsage
from apps.ecommerce.purchase_check import get_purchase_check
from django.db.models import F  # Add this import at the top with other imports

from . import models, tasks, utils

logger = logging.getLogger(__file__)
logger.setLevel(logging.DEBUG)

storage: utils.Supabase | None = utils.storage

# Create a listview for reports using django rest
from rest_framework import permissions, views
from rest_framework.decorators import api_view
from rest_framework.response import Response

# A list view for reports for the user
class ReportList(views.APIView):
    serializer_class = ReportSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    authentication_classes = [SessionAuthentication, BasicAuthentication]

    def get(self, request, report_id: Optional[int] = None):
        if report_id:
            reports_as_owner = models.Report.objects.filter(user=request.user, pk=report_id)
            reports_as_collaborator = models.Report.objects.filter(
                collaborators__collaborator=request.user.email, pk=report_id
            )
            instance = (reports_as_owner | reports_as_collaborator).distinct().first()
            serializer = ReportSerializer(instance)
        else:
            reports_as_owner = models.Report.objects.filter(user=request.user)
            reports_as_collaborator = models.Report.objects.filter(collaborators__collaborator=request.user.email)
            queryset = reports_as_owner | reports_as_collaborator
            serializer = ReportSerializer(queryset.distinct(), many=True)
        return Response(serializer.data)


class IssueList(views.APIView):
    serializer_class = IssueSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    authentication_classes = [SessionAuthentication, BasicAuthentication]

    def get(self, request: HttpRequest, report_id: int):
        
        # Check if current user actually owns the report
        report = get_report_data(report_id, request.user)
        if not report:
            return Response({"error": "You don't have permission to access this report."}, status=status.HTTP_403_FORBIDDEN)
        queryset = models.Issue.objects.filter(report=report)
        serializer = IssueSerializer(queryset, many=True)
        return Response(serializer.data)



class PreferencesList(views.APIView):
    serializer_class = PreferencesSerializer
    permissions_classes = [permissions.IsAuthenticatedOrReadOnly]
    queryset = models.Preferences.objects.all()

    def get(self, request: HttpRequest, report_id: int):
        # Check if user owns the report or is a collaborator

        report = get_report_data(report_id, request.user)
        if not report:
            return Response({"error": "You don't have permission to access this report."}, status=status.HTTP_403_FORBIDDEN)
        queryset = models.Preferences.objects.filter(report=report)
        serializer = PreferencesSerializer(queryset, many=True)
        return Response(serializer.data)




class ComparePreferences(views.APIView):
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    authentication_classes = [SessionAuthentication, BasicAuthentication]

    def get(self, request: HttpRequest):
        ids = request.GET.getlist("ids")
        ids = [int(id) for id in ids]

        response_data = {"preferences": {}, "comparison": []}
        all_preferences = {}
        home_addresses = {}

        for report_id in ids:
            queryset = models.Preferences.objects.filter(report=report_id)
            serializer = PreferencesSerializer(queryset, many=True)
            response_data["preferences"][str(report_id)] = serializer.data
            all_preferences[report_id] = {pref["preference"]: pref for pref in serializer.data}
        # print("All Preferences:", all_preferences)
        # Obtain the address of the homes being compared
        for report_id in ids:
            report = models.Report.objects.get(pk=report_id)
            home_addresses[report_id] = report.name
        # Calculate differences and store them
        differences = []
        for pref_key in all_preferences[ids[0]].keys():  # Assume first report's preferences as base for comparison
            base_pref = all_preferences[ids[0]][pref_key]
            for report_id in ids[1:]:
                if pref_key in all_preferences[report_id]:
                    compared_pref = all_preferences[report_id][pref_key]
                    score_diff = abs(base_pref["score"] - compared_pref["score"])
                    differences.append(
                        {
                            "preference": pref_key,
                            "difference": score_diff,
                            "report_a_score": base_pref["score"],
                            "report_a_rationale": base_pref["rationale"],
                            "report_b_score": compared_pref["score"],
                            "report_b_rationale": compared_pref["rationale"],
                        }
                    )

        # Sort differences
        top_differences = sorted(differences, key=lambda x: x["difference"], reverse=True)

        for td in top_differences:
            td.update(
                {
                    "home_a_address": home_addresses[ids[0]].split(",", 1)[0],
                    "home_b_address": home_addresses[ids[1]].split(",", 1)[0],
                }
            )

        response_data["comparison"] = compare_preferences(top_differences)
        return Response(response_data)


import math

import numpy as np

from apps.users.models import UserPreferences


class FitScore(views.APIView):
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    authentication_classes = [SessionAuthentication, BasicAuthentication]

    def get(self, request: HttpRequest):
        user_id = request.user
        report_id = request.GET.get("report_id")
        response_data = {"fit_score": 0.0}

        # Get all preferences table for home and user
        home_preferences_objs = models.Preferences.objects.filter(report=report_id)
        user_preferences_objs = UserPreferences.objects.filter(user=user_id)

        # Create dictionary for values of preferences with it's scores
        home_scores_dict = {preference.preference: preference.score for preference in home_preferences_objs}
        user_scores_dict = {preference.preference: preference.score for preference in user_preferences_objs}
        logger.debug("Home preferences and scores: %s", str(home_scores_dict))
        logger.debug("User preferences and scores: %s", str(user_scores_dict))

        if len(home_scores_dict.values()) != len(user_scores_dict.values()):
            return Response(
                {
                    "error": "Home and User preferences don't intersect with given default preferences",
                    "home_preferences": str(home_scores_dict),
                    "user_preferences": str(user_scores_dict),
                },
                status=416,
            )

        # Calculate differences between metric values
        difference_array = np.array(list(home_scores_dict.values())) - np.array(list(user_scores_dict.values()))

        # Calculate the mean square distance
        mean_square_distance = np.mean(np.square(difference_array))

        # Calculate the maximum possible distance
        max_distance = 6 * 6
        fit_score = 1 - mean_square_distance / max_distance

        response_data["fit_score"] = fit_score
        return Response(response_data)


class UploadForm(forms.Form):
    name = forms.CharField(max_length=256)
    file = forms.FileField(allow_empty_file=False)


# Create your views here.
@login_required
@require_http_methods(["GET"])
def home(request):
    form = UploadForm()
    user_reports = ReportSerializer(models.Report.objects.filter(user=request.user).order_by("-id"), many=True).data
    reports = {"data": user_reports}

    chat, _ = Chat.objects.update_or_create(
        user=request.user,
        name="home",
        file_name="",
    )
    serialized_chat = ChatSerializer(chat).data

    return render(
        request,
        "reports/home.html",
        {
            "form": form,
            "reports": reports,
            "user": request.user,
            "active_tab": "reports",
            "page_title": "Reports",
            "active_tab": "openai",
            "chat": chat,
            "serialized_chat": serialized_chat,
            "api_urls": get_chat_api_url_templates(),
        },
    )


@login_required
@api_view(["POST"])
def upload(request):
    # Check multiple conditions for purchase check bypass
    should_check_purchase = True
    
    if (
        (waffle.flag_is_active(request, 'disable_purchase_check') and request.user.is_staff) or  # Global override
        (waffle.flag_is_active(request, 'admin_bypass_purchase')  and request.user.is_superuser)    # Admin/staff bypass
    ):
        should_check_purchase = False
        
    if should_check_purchase:
        check_result = get_purchase_check(request.user)
        if not check_result.passed:
            return HttpResponseForbidden(check_result.message)
    
    # Always track usage regardless of purchase check
    with transaction.atomic():
        usage, created = UserUsage.objects.get_or_create(
            user=request.user,
            defaults={'homes_added': 1}
        )
    
        if not created:
            usage.homes_added = F('homes_added') + 1
            usage.save(update_fields=['homes_added'])
    
    # Continue with existing upload logic
    file = request.FILES.get("file")
    name = address = request.POST.get("address")
    time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    filename = md5((request.user.username + name + time).encode("utf-8")).hexdigest()
    report = models.Report.objects.create(
        user=request.user,
        name=address,
        file_name=filename,
        summary_status="Uploading",
        chat_status="Not ready",
        address=address,
    )
    report.save()
    logger.info("Generating chat.")
    tasks.create_chat.delay(filename, request.user.id)
    tasks.get_preferences.delay(filename, request.user.id)

    if file:
        logger.info("File provided. uploading")
        logger.info("Trying to upload %s of size: %d", filename, file.size)
        url = storage.put_file(settings.REPORT_BUCKET, filename, file)
        logger.info("Generating summary.")
        tasks.summary.delay(filename, request.user.id, url)
    else:
        report.has_report = False
        report.chat_status = "Done"
        report.summary_status = "Done"
        report.save()
        logger.info("File not providing. Skipping upload")
    return Response(ReportSerializer(report).data, status=200)


from django.http import FileResponse, Http404


class ReportPDF(views.APIView):
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    authentication_classes = [SessionAuthentication, BasicAuthentication]

    def get(self, request, report_id: int):
        try:
            reports_as_owner = models.Report.objects.filter(user=request.user, pk=report_id)
            reports_as_collaborator = models.Report.objects.filter(
                collaborators__collaborator=request.user.email, pk=report_id
            )
            report = (reports_as_owner | reports_as_collaborator).distinct().first()
            filename = report.file_name
            logger.info("Trying to get report %s", filename)
            if report.has_report:
                # Assuming that the PDF file is stored in a field named 'file'
                url = storage.get_url(settings.REPORT_BUCKET, filename)
                return Response({"url": url})
            else:
                raise Http404("Report not found")
        except models.Report.DoesNotExist:
            raise Http404("Report not found")


class view_image_urls(views.APIView):
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    authentication_classes = [SessionAuthentication, BasicAuthentication]
    queryset = models.Images.objects.all()

    def get(self, request: HttpRequest, report_id: int):
        # Check if user owns the report or is a collaborator

        report = get_report_data(report_id, request.user)
        if not report:
            return Response({"error": "You don't have permission to access this report."}, status=status.HTTP_403_FORBIDDEN)
        queryset = models.Images.objects.filter(report=report)
        image_data = (
        queryset.annotate(room_type_str=Cast("room_type", CharField(max_length=255)))
        .values("image_url", "room_type_str")
        .distinct()
        )
        
        return Response({"images": list(image_data)})

def capitalize_words(s: str) -> str:
    return ' '.join(word.capitalize() for word in s.split())

class room_details(views.APIView):
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    authentication_classes = [SessionAuthentication, BasicAuthentication]
    queryset = models.Images.objects.all()

    def get(self, request, report_id: int):
        # Check if user owns the report or is a collaborator
        report = get_object_or_404(models.Report, pk=report_id)
        with open("apps/reports/sample_room_data.json", "r") as file:
            image_data = json.load(file)

        # Return the dictionary in the response
        return Response({"images": image_data})
    
    
class room_details_updated(views.APIView):
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    authentication_classes = [SessionAuthentication, BasicAuthentication]
    queryset = models.Images.objects.all()

    def get(self, request: HttpRequest, report_id: int):
        # Initialize variables
        room_details = {"images": {}}
        user = request.user.id
        # Step 1: Retrieve the report
        report = get_report_data(report_id, request.user)
        if not report:
            return Response({"error": "You don't have permission to access this report."}, status=status.HTTP_403_FORBIDDEN)


        # Step 2: Fetch all RoomSummary objects for the report
        try: 
            room_summaries = RoomSummary.objects.filter(report=report)
            
            # Step 3: Organize summaries by room_type
            for summary in room_summaries:
                room_type = summary.room_type.value

                room_type_capitalized = capitalize_string(room_type)

                if room_type_capitalized not in room_details["images"]:
                    room_details["images"][room_type_capitalized] = {
                        "room_type": room_type_capitalized,
                        "features": []
                    }
                    
                static_values = get_static_values(room_type_capitalized, summary.feature_type.value)
                static_values = {k: list(v) if isinstance(v, set) else v for k, v in static_values.items()}
                
                # Prepare feature data
                feature_data = {
                    "feature": capitalize_string(summary.feature_type.value) if summary.feature_type else summary.feature_type.value,
                    "summary": summary.summary,
                    "values": [value.capitalize() for value in summary.values if value is not None],
                    # Include static values if available
                    **static_values
                }

                room_details["images"][room_type_capitalized]["features"].append(feature_data)

            # Step 4: Return the response
            return Response({"message": room_details}, status=status.HTTP_200_OK)
        
        except RoomSummary.DoesNotExist:
            return Response({"error": "Room summaries not found."}, status=status.HTTP_404_NOT_FOUND)
        
        # Check the room summary status outside of the except block
        if report.room_summary_status != "Done":
            return Response({"info": "Room summaries not ready."})


    
class image_details(views.APIView):
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    authentication_classes = [SessionAuthentication, BasicAuthentication]
    queryset = models.Images.objects.all()

    def get(self, request, report_id: int):
        # Check if user owns the report or is a collaborator
        report = get_report_data(report_id, request.user)
        if not report:
            return Response({"error": "You don't have permission to access this report."}, status=status.HTTP_403_FORBIDDEN)
        images = models.Images.objects.filter(report=report).order_by('room_type')

        
        # Check if there are no images in the database for the given report
        if not images.exists():
            # Trigger the async task to fetch image details if no data is found
            user = request.user.id
            # Return a response indicating that the task has been triggered
            return Response({"message": "No image data found. Fetching image details..."})

        # Dictionary to group images by room type
        images_by_room = {}

        # Group images by room_type
        for image in images:
            room_type = capitalize_words(image.room_type.name.lower())  # Get the display name of the enum (assuming EnumField)
            if room_type not in images_by_room:
                images_by_room[room_type] = []

            # Fetch related features
            features = models.ImageFeature.objects.filter(image=image).all()
            
            feature_list = [{
                "feature_type": capitalize_words(feature.image_feature_type.value.lower()),  # Assuming EnumField
                "choice": capitalize_words(feature.choice),
                "description": feature.description
            } for feature in features]
            logger.info("feature_list",feature_list)
            images_by_room[room_type].append({
                "image_url": image.image_url,
                "features": feature_list
            })
            
        # Wrap the result in a dictionary with "image_data" as the key
        return Response({"image_data": images_by_room})

def view(request, filename):
    report = models.Report.objects.get(user=request.user.id, file_name=filename)
    if report.has_report:
        url = storage.get_url(settings.REPORT_BUCKET, filename)
    else:
        url = None
    chat = get_object_or_404(Chat, user=request.user, file_name=filename)
    serialized_chat = ChatSerializer(chat).data

    chat = get_object_or_404(Chat, user=request.user, file_name=filename)
    serialized_chat = ChatSerializer(chat).data

    homescore = report.homescore
    if waffle.flag_is_active(request, "disable_homescore"):
        homescore = 0
    chat = get_object_or_404(Chat, user=request.user, file_name=filename)
    serialized_chat = ChatSerializer(chat).data
    issues_data = serializers.serialize("json", models.Issue.objects.filter(report=report))
    preferences_data = serializers.serialize("json", models.Preferences.objects.filter(report=report))

    logging.info("preferences:%s", preferences_data)
    return render(
        request,
        "reports/view.html",
        {
            "url": url,
            "report": report,
            "issues": issues_data,
            "preferences": preferences_data,
            "homescore": homescore,
            "serialized_chat": serialized_chat,
            "api_urls": get_chat_api_url_templates(),
            "base_url": request.build_absolute_uri("/"),
        },
    )


import requests
from django.http import FileResponse, Http404
from rest_framework import status
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework.views import APIView

from apps.reports.models import Collaborators
from apps.reports.serializers import CollaboratorSerializer


@login_required
@api_view(["GET"])
def places(request):
    issue = request.GET.get("issue", "electrical")
    PROMPT = """
    You are a home repairs expert. Identify the professional or type of
    establishment to search given the home repair issue below.
    Provide a single best choice of professional and keep it to 3 words or less.
    """
    logger.info("Issue: %s", issue)
    messages = [
        {"role": "system", "content": PROMPT},
        {"role": "user", "content": "The issue is: %s" % (issue)},
    ]
    issue = tasks.get_llm_response(settings.OPENAI_MODEL, messages, max_tokens=settings.OPENAI_ISSUE_SUMMARY_MAX_TOKENS)
    logger.info("Issue: %s", issue)
    address = request.GET.get("address", settings.DEFAULT_ADDRESS)
    url = settings.GOOGLE_PLACES_URL
    key = settings.GOOGLE_API_KEY
    params = {"key": key, "type": "establishment", "query": f"{issue} store near {address}"}
    response = requests.get(url, params)
    details = response.json()
    # Get details

    return HttpResponse(
        json.dumps({"results": details.get("results", [])[0:10]}),
        headers={
            "Access-Control-Allow-Origin": "*",
            "Content-Type": "application/json",
        },
    )


@login_required
@api_view(["GET"])
def place_details(request):
    place_id = request.GET.get("place_id")
    key = settings.GOOGLE_API_KEY
    url = settings.GOOGLE_PLACES_DETAILS_URL
    params = {"key": key, "place_id": place_id}
    response = requests.get(url, params)
    return HttpResponse(
        json.dumps({"url": reverse("reports:place_details"), "results": response.json()}),
        headers={
            "Access-Control-Allow-Origin": "*",
            "Content-Type": "application/json",
        },
    )


# Function to get places based on a category near a given address
def get_places(api_key, address, category, max_results: int = 10):
    url = settings.GOOGLE_PLACES_URL
    params = {"key": api_key, "type": "establishment", "query": f"{category} near {address}"}
    response = requests.get(url, params=params)
    details = response.json()

    results = details.get("results", [])[0:max_results]
    # Create a list of dictionaries to store the results
    places = []
    for result in results:
        place = {"name": result.get("name"), "address": result.get("formatted_address")}
        places.append(place)

    return places, results


# Function to get the distance and duration between two addresses
def get_distance(api_key, address_from, address_to):
    url = settings.GOOGLE_DISTANCE_MATRIX_URL
    params = {"units": "imperial", "origins": address_from, "destinations": address_to, "key": api_key}
    response = requests.get(url, params=params)
    data = response.json()

    # Check for the existence of necessary fields
    try:
        distance_text = data["rows"][0]["elements"][0]["distance"]["text"]
        duration_text = data["rows"][0]["elements"][0]["duration"]["text"]
        distance_value = float(distance_text.replace(" mi", "").replace(",", ""))
        duration_value = data["rows"][0]["elements"][0]["duration"]["value"]
    except (KeyError, IndexError, ValueError):
        # Return None values or default values in case of missing data
        # TODO: Do we need it for sentry?
        return None, "N/A", "N/A", None

    return distance_value, distance_text, duration_text, duration_value


def get_place_photo_url(api_key, place_id):
    url = settings.GOOGLE_PLACES_DETAILS_URL
    params = {"place_id": place_id, "key": api_key, "fields": "photos"}
    response = requests.get(url, params=params)
    details = response.json()

    if "result" not in details or "photos" not in details["result"]:
        return "N/A"

    photos = details["result"]["photos"]
    photo_reference = photos[0]["photo_reference"]
    photo_url = f"{settings.GOOGLE_PLACES_PHOTO_URL}?maxwidth=400&photoreference={photo_reference}&key={api_key}"

    return photo_url


def get_place_details(api_key, place_id):
    url = settings.GOOGLE_PLACES_DETAILS_URL
    params = {"place_id": place_id, "key": api_key, "fields": "photos,opening_hours,name"}
    response = requests.get(url, params=params)
    details = response.json()

    if "result" not in details:
        return {"closing_time": "N/A", "photo_url": "N/A", "name": "N/A", "open_now": "N/A"}

    # Extract closing time
    closing_time = "N/A"
    if "opening_hours" in details["result"] and "periods" in details["result"]["opening_hours"]:
        for period in details["result"]["opening_hours"]["periods"]:
            if "close" in period:
                closing_time = period["close"].get("time", "N/A")
                break

    # Extract photo URL
    photo_url = "N/A"
    if "photos" in details["result"]:
        photos = details["result"]["photos"]
        if photos:
            photo_reference = photos[0]["photo_reference"]
            photo_url = (
                f"{settings.GOOGLE_PLACES_PHOTO_URL}?maxwidth=400&photoreference={photo_reference}&key={api_key}"
            )

    # Extract name and open_now status
    name = details["result"].get("name", "N/A")
    open_now = details["result"].get("opening_hours", {}).get("open_now", "N/A")

    return {"closing_time": closing_time, "photo_url": photo_url, "name": name, "open_now": open_now}


@login_required
@api_view(["GET"])
def places_view(request, category):
    api_key = settings.GOOGLE_API_KEY
    address = request.GET.get("address", settings.DEFAULT_ADDRESS)
    valid_categories = {
        "hospital": "Hospital",
        "pharmacy": "Pharmacy",
        "urgent_care": "Urgent Care",
        "restaurants": "Famous Restaurants",
        "supermarkets": "Supermarket",
    }

    if category not in valid_categories:
        return HttpResponse(
            json.dumps({"error": "Invalid category"}, indent=4), content_type="application/json", status=400
        )

    places, raw_data = get_places(api_key, address, valid_categories[category])

    distances = []
    for place, result in zip(places, raw_data):
        place_address = place["address"]
        distance, distance_text, duration_text, duration = get_distance(api_key, address, place_address)
        distances.append((place_address, distance, distance_text, duration_text, duration, result))

    distances.sort(key=lambda x: x[4])  # Sort by duration

    place_details = []
    for place in distances:
        address, distance, distance_text, duration_text, duration, details = place
        place_id = details["place_id"]
        details_extracted = get_place_details(api_key, place_id)

        place_info = {
            "name": details_extracted["name"],
            "address": address,
            "distance": distance_text,
            "duration": duration_text,
            "open_now": details_extracted["open_now"],
            "closing_time": details_extracted["closing_time"],
            "photo_url": details_extracted["photo_url"],
        }
        place_details.append(place_info)

    data = {"category": category, "places": place_details}
    return HttpResponse(json.dumps(data, indent=4), content_type="application/json")


@method_decorator(csrf_exempt, name="dispatch")
@login_required
@api_view(["POST"])
def upload_doc(request, report_id: int):
    """Uploads document for already created report."""
    file = request.FILES.get("file")
    report = get_object_or_404(models.Report, user=request.user, pk=report_id)
    filename = report.file_name
    logger.info("File provided. uploading")
    logger.info("Trying to upload %s of size: %d", filename, file.size)
    url = storage.put_file(settings.REPORT_BUCKET, filename, file)
    logger.info("Generating summary.")
    tasks.summary.delay(filename, request.user.id, url)
    report.update(has_report=True)
    return Response({"details": "Report created successfully"}, status=200)


class CollaboratorList(views.APIView):
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    authentication_classes = [SessionAuthentication, BasicAuthentication]

    def get(self, request: HttpRequest, report_id: int):
        collaborators = Collaborators.objects.filter(report_id=report_id)
        serializer = CollaboratorSerializer(collaborators, many=True)
        return Response(serializer.data)

    def post(self, request, report_id: int):
        if "report" not in request.data:
            request.data["report"] = report_id

        serializer = CollaboratorSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()

            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ShareReport(views.APIView):
    def post(self, request, report_id: int):
        if "report" not in request.data:
            request.data["report"] = report_id

        email = request.data.get("email")
        if not email:
            return Response({"error": "Email is required"}, status=status.HTTP_400_BAD_REQUEST)

        request_report_id = request.data.get("report_id", report_id)
        report = get_object_or_404(Report, id=request_report_id)

        user = None
        try:
            user = CustomUser.objects.get(email=email)
            message = f"Link shared with existing user: {user.first_name} {user.last_name}"
        except CustomUser.DoesNotExist:
            message = "Invitation link sent to new user"

        send_invitation_email(email, report, user)
        response_data = {"message": message, "report": ReportSerializer(report).data}

        return Response(response_data, status=status.HTTP_200_OK)
    

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.shortcuts import get_object_or_404
from .models import Report, ReportState
from .serializers import ReportSerializer

class DeleteReportView(APIView):

    def delete(self, request, report_id):
        report = get_report_data_or_404(report_id, request.user)
        report.delete()
        return Response({'message': 'Report deleted successfully'}, status=status.HTTP_200_OK)

from django.db import transaction
from django.shortcuts import get_object_or_404
from rest_framework.views import APIView
from rest_framework.response import Response
from .models import Report, ReportState

class UpdateReportStateView(APIView):

    @transaction.atomic
    def post(self, request, report_id, action):
        report = get_object_or_404(Report, id=report_id)

        # Validate action
        if action not in ['promote', 'demote']:
            return Response({'error': 'Invalid action'}, status=400)

        # # Refresh to ensure latest state is checked (Concurrency safety)
        # report.refresh_from_db()

        # Handle 'promote' action
        if action == 'promote':
            if report.state == ReportState.CONSIDERING:
                report.state = ReportState.VIEWED
            elif report.state == ReportState.VIEWED:
                report.state = ReportState.AWAITING_DETAILS
            elif report.state == ReportState.AWAITING_DETAILS:
                report.state = ReportState.SHORTLISTED
            elif report.state == ReportState.SHORTLISTED:
                return Response({'message': 'Report is already shortlisted. Cannot be promoted further'}, status=200)
        

        # Handle 'demote' action
        elif action == 'demote':
            if report.state == ReportState.SHORTLISTED:
                report.state = ReportState.AWAITING_DETAILS
            elif report.state == ReportState.AWAITING_DETAILS:
                report.state = ReportState.VIEWED
            elif report.state == ReportState.VIEWED:
                report.state = ReportState.CONSIDERING
            elif report.state == ReportState.CONSIDERING:
                return Response({'message': 'Report is already in considering state. Cannot be demoted further'}, status=200)

        # Save state if changed
        report.save()
        return Response({'message': f'Report state updated to {report.state.value}'}, status=200)
