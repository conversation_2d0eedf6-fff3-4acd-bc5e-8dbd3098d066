from django.urls import path

from . import views

app_name = "reports"
urlpatterns = [
    path("", views.home, name="home"),
    path("upload/", views.upload, name="upload"),
    path("view/<filename>", views.view, name="view"),
    path("places", views.places, name="places"),
    path("places/<str:category>", views.places_view, name="places_view"),
    path("place_details", views.place_details, name="place_details"),
    path("reports/", views.ReportList.as_view(), name="report_list"),
    path("reports/<int:report_id>", views.ReportList.as_view(), name="report_detail"),
    path("reports/<int:report_id>/upload", views.upload_doc, name="report_upload"),
    path("issues/<int:report_id>", views.IssueList.as_view()),
    path("preferences/<int:report_id>", views.PreferencesList.as_view()),
    path("compare", views.ComparePreferences.as_view()),
    path("fit_score", views.FitScore.as_view()),
    path("collaborators/<int:report_id>", views.CollaboratorList.as_view()),
    path("report_pdf/<int:report_id>", views.ReportPDF.as_view()),
    path("image_urls/<int:report_id>", views.view_image_urls.as_view(), name="image_urls"),
    path("share/<int:report_id>", views.ShareReport.as_view(), name="report_share"),
    path('reports/<int:report_id>/delete/', views.DeleteReportView.as_view(), name='delete_report'),
    path('reports/<int:report_id>/<str:action>/', views.UpdateReportStateView.as_view(), name='update_report_state'),
    path("room_details/<int:report_id>", views.room_details.as_view(), name="room_details"),
    path("image_details/<int:report_id>", views.image_details.as_view(), name="image_details"),
    path("room_details_updated/<int:report_id>", views.room_details_updated.as_view(), name="room_details_updated"),
]
