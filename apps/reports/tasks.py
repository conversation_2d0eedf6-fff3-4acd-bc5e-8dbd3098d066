import json
import logging
import math
import os
import re
import time
from threading import Semaphore
from collections import defaultdict
from pinecone import Pinecone
import requests
import tiktoken
from functools import wraps  # Add this import at the top with other imports
import pickle
from hashlib import md5
from apify_client import ApifyClient
from celery import shared_task, chain
from celery.exceptions import MaxRetriesExceededError
from django.conf import settings
from django.core.cache import cache
from django.db import connection

import openai
from openai import BadRequestError, OpenAI, AzureOpenAI
from azure.ai.formrecognizer import DocumentAnalysisClient
from azure.core.credentials import AzureKeyCredential
from langchain.schema import Document
from langchain.text_splitter import TokenTextSplitter
from langchain_community.vectorstores import Pinecone as PineconeCommunity
from langchain_openai import OpenAIEmbeddings, AzureOpenAIEmbeddings

from apps.chat.models import Chat, ChatMessage, MessageTypes
from homescore.celery import app
from . import utils
from .models import Images, Issue, Preferences, Report, ImageFeature, RoomSummary

# Initialization
logger = logging.getLogger(__file__)
storage: utils.Supabase | None = utils.storage

semaphore = Semaphore(5)

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

# List of functions that process document content
doc_processing_functions = {
    "get_text",
    "get_index_summary",
    "truncate_input"
}

# Initialize Azure OpenAI client
client = AzureOpenAI(
    api_key=settings.AZURE_OPENAI_API_KEY,
    api_version=settings.AZURE_OPENAI_API_VERSION,
    azure_endpoint = settings.AZURE_OPENAI_API_ENDPOINT
    )
def generate_cache_key(func_name, *args, **kwargs):
    key_elements = [func_name]
    for arg in args:
        key_elements.append(str(arg))
    for k, v in kwargs.items():
        key_elements.append(f"{k}={v}")
    return md5("-".join(key_elements).encode("utf-8")).hexdigest()

def cached_with_logging(timeout=60*60*24*7):  # 7 days default
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            logger.info(f"Entering {func.__name__}")
            
            cache_key = None  # Initialize cache_key to None
            # Check if function is one of the doc_processing_functions
            if func.__name__ in doc_processing_functions:
                # Extract the report or filename from args or kwargs
                report = None
                if "report" in kwargs:
                    report = kwargs["report"]
                elif "filename" in kwargs:
                    filename = kwargs["filename"]
                    report = Report.objects.filter(file_name=filename).first()
                else:
                    for arg in args:
                        if isinstance(arg, Report):
                            report = arg
                            break
                        elif isinstance(arg, str):
                            filename = arg
                            report = Report.objects.filter(file_name=filename).first()
                            break

                # If report is found and the name is "Sample Report", use consistent cache key
                if report and report.name == "Sample Report":
                    cache_key = f"sample_report_cache_{func.__name__}"
                    logger.info(f"Using sample report cache key for {func.__name__}: {cache_key}")
                
                #If report is found and the name is not "Sample Report", generate cache key using the address value of the report which can be found in the name of the report object
                elif report and report.name != "Sample Report":
                    cache_key = generate_cache_key(func.__name__, report.name)
                    logger.info(f"Generated cache key for {func.__name__} with report name: {cache_key}")
            else:
                cache_key = generate_cache_key(func.__name__, *args, **kwargs)
            
            # Log the cache key
            logger.debug(f"Generated cache key for {func.__name__}: {cache_key}")
            
            # Proceed with caching logic
            if cache_key is None:
                logger.info(f"Skipping cache for {func.__name__} - no valid cache key")
                return func(*args, **kwargs)

            # Try to get from cache
            cached_value = cache.get(cache_key)
            if cached_value is not None:
                logger.info(f"Cache HIT for {func.__name__} with key {cache_key}")
                try:
                    result = pickle.loads(cached_value)
                    logger.info(f"Returning cached result for {func.__name__}")
                    return result
                except (pickle.UnpicklingError, TypeError) as e:
                    logger.info(f"Failed to unpickle cached value for {func.__name__}: {e}")

            logger.info(f"Cache MISS for {func.__name__} with key {cache_key}")
            result = func(*args, **kwargs)

            try:
                pickled_result = pickle.dumps(result)
                cache.set(cache_key, pickled_result, timeout)
                logger.info(f"Cached result for {func.__name__} with key {cache_key}")
            except (pickle.PicklingError, TypeError) as e:
                logger.info(f"Failed to pickle result for {func.__name__}: {e}")

            return result
        return wrapper
    return decorator

# Cache decorator that uses django db cache
def cached(func: callable):
    def wrapper(*args, **kwargs):
        key = md5(f"{func.__name__}-{args}-{kwargs}".encode("utf-8")).hexdigest()
        value = cache.get(key)
        if value is None:
            logger.info(f"Cache miss for key: {key}. Calling {func.__name__}")
            value = func(*args, **kwargs)
            cache.set(key, value, 60 * 60 * 24)
        else:
            logger.info(f"Cache hit for key: {key}. Fetching the {func.__name__} from cache.")
        return value

    return wrapper


def truncate_for_logging(content, max_length=200):
    """Truncate content for logging purposes."""
    if isinstance(content, str):
        return content[:max_length] + '...' if len(content) > max_length else content
    elif isinstance(content, dict):
        return str(content)[:max_length] + '...' if len(str(content)) > max_length else str(content)
    elif isinstance(content, list):
        return str(content)[:max_length] + '...' if len(str(content)) > max_length else str(content)
    else:
        return str(content)[:max_length] + '...' if len(str(content)) > max_length else str(content)

@shared_task
@cached
def get_llm_response(model, messages, max_tokens=None, *args, **kwargs):

    logger.info("Executing get_llm_response")
    if max_tokens is None:
        max_tokens = settings.OPENAI_MAX_TOKENS
    response = ""
    
    # Truncate messages for logging
    truncated_messages = []
    for msg in messages:
        content = msg.get('content', '')
        truncated_content = truncate_for_logging(content)
        truncated_messages.append({**msg, 'content': truncated_content})
    
    logger.info(f"Sending request to OpenAI with model: {model}, messages: {truncated_messages}, max_tokens: {max_tokens}")
    
    for i in range(10):
        try:
            response = client.chat.completions.create(
                *args, model=model, messages=messages, max_tokens=max_tokens, **kwargs
            )
            
            response_dump = response.model_dump()
            response = response_dump["choices"][0]["message"]
            logger.info(f"Processed response_dump to response: {truncate_for_logging(response, 300)}")
        except Exception as e:
            message = f"API ERROR, while processing page. Retrying: {i}. Error: {str(e)}"
            logger.info(message)
            raise ValueError(message) from e
        
        # If we reach here, we got a successful response
        break
    
    if isinstance(response, dict):
        logger.info(f"Response is a dictionary: {response}")
        try:
            # Check for tool_calls first
            if "tool_calls" in response and response["tool_calls"]:
                logger.info("Found tool_calls in response")
                
                tool_args = response["tool_calls"][0].get("function", {}).get("arguments")
                if not tool_args:
                    logger.warning("Empty tool call arguments received")
                    return "No tool arguments provided"
                return tool_args
                
            # Check for legacy function_call format
            elif "function_call" in response and response["function_call"] is not None:
                func_args = response["function_call"].get("arguments")
                logger.info("Found function_call in response")
                
                if not func_args:
                    logger.warning("Empty function call arguments received")
                    return "No function arguments provided"
                return func_args
                
            else:
                logger.info("No function call or tool calls found in response")
                
        except Exception as e:
            logger.error(f"Error processing function/tool calls: {str(e)}")
            raise
            
    else:
        logger.info(f"Response is not a dictionary: {response}")
        
        if isinstance(response, dict):
            content = response.get("content")
        else:
            content = str(response)

        if not content:
            logger.warning("Empty content received in response")
            return "No content available"
        return content

@cached
def truncate_input(input_text, model=settings.OPENAI_MODEL, token_limit=3000):
    """
    This function takes an input text and a token limit, and truncates the input text to fit within the token limit.
    """
    encoding = tiktoken.encoding_for_model(model)
    tokens = encoding.encode(input_text)
    print(len(tokens), "tokens present")
    if len(tokens) > token_limit:
        tokens = tokens[:token_limit]
        input_text = encoding.decode(tokens)
    return input_text


logger = logging.getLogger(__file__)

storage: utils.Supabase | None = utils.storage

@shared_task
@cached_with_logging(timeout=60*60*24*7)
def get_text(filename, url, report):
    client = DocumentAnalysisClient(
        endpoint=os.getenv("FR_ENDPOINT"), credential=AzureKeyCredential(os.getenv("FR_KEY"))
    )
    report.update(summary_status="Analyzing document")
    logger.info("Analyzing document at %s", url)
    poller = client.begin_analyze_document_from_url("prebuilt-read", url, pages="1-100")
    result = poller.result()

    pages = []
    docs = []
    logger.info("Processing text extraction for filename: %s", filename)
    for page_num, page in enumerate(result.pages):
        logger.info("----Analyzing layout from page #{}----".format(page.page_number))
        page_content = f"page {page_num+1}:"
        for _, line in enumerate(page.lines):
            page_content += line.content
        pages.append(page_content)
        docs.append(
            Document(
                page_content=f"page: {page_num}\n" + page_content,
                metadata={
                    "filename": filename,
                    "source": f"pg: {page_num}",
                },
            )
        )
        logger.info("Doc %d metadata: %s", page_num, docs[-1].metadata)
    splitter = TokenTextSplitter(chunk_size=1000, chunk_overlap=250)
    docs = splitter.split_documents(docs)
    return pages, docs


SUMMARY = """Imagine you are a home inspection expert.
Your goal is to help prospective home buyer understand issues with the house
they are buying and either negotiate price down or walk away from the deal.
List top 10 issues you find as a bulleted list. For each of the issue,
provide some useful context for first time home buyer.
Also provide estimate of cost to fix the issue.
Attribute information to specific pages.
Make information accessible to first time buyers.
Create a numbered list. Use less than 50 words for each issue.
Select top 10 issues based on highest cost to fix them.
Ignore issue if the report does not specify specific problems.
Use the following format
-------
Issue: Short issue description
Category: Roofing|Electrical|Plumbing|HVAC|Mold|Pest|Water damage|Interior|Radon|Exterior|Structural
Details: Verbatim details about the issue found in the report.
Source: cite the source or page where the issue was described using format [pg XX]
Context:  background information for the issue. Include common problems
Recommendation: Next steps to address the issue
Estimated Cost: $XXXX-$YYYY
Urgency: Low|Med|High
-------
"""

functions = [
    {
        "name": "issue_description",
        "description": "Top issues mentioned in the report. These are issues that help prospective home buyers understand the condition of the house. They can use these issues to negotiate the price down or walk away from the deal. Ignore issues that are minor or don't materially change the price of the house.",
        "parameters": {
            "type": "object",
            "properties": {
                "issues": {
                    "description": "Top issues mentioned in the report.",
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "issue": {
                                "type": "string",
                                "description": "Short name (less than 3 words) name for the issue",
                            },
                            "category": {
                                "type": "string",
                                "description": "Category of the issue. It should be among one of the following: ",
                                "enum": [
                                    "Roofing",
                                    "Electrical",
                                    "Plumbing",
                                    "HVAC",
                                    "Mold",
                                    "Pest",
                                    "Water damage",
                                    "Interior",
                                    "Radon",
                                    "Exterior",
                                    "Structural",
                                ],
                            },
                            "source": {
                                "type": "string",
                                "description": "cite the source or page where the issue was described using format [pg XX]",
                            },
                            "context": {
                                "type": "string",
                                "description": "background information for the issue. Include common problems. Verbatim text from the report. Make information accessible for novice home buyer",
                            },
                            "recommendation": {
                                "type": "string",
                                "description": "Next steps to address the issue. Make information accessible for novice home buyer. Recommendations include type of professional to reach out and also how soon to fix the problems",
                            },
                            "cost_estimate_low": {
                                "type": "integer",
                                "description": "Low range of the cost estimate to fix the issue",
                            },
                            "cost_estimate_high": {
                                "type": "integer",
                                "description": "High range of the cost estimate to fix the issue",
                            },
                            "urgency": {
                                "type": "string",
                                "description": "Urgency of fixing the issue being described",
                                "enum": ["Low", "Medium", "High"],
                            },
                        },
                    },
                },
            },
        },
    }
]


@cached_with_logging(timeout=60*60*24*7)
def get_index_summary(user_id, filename, summary_prompt) -> str:
    MAJOR_ISSUES = [
        "Foundation:  Look for visible cracks on the exterior and interior of the home, doors that jam, or uneven floors. Foundation problems can lead to major structural issues.",
        "Roofing : A worn-out or damaged roof can lead to leaks and water damage inside the home.",
        "Electrical: Outdated or faulty wiring can be a fire hazard. Look for flickering lights, burning smells, or outdated fuse boxes.",
        "Plumbing: Leaky faucets, poor water pressure, or water stains might indicate plumbing issues.",
        "Heating and HVAC: An outdated or malfunctioning HVAC system can result in inefficient heating or cooling.",
        "Mildew and mold: Mold can lead to health issues and indicates moisture problems in the home.",
        "Pest or rodent infestation: Signs of pests like termites can indicate damage to the structure of the home.",
        "Water damage: Water stains on walls, ceilings, or a musty smell can indicate past or current leaks.",
        "Pool insulation:  Poorly insulated homes can lead to high utility bills and uncomfortable living conditions.",
        "Windows and doors: Old or improperly sealed windows and doors can lead to energy inefficiencies and security issues.",
        "Interior: Look out for lead based paint, asbestos presence outdated electrical outlets",
        "Radon issue: Radon is a naturally occurring radioactive gas that can seep into homes from the ground. It's colorless, odorless, and tasteless, making it hard to detect without special equipment.",
        "Structural issues: Structural issues in a home can arise from various causes, such as foundation settlement and movement, cracks in walls or ceilings, leaning walls, uneven floors, deteriorating wood",
    ]
    logger.info("Entering get_index_summary with user_id: %s, filename: %s", user_id, filename)
    report = Report.objects.filter(user_id=user_id, file_name=filename).first()
    logger.info("Fetched report: %s", report)
    if report:
        logger.info("Initializing Pinecone with API key and environment.")
        pc = Pinecone(api_key=settings.PINECONE_API_KEY, environment=settings.PINECONE_ENV)
        embeddings = AzureOpenAIEmbeddings(model=settings.AZURE_OPENAI_EMBEDDINGS_MODEL)
        doc_search = PineconeCommunity.from_existing_index(settings.PINECONE_INDEX, embeddings)

        similar_docs = []
        
        # For deduplication
        seen_content = set()

        for major_issue in MAJOR_ISSUES:
            logger.info("Looking for issue: %s", major_issue)
            logger.info("Querying Pinecone with filter filename: %s", report.file_name)
            
            docs = doc_search.similarity_search_with_score(
                major_issue, 
                k=settings.TOPK,
                filter={"filename": report.file_name}
            )
            
            # After retrieval, log even more context
            logger.info("Pinecone returned %d docs", len(docs))
            keywords = major_issue.lower().split()[:3]
            logger.info("Keywords considered: %s", keywords)


            filtered_docs = []
            for doc, score in docs:
                doc_lower = doc.page_content.lower()
                pass_threshold = score > settings.SIMILARITY_THRESHOLD
                pass_seen = doc.page_content not in seen_content
                pass_keywords = any(keyword in doc_lower for keyword in keywords)

                logger.info(
                    "Doc Score: %.3f | Pass Threshold: %s | Pass Seen: %s | Pass Keywords: %s | Content (truncated): %r",
                    score, pass_threshold, pass_seen, pass_keywords, doc.page_content[:200]
                )

                if pass_threshold and pass_seen and pass_keywords:
                    filtered_docs.append((doc, score))
            logger.info("Filtered down to %d documents after applying conditions", len(filtered_docs))
            
            for doc, score in filtered_docs:
                logger.info(
                    "Filtered Doc - Score: %.3f, Content (truncated): %r",
                    score, doc.page_content[:200]
                )
                seen_content.add(doc.page_content)

            # Extend similar_docs with the newly filtered_docs
            similar_docs.extend(filtered_docs)
            logger.info("Extended similar_docs. Current count: %d", len(similar_docs))

        messages = []
        if similar_docs:
            # Sort by similarity score
            similar_docs.sort(key=lambda x: x[1], reverse=True)

            logger.info("Building context from similar_docs...")
            context = "Use additional context below to answer the question: \n"
            context += "\n".join([
                f"page: {doc.metadata.get('source')} (score: {score:.2f})\n{doc.page_content}"
                for doc, score in similar_docs
            ])
            context = truncate_input(context, token_limit=12000)
            logger.info("Context length after truncation: %d characters", len(context))

            messages.extend(
                [{"role": "system", "content": summary_prompt}, {"role": "user", "content": context}]
            )
            logger.info("Messages: %.300s", messages)
        else:
            logger.info("No similar_docs found. Messages will remain empty.")

        MODEL = settings.OPENAI_MODEL
        openai_response = get_llm_response(
            model=MODEL,
            messages=messages,
            functions=functions,
            function_call="auto",
            temperature=0.0,
        )
        return openai_response
    else:
        logger.info("No report found for user_id: %s and filename: %s", user_id, filename)
        return None


def parse_and_store_json_summary(report: Report, summary: str) -> str:
    logger.info("Interpreting summary as JSON")
    
    if not summary:
        logger.error("Summary is empty. Cannot parse JSON.")
        raise ValueError("Summary is empty. Cannot parse JSON.")
    
    try:
        parsed_summary = json.loads(summary)
        logger.info("Parsed Summary: %s", parsed_summary)
    except json.JSONDecodeError as e:
        logger.error("JSON decoding error: %s", e)
        raise ValueError("Invalid JSON format in summary.") from e
    
    if "issues" in parsed_summary:
        summary = parsed_summary["issues"]
        logger.info("Issues in parsed summary: %s", summary)
    
    result = []
    if summary:
        Issue.objects.filter(report_id=report.id).delete()
        for issue in summary:
            issue["report"] = report
            result.append(
                f"{issue.get('issue')} [{issue.get('source')}] ({issue.get('urgency')} Urgency)\n"
                f"{issue.get('category')}\n"
                f"{issue.get('context')}\n"
                f"{issue.get('recommendation')}\n"
                f"Estimate: {issue.get('cost_estimate_low')}-{issue.get('cost_estimate_high')}"
            )
            issue_obj, _ = Issue.objects.update_or_create(**issue)
            issue_obj.save()
        return "\n\n".join(result)
    else:
        logger.error("No issues found in parsed summary.")
        raise ValueError("No issues found in parsed summary.")


@app.task(queue="report_task")
def homescore(report_id: int):
    urgency_to_score = {"High": 1.0, "Medium": 0.25, "Low": 0.0}
    issues = Issue.objects.filter(report_id=report_id)
    # Current score
    report_score = sum([urgency_to_score[issue.urgency] for issue in issues])
    # compute home score
    cursor = connection.cursor()
    cursor.execute(
        f"""select
            percent_rank({report_score}) within group (order by score)
            from
            (
                select report_id, sum(case
                    when urgency='High' then 1
                    when urgency='Medium' then 0.25
                    when urgency='Low' then 0.0
                    else 0
                    end) as score
                from
                    reports_issue
                    group by report_id
                    order by score
            ) as tscore"""
    )
    perc_score = cursor.fetchone()[0]
    print(report_score, perc_score)
    score = int(100 * (1.0 - perc_score))
    return min(95, max(5, score))


@app.task(rate_limit="1/m", queue="report_task")
def upgrade_homescore():
    for report in Report.objects.all():
        if report.homescore_version != settings.HOMESCORE_VERSION or report.homescore == 0:
            logger.info(
                "Upgrading homescore for %s. Found %d, expecting: %d",
                report.file_name,
                report.homescore_version,
                settings.HOMESCORE_VERSION,
            )
            score = homescore(report.id)
            report.homescore = score
            report.homescore_version = settings.HOMESCORE_VERSION
            report.save(update_fields=["homescore", "homescore_version"])
        else:
            logger.info(
                "Skipping homescore for %s. Found %d (%f), expecting: %d",
                report.file_name,
                report.homescore_version,
                report.homescore,
                settings.HOMESCORE_VERSION,
            )


def update_report(report: Report, **kwargs):
    Report.objects.filter(id=report.id).update(**kwargs)

def wait_for_docs(filename: str, embeddings, max_retries=5, wait_seconds=4):
    doc_search = PineconeCommunity.from_existing_index(settings.PINECONE_INDEX, embeddings)
    for attempt in range(max_retries):
        # Try querying for any known keyword just to see if docs are present
        docs = doc_search.similarity_search_with_score("inspection", k=3, filter={"filename": filename})
        if docs:
            logger.info("Found documents for filename %s after %d attempts", filename, attempt + 1)
            return True
        else:
            logger.info("No documents found for filename %s yet. Retrying in %d seconds...", filename, wait_seconds)
            time.sleep(wait_seconds)
    logger.warning("No documents found for filename %s after %d retries.", filename, max_retries)
    return False


@app.task(rate_limit="1/m", queue="report_task")
def summary(filename, user, url=None):
    start_time = time.time()
    logger.info("Starting summary task at: {}".format(start_time))
    
    SYSTEM = (
        "My goal is to help you understand the issues found in the report."
        "You can use this information to understand the report better, negotiate price down, find appropriate professionals."
        "For each of the issue, I will provide some useful context, cost estimate and recommendation."
        "I will keep the response short, but you can ask me to elaborate."
    )

    SUMMARY_PREFIX = "The summary of the report is: \n"
    logger.info("Getting summary for %s", filename)
    report = Report.objects.get(user=user, file_name=filename)
    if not url:
        logger.info("Url not provided. Fetching it.")
        try:
            url = storage.get_url(settings.REPORT_BUCKET, filename)
            logger.info("File stored at signed-url: %s", url)
        except Exception as e:
            logger.warning("Unable to get summary")
            report.update(summary_status="Done")
            raise ValueError("Unable to get summary")

    # Extract text
    report.update(summary_status="Extracting text...")
    text_extraction_start = time.time()
    pages, docs = get_text(filename, url, report)
    logger.info("Text extraction took: {:.2f} seconds for {} pages".format(
                time.time() - text_extraction_start, len(pages)))
    report.update(summary_status=f"Extracted ({len(pages)} pages)...")
    embeddings = AzureOpenAIEmbeddings(model=settings.AZURE_OPENAI_EMBEDDINGS_MODEL)
    index_name = settings.PINECONE_INDEX
    upload_start = time.time()
    try:
        for i, doc in enumerate(docs):
            logger.info("Doc %d metadata: %s", i, doc.metadata)
        upload_documents_to_pinecone(docs, index_name, embeddings)
        logger.info("Pinecone upload took: {:.2f} seconds".format(time.time() - upload_start))
    except Exception as e:
        logger.error("Failed to insert documents into Pinecone: %s", e)
        report.update(summary_status="Failed: Pinecone insertion error")
        raise
    
    
    # Wait until documents are available in Pinecone
    if not (report.name == "Sample Report" and cache.get("sample_report_cache_get_index_summary")):
        if not wait_for_docs(filename, embeddings):
            logger.info(
                "No documents found for filename %s in Pinecone after waiting. "
                "Continuing without fresh indexing, may serve stale cached data.",
                filename
            )
    report.update(summary_status=f"Generating AI summary ({len(pages)} pages)...")
    summary_start = time.time()
    summary = get_index_summary(user_id=user, filename=filename, summary_prompt=SUMMARY)
    logger.info("Summary generation took: {:.2f} seconds".format(time.time() - summary_start))
    logger.info("Get Index Summary: %s", summary)
    report.update(summary=summary)
    report.update(summary_version=settings.SUMMARY_VERSION)
    parse_and_store_json_summary(report, summary)
    report.update(summary_status="Done")
    logger.info("Total task time: {:.2f} seconds".format(time.time() - start_time))
    


def upload_documents_to_pinecone(docs, index_name, embeddings):
    
    start_time = time.time()
    try:
        pc = Pinecone(api_key=settings.PINECONE_API_KEY, environment=settings.PINECONE_ENV)
        init_time = time.time()
        logger.info("Pinecone init took: {:.2f} seconds".format(init_time - start_time))
        
        doc_search = PineconeCommunity.from_existing_index(index_name, embeddings)
        index_time = time.time()
        logger.info("Index connection took: {:.2f} seconds".format(index_time - init_time))
        
        logger.info("Starting upload of {} documents".format(len(docs)))
        doc_search.add_documents(docs)
        upload_time = time.time()
        logger.info("Document upload completed in: {:.2f} seconds".format(
            upload_time - index_time))
            
        return doc_search
        
    except Exception as e:
        elapsed = time.time() - start_time
        logger.error("Upload failed after {:.2f} seconds: {}".format(
            elapsed, str(e)))
        raise

@app.task
def create_chat(filename, user):
    report = Report.objects.get(user=user, file_name=filename)
    chat, created = Chat.objects.update_or_create(user_id=user, name=filename, file_name=filename)
    logger.info(f"Chat created? {created} {chat}")
    report.update(chat_status="Done")


@app.task(queue="report_task")
def upgrade_summaries():
    for report in Report.objects.all().order_by("-id"):
        if report.summary_version != settings.SUMMARY_VERSION:
            logger.info("Found summary version:%d, needed: %d", report.summary_version, settings.SUMMARY_VERSION)
            logger.info("Recreating summary for %s, %s", report.user, report.file_name)
            app.send_task("apps.reports.tasks.summary", [report.file_name, report.user.id])
        else:
            logger.info("Summary upto date.Skipping report for %s, %s", report.user, report.file_name)



def camel_case_split(str):
    return re.findall(r"[A-Z](?:[a-z]+|[A-Z]*(?=[A-Z]|$))", str)


@cached
def get_cached_home_listing(address: str):
    logger.debug(f"Executing get_cached_home_listing with address: {address}")
    results = []
    try:
        # Initialize the ApifyClient with your API token
        logger.info("Calling scraper.. with %s", address)
        # Get ZPID
        url = settings.ZILLOW_HOME_SEARCH_API
        querystring = {"location": address}
        headers = {"X-RapidAPI-Key": settings.APIFY_KEY, "X-RapidAPI-Host": "zillow69.p.rapidapi.com"}
        response = requests.get(url, headers=headers, params=querystring)
        logger.info("Got back:%s", response.json())

        response = response.json()
        if isinstance(response, list):
            response = response[0]
        zpid = response.get("zpid")
        logger.info("Using zpid:%s", zpid)
        if zpid:
            # Get Details
            time.sleep(0.5)
            url = settings.ZILLOW_PROPERTY_DETAILS_API
            querystring = {"zpid": zpid}
            response = requests.get(url, headers=headers, params=querystring)
            results = response.text
            logger.info("Using zpid: %d, Got back:%s", zpid, response.text)
    except Exception as e:
        message = "Error while calling scraper: %s"
        logger.error(message, e)
        raise ValueError(message % (e))
    return results

@app.task(rate_limit="1/s", queue="image_task")
def get_image_details(report_id: int, user):
    """
    Process image details for a home report.
    Args:
        report_id (int): Report ID
        user: User ID
    """
    logger.info(f"Processing image details for report_id: {report_id}")

    try:
        # Logging before fetching the report
        logger.debug("Fetching the report from the database.")
        report = Report.objects.get(user=user, id=report_id)
        logger.debug(f"Fetched report: {report.id}")

        # Update image_details_status
        report.update(image_details_status="Fetching the report from the database.")

        # Extract zpid from report details
        logger.debug("Extracting zpid from report details.")
        
        try:
            report_details = json.loads(report.details)
            zpid = report_details.get("zpid")
            logger.debug(f"Extracted zpid: {zpid}")
        except json.JSONDecodeError as e:
            logger.error(f"JSON decoding error: {e}")
            report.update(image_details_status=f"JSON decoding error: {e}")
            return

        # Prepare API call details
        photos_url = settings.ZILLOW_PROPERTY_PHOTOS_API
        querystring = {"zpid": zpid}
        headers = {
            "X-RapidAPI-Key": settings.APIFY_KEY,
            "X-RapidAPI-Host": "zillow69.p.rapidapi.com"
        }

        # Logging before making API call
        logger.info(f"Making API call to fetch photos for zpid: {zpid}")
        report.update(image_details_status="Making API call to fetch photos for zpid: {zpid}")
        try:
            response_photos = requests.get(photos_url, headers=headers, params=querystring)
            response_photos.raise_for_status()  # Raise an exception for 4xx or 5xx status codes
            logger.info(f"API call response status: {response_photos.status_code}")
            report.update(image_details_status=f"API call response status: {response_photos.status_code}")
            photo_data = response_photos.json()
            logger.info(f"API returned photo data: {len(photo_data.get('photos', []))} photos")
            report.update(image_details_status=f"API returned photo data: {len(photo_data.get('photos', []))} photos")
        except requests.exceptions.RequestException as e:
            logger.error(f"Error occurred while fetching photos: {e}")
            report.update(image_details_status=f"Error occurred while fetching photos: {e}")
            raise  # Re-raise the exception to propagate it further

        format = "webp"
        image_urls = [
            image["url"]
            for photo in photo_data["photos"]
            for image in photo["mixedSources"].get(format, [])
            if image["width"] == 192
        ]
        logger.info(f"Filtered {len(image_urls)} images with width 192.")
        report.update(image_details_status=f"Filtered {len(image_urls)} images with width 192.")

        # Logging before getting descriptions
        logger.info("Fetching descriptions for image URLs.")
        report.update(image_details_status="Fetching descriptions for image URLs.")
        try:
            descriptions = get_all_descriptions(image_urls)
            logger.debug(f"Image descriptions for {len(descriptions)} fetched")
            report.update(image_details_status=f"Fetched descriptions for {len(descriptions)} images.")
        except Exception as e:
            logger.error(f"Error occurred while fetching descriptions: {e}")
            report.update(image_details_status=f"Error occurred while fetching descriptions: {e}")
            raise  # Re-raise the exception to propagate it further

        # Logging before saving descriptions to the database
        for url, desc in zip(image_urls, descriptions):
            logger.debug(f"Saving description for image URL: {url}")
            try:
                save_to_database(report, url, desc)
            except Exception as e:
                logger.error(f"Error occurred while saving description for image URL {url}: {e}")
                report.update(image_details_status=f"Error occurred while saving description for image URL {url}: {e}")
                continue  # Continue to the next iteration instead of re-raising the exception

    except json.JSONDecodeError as e:
        logger.error(f"JSON decoding error: {e}")
        report.update(image_details_status=f"JSON decoding error: {e}")
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}")
        report.update(image_details_status=f"An unexpected error occurred: {e}")

# Synchronous function to process all image descriptions
@cached
@app.task(rate_limit="1/s", queue="image_task")
def get_all_descriptions(image_urls):
    logger.info(f"Starting get_all_descriptions for {len(image_urls)} URLs.")
    client = AzureOpenAI(
    api_key=settings.AZURE_OPENAI_API_KEY,  
    api_version=settings.AZURE_OPENAI_API_VERSION,
    azure_endpoint = settings.AZURE_OPENAI_API_ENDPOINT
    )
    
    # Logging before tasks creation
    logger.info(f"Fetching descriptions for {len(image_urls)} URLs.")
    
    descriptions = []
    for url in image_urls:
        descriptions.append(get_image_description(client, url))
    
    return descriptions

# Synchronous function to get the description for each image
@cached
@app.task(queue="image_task")
def get_image_description(client, url):
    logger.info(f"Fetching description for image URL: {url}")
    messages = [
        {
            "role": "user",
            "content": [
                {"type": "text", "text": settings.IMAGE_ANALYSIS_PROMPT},
                {"type": "image_url", "image_url": {"url": url}}
            ]
        }
    ]
    
    image_analysis = None

    try:
        response = client.chat.completions.create(
            model=settings.OPENAI_QUIZ_MODEL,
            messages=messages,
            tools=settings.IMAGE_ANALYSIS_TOOLS,
            tool_choice="required",
            temperature=0
        )

        response_dump = response.model_dump()

        choices = response_dump.get("choices", [])
        if choices:
            message = choices[0].get("message", {})
            tool_calls = message.get("tool_calls", [])
            if tool_calls:
                function = tool_calls[0].get("function", {})
                arguments = function.get("arguments", {})
                if arguments:
                    image_analysis = arguments   

        logger.info(f"Got description for image URL: {url}")
        try:
            return json.loads(image_analysis) if image_analysis else []
        except json.JSONDecodeError:
            logger.error(f"JSON decode error for image URL: {url}")
            return []

    except Exception as e:
        logger.error(f"API Error while processing image URL: {url}. Error: {e}")
        return []

# Synchronous function to save image descriptions to the database
@app.task(queue="image_task")
def save_to_database(report, url, image_description):
    if image_description:
        try:
            feature_dict = image_description.get("room_type", {})
            if feature_dict:
                for room_type, details in feature_dict.items():
                    logger.debug("Processing Room Type: %s started", room_type)
                    
                    # Create or update the Images object
                    try:
                        image_instance, created = Images.objects.update_or_create(
                            image_url=url,
                            report=report,
                            defaults={
                                "room_type": room_type.lower(),
                            }
                        )
                        
                        image_instance.save()
                    except Exception as e:
                        logger.error(f"Failed to store image URL {url}: {e}")
                    
                    
                    # Create or update the ImageFeature object
                    for feature, details in details.items():
                        value = details.get("value")
                        summary = details.get("summary")

                        logger.debug(f"Room Feature: {feature}, Value: {value}, Summary: {summary}")

                        try:
                            feature = feature.lower().replace(" ", "_")
                            image_feature, _ = ImageFeature.objects.update_or_create(
                                image=image_instance,
                                image_feature_type= feature,
                                defaults={"description": summary, "choice": value.lower()},
                            )
                            image_feature.save()
                        except Exception as e:
                            logger.error(f"Failed to store feature {feature}: {e}")
                            continue
                        
                    logger.info(f"Saved details for room_type: {room_type}, image URL: {url} in the database.")
        except json.JSONDecodeError:
            logger.error(f"JSON decode error for image URL: {url} with description: {image_description}")
    else:
        logger.error(f"Empty description for image URL: {url}")
    
    report.update(image_details_status="Done")
    logger.info(f"All image details for {url} saved to the database.")

@app.task(queue="image_task")
def aggregate_room_features(report_id: int, user):
    """
    Task function to aggregate room features from the Images model for a specific report,
    process the data, and generate a final summary.
    """
    try:
        # Fetch image details from the database based on the report_id and user
        logger.info(f"Fetching image details from database for report_id: {report_id} and user: {user}")
        images = Images.objects.filter(report_id=report_id)

        if not images.exists():
            logger.error(f"No images found for report_id: {report_id}")
            return

        # Aggregating image details into a dictionary
        image_description = defaultdict(lambda: defaultdict(lambda: {"values": set(), "summaries": []}))

        for image in images:
            room_type = image.room_type.name

            # Fetch all features related to the image
            features = ImageFeature.objects.filter(image=image)

            for feature in features:
                image_feature_type = feature.image_feature_type.value
                value = feature.choice
                summary = feature.description

                # Use a set to ensure unique values
                if value and summary:
                    image_description[room_type][image_feature_type]["values"].add(value)
                    image_description[room_type][image_feature_type]["summaries"].append(summary)
                else:
                    logger.error(f"Invalid value or summary for image: {image}, value: {value}, summary: {summary}")
        
    except Exception as e:
        logger.error(f"Error aggregating room features: {e}")
        return
    
    return image_description


# Function to generate the final summary for each room type
@cached
@app.task(rate_limit="1/s", queue="image_task")
def process_feature(feature, feature_details):
    logger.info(f"Processing feature: {feature} started")
    # For all features, combine each value and its corresponding summary
    feature_summary_list = []
    
    for value, summary in zip(feature_details["values"], feature_details["summaries"]):
        # Construct the string that pairs value with the corresponding summary
        feature_summary_list.append(f"{value} {feature} has summary like {summary}.")

    # Join all feature-summary pairs into a single string for LLM input
    combined_summaries = ' '.join(feature_summary_list)

    # Detailed message sent to LLM for generating the final summary
    detailed_message = f"The feature is {feature}:\n{combined_summaries}"

    # Send the detailed message to LLM
    messages = [
        {"role": "system", "content": settings.BY_ROOM_PROMPT},  # Assuming PROMPT is predefined
        {"role": "user", "content": detailed_message}
    ]

    # Generate the summary for the feature using LLM
    response = client.chat.completions.create(
        model=settings.OPENAI_QUIZ_MODEL,  
        messages=messages,
        temperature=0,
    )

    # Extract and format the summary from the LLM response
    response_dump = response.model_dump()
    
    choices = response_dump.get("choices", [])
    if choices:
        message = choices[0].get("message", {})
        content = message.get("content", "")
        if content:
            feature_summary = content.strip()
    return feature_summary

def capitalize_string(s: str) -> str:
    return " ".join(word.capitalize() for word in re.split(r"[-_\s]+", s))

def normalize_string(s: str) -> str:
    return re.sub(r"[-_]+", " ", s).lower()
# Load the sample room data from the JSON file
def get_static_values(room_type, feature_name):
    with open("apps/reports/sample_room_data.json", "r") as file:
        sample_room_data = json.load(file)
        
    feature_name = capitalize_string(feature_name)

    # Normalize room_type to lower case for matching
    room_type_lower = room_type.lower()
    # Create a mapping from lower case room_types to their data
    room_data = None
    for key, value in sample_room_data.items():
        if key.lower() == room_type_lower:
            room_data = value
            break

    if (room_data is None):
        return {}

    for feature in room_data.get("features", []):
        if feature["feature"] == feature_name:
            return {
                "ui-type": feature.get("ui-type", "none"),
                "hero-card": feature.get("hero-card", "false"),
                "small-card": feature.get("small-card", "false")
            }
    return {}

@shared_task(bind=True, max_retries=5, default_retry_delay=10)  # Retry up to 5 times with a 10-second delay
def check_and_generate_room_summary(self, report_id, user):
    try:
        report = Report.objects.get(id=report_id, user=user)
        
        # Check if get_image_details task is done
        if report.image_details_status != "Done":
            logger.info("Image details are not yet processed. Retrying check_and_generate_room_summary.")
            raise self.retry(exc=Exception("Image details not processed yet"))
        
        # Trigger generate_room_summary task
        generate_room_summary.delay(report_id, user)
        
    except MaxRetriesExceededError:
        logger.error("Max retries exceeded for check_and_generate_room_summary task")
    except Exception as e:
        logger.error(f"An unexpected error occurred while checking image details status: {e}")

@cached
@app.task(rate_limit="1/s", queue="image_task")
def process_room(room, features, report):
    logger.info(f"Processing room: {room} started")
    room_normalized = normalize_string(room)
    room_capitalized = capitalize_string(room_normalized)
    
    room_data = {
        "room_type": room_capitalized,
        "features": []
    }
    
    # Initialize a dictionary to hold feature summaries per room
    room_feature_summaries = {}
    
    # Initialize embeddings and connect to existing Pinecone index
    try:
        embeddings = AzureOpenAIEmbeddings(model=settings.AZURE_OPENAI_EMBEDDINGS_MODEL)
        pc = Pinecone(api_key=settings.PINECONE_API_KEY, environment=settings.PINECONE_ENV)
        index_name = settings.PINECONE_INDEX  # Ensure this is set to your existing index name
        
        # Connect to existing index
        doc_search = PineconeCommunity.from_existing_index(index_name, embeddings)
    except Exception as e:
        logger.error(f"Error initializing embeddings or connecting to Pinecone: {e}")
        # Handle the exception appropriately or re-raise if necessary
    
    for feature, feature_details in features.items():
        feature_summary = process_feature(feature, feature_details)
        values = feature_details["values"]
        processed_values = [value.capitalize() for value in values if value is not None]

        if feature_summary:
            static_values = get_static_values(room_capitalized, feature)
            static_values = {k: list(v) if isinstance(v, set) else v for k, v in static_values.items()}
            room_data["features"].append({
                "feature": capitalize_string(feature),
                "summary": feature_summary,
                "values": processed_values,
                **static_values
            })
            report.update(room_summary_status="Storing")

            try:
                # Save individual feature summaries to the database (unchanged)
                room_summary, created = RoomSummary.objects.update_or_create(
                    report=report,
                    room_type=room_normalized,
                    feature_type=feature,
                    defaults={"summary": feature_summary, "values": processed_values}
                )
                room_summary.save()
                logger.info(f"RoomSummary saved: {room_summary}, Created: {created}")

                # Collect feature summaries for aggregation
                if room_normalized not in room_feature_summaries:
                    room_feature_summaries[room_normalized] = []
                room_feature_summaries[room_normalized].append(f"{capitalize_string(feature)}: {feature_summary}")

            except Exception as e:
                logger.error(f"Error saving RoomSummary: {e}")
    
    # Prepare documents for Pinecone

    docs_to_add = []

    for room_type, feature_summaries in room_feature_summaries.items():
        # Combine feature summaries into one string
        room_summary_text = "\n".join(feature_summaries)

        # Create a Document object for the room summary
        docs_to_add.append(Document(
            page_content=room_summary_text,
            metadata={
                "source": str(report.id),
                "filename": report.file_name,
                "room_type": room_type,
                "embedding_type": "room_summary"
            }
        ))

    # Add documents to Pinecone
    try:
        if docs_to_add:
            upload_documents_to_pinecone(docs_to_add, index_name, embeddings)
            logger.info("Documents successfully added to Pinecone")
    except Exception as e:
        logger.error(f"Error adding documents to Pinecone: {e}")

    return room_data

@app.task(rate_limit="1/s", queue="image_task")
def generate_room_summary(report_id: int, user):
    logger.info(f"Generating room summary for report_id: {report_id}")
    report = Report.objects.get(id=report_id, user=user)
    
    # Update status to Started
    report.update(room_summary_status="Started")

    try:
        # Generating the final room details
        image_description = aggregate_room_features(report_id, user)
        room_details = {"images": {}}
        
        # Update status to Processing
        report.update(room_summary_status="Processing")
        
        for room, features in image_description.items():
            room_data = process_room(room, features, report)
            room_details["images"][room_data["room_type"]] = room_data
            report.update(room_summary_status="Done")
        
    except Exception as e:
        #Update status to 0% in case of an error
        report.update(room_summary_status="Failed")
        logger.exception("Error generating room summary: %s", e)
        
    return room_details

@cached
@app.task(queue="report_task")
def get_home_details(filename: str, user):
    report = Report.objects.get(user=user, file_name=filename)
    if not report:
        logging.error("Unable to find report for filename:%s", filename)
        report.update(details_status="Done")
        return
    # Extract text
    report.update(details_status="Extracting details...")
    results = get_cached_home_listing(report.address)
    # Sanitize and truncate results for logging
    if results:
        try:
            results_dict = json.loads(results)
            safe_fields = ['zpid', 'streetAddress', 'zipcode', 'city', 'state']
            safe_results = {k: results_dict.get(k) for k in safe_fields if k in results_dict}
            logger.info(f"Results from get_cached_home_listing: {safe_results}")
        except json.JSONDecodeError:
            logger.info("Results received but could not be parsed as JSON")
    else:
        logger.info("No results received from get_cached_home_listing")

    if not results:
        logger.info("Empty results. Skipping")
        return
    report.update(details=results)
    logger.info('Updating report with details for "%s"', report)
    # logger.info("Details: %s", report.details)

    # Get Image Details task
    logger.info("Extracting details from the image_urls")
    # Trigger the status check task
    chain(
        get_image_details.s(report.id, user),
        generate_room_summary.si(report.id, user)
        ).apply_async()

    return results


@app.task(queue="report_task")
def get_preferences(filename: str, user, fetch_details=True):
    logger.info("Getting homedetails for %s", filename)
    report = Report.objects.get(user=user, file_name=filename)
    report.refresh_from_db()
    details = ""
    if not report.details and fetch_details:
        get_home_details(filename, user)
        report.refresh_from_db()
    # Extract text
    try:
        details_dict = json.loads(report.details)
        # Create text out of json
        details = "\n".join(["%s : %s" % (camel_case_split(key), value) for key, value in details_dict.items()])
    except Exception as e:
        message = "Error while parsing json: %s"
        logger.error("Error while parsing json: %s", e)
        report.update(details_status="Error")
        report.update(room_summary_status="Error")
        raise ValueError(message % (e))

    report.update(details_status="Extracting preferences...")
    logger.info("Trying to extract preferences")

    messages = []
    messages.extend(
        [
            {"role": "system", "content": settings.SCORE_PREFERENCES_PROMPT},
            {"role": "user", "content": "The description of the house is as follows: %s" % (details)},
        ]
    )
    MODEL = settings.OPENAI_MODEL
    summary = get_llm_response(
        model=MODEL,
        messages=messages,
        tools=settings.HOME_PREFERENCES_TOOLS,
        tool_choice="required",
        temperature=0.0,
    )
    logger.info("Extracted preferences: %s", summary)

    preferences = json.loads(summary)
    preferences = preferences.get("preferences")
    if preferences:
        Preferences.objects.filter(report_id=report.id).delete()
        sorted_preferences = sorted(preferences, key=lambda p: p["preference"])
        logger.info("Inserting sorted preferences: %s", sorted_preferences)
        for preference in sorted_preferences:
            preference["report"] = report
            logger.info("Inserting preference: %s", preference)
            record, _ = Preferences.objects.update_or_create(**preference)
            record.save()

    # Index
    report.update(details_status="Indexing public records...")
    embeddings = AzureOpenAIEmbeddings(model=settings.AZURE_OPENAI_EMBEDDINGS_MODEL)
    pc = Pinecone(
        api_key=settings.PINECONE_API_KEY,
        environment=settings.PINECONE_ENV,
    )
    index_name = settings.PINECONE_INDEX
    doc_search = PineconeCommunity.from_existing_index(index_name, embeddings)

    docs = [
        Document(
            page_content=details,
            metadata={
                "filename": filename,
                "source": "public records",
            },
        )
    ]
    logger.info("Trying to insert %d records", len(docs))
    splitter = TokenTextSplitter(chunk_size=1000, chunk_overlap=500)
    docs = splitter.split_documents(docs)
    upload_documents_to_pinecone(docs, index_name, embeddings)

    report.update(details_status="Done")
    logger.info("Done with report:%s", report)


@app.task(queue="report_task")
def compare_preferences(differences):
    logger.info("Comparing preferences")
    logger.info("Differences: %s", differences)

    messages = []
    messages.extend(
        [
            {"role": "system", "content": settings.COMPARE_PREFERENCES_PROMPT},
            {"role": "user", "content": "The differences between the two homes are as follows: %s" % (differences)},
        ]
    )
    MODEL = settings.OPENAI_MODEL
    diff_summary = get_llm_response(
        model=MODEL,
        messages=messages,
        tools=settings.COMPARE_PREFERENCES_TOOLS,
        tool_choice="required",
        temperature=0.0,
    )

    logger.info("Extracted differences: %s", diff_summary)
    diff_summary = json.loads(diff_summary)
    return diff_summary