import logging
import os
import tempfile

import supabase
from django.conf import settings
from django.core.mail import send_mail
from django.shortcuts import get_object_or_404
from django.http import Http404
from django.core.exceptions import PermissionDenied

from apps.reports.models import Report
from apps.reports.serializers import ReportSerializer
from apps.users.models import CustomUser


logger = logging.getLogger(__file__)
logger.setLevel(logging.DEBUG)


class Supabase(object):
    def __init__(self):
        self.supabase: supabase.Client = supabase.create_client(settings.SUPABASE_URL, settings.SUPABASE_KEY)

    def put_file(self, bucket_name: str, name: str, file):
        result = None
        logger.debug(f"putting object {bucket_name}, {name}")
        bucket = self.supabase.storage.get_bucket(bucket_name)
        if not bucket:
            self.supabase.storage.create_bucket(bucket_name)
        with tempfile.TemporaryDirectory() as tmpdirname:
            tmp_name = os.path.join(tmpdirname, name)
            with open(tmp_name, "wb+") as fout:
                fout.write(file.read())
            result = bucket.upload(name + ".pdf", tmp_name, {"Content-Type": "application/pdf"})
            logger.info("Upload result: %s", result)
            result = bucket.create_signed_url(path=name + ".pdf", expires_in=24 * 3600).get("signedURL")
            logger.info("Pushed to url: %s", result)
        return result

    def get_file(self, bucket_name: str, name: str):
        logger.debug(f"getting object {bucket_name}, {name}")
        bucket = self.supabase.storage.get_bucket(bucket_name)
        if not bucket:
            logger.error(f"Invalid bucket specified {bucket_name}")
            return None
        file = bucket.download(name + ".pdf")
        return file

    def get_url(self, bucket_name: str, name: str):
        bucket = self.supabase.storage.get_bucket(bucket_name)
        return bucket.create_signed_url(path=name + ".pdf", expires_in=24 * 3600).get("signedURL")


def get_supabase():
    try:
        storage: Supabase = Supabase()
        return storage
    except supabase._sync.client.SupabaseException:
        logger.error("Supabase not configured")
        raise supabase._sync.client.SupabaseException("Supabase not configured")


storage = get_supabase()

def get_report_data(report_id: int, user: CustomUser):
    """
    Retrieves a report if the user is the owner or a collaborator.
    Returns None if the user doesn't have permission.
    """
    try:
        # TO DO: we should check if user has even authenticated
        # if not user.is_authenticated:
        #     return None
        reports_as_owner = Report.objects.filter(user=user, pk=report_id)

        reports_as_collaborator = Report.objects.filter(
            collaborators__collaborator=user.email, pk=report_id
        )
        return (reports_as_owner | reports_as_collaborator).distinct().get()
    except Report.DoesNotExist:
        return None
    
def get_report_data_or_404(report_id: int, user: CustomUser):
    """
    Retrieves a report if the user is the owner or a collaborator.
    """
    if not user.is_authenticated:
        raise PermissionDenied()
    try:
        reports_as_owner = Report.objects.filter(user=user, pk=report_id)

        reports_as_collaborator = Report.objects.filter(
            collaborators__collaborator=user.email, pk=report_id
        )
        return (reports_as_owner | reports_as_collaborator).distinct().get()
    except Report.DoesNotExist:
        raise Http404()

def send_invitation_email(email: str, report: Report, user_info: CustomUser | None = None):
    report_data = ReportSerializer(report).data

    invitation_link = f"{settings.REACT_APP_API_URL}/my-properties/insights/{report_data['id']}"

    owner_name = f"{report_data['user']['first_name']} {report_data['user']['last_name']}".strip()
    owner_email = report_data["user"]["email"]
    publisher = f"{owner_name} - {owner_email}"

    if not owner_name:
        owner_name = owner_email
        publisher = owner_email

    user_name = ""
    if user_info:
        user_name = f"{user_info.first_name} {user_info.last_name}"

    subject = f"[HomeScore]: {owner_name} invited you to see a home"

    message = f"""
    Hi, {user_name}

    You've been invited to view the home report.

    Home details:
    Address: {report_data['address']}
    Name: {report_data['name']}
    Publisher: {publisher}

    Click the link to join: {invitation_link}
    """

    send_mail(subject, message, settings.DEFAULT_FROM_EMAIL, [email])
