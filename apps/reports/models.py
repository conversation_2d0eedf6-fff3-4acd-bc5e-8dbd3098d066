from django.contrib import admin
from django.db import models
from enum import Enum
from enumfields import EnumField

from apps.users.models import CustomUser

class ReportState(Enum):
    CONSIDERING = "Considering"
    VIEWED = "Viewed"
    AWAITING_DETAILS = "Awaiting Details"
    SHORTLISTED = "Shortlisted"

class RoomType(Enum):
    LIVING_ROOM = "living room"
    KITCHEN = "kitchen"
    BEDROOM = "bedroom"
    BATHROOM = "bathroom"
    EXTERIOR = "exterior"

class FeatureType(Enum):
    SIZE = "size"
    FLOORING = "flooring"
    QUALITY = "quality"
    COUNTERTOP_MATERIALS = "countertop_materials"
    APPLIANCES = "appliances"
    CLOSETS = "closets"
    LIGHTING_FIXTURE = "lighting_fixture"
    VANITY_STYLES = "vanity_styles"
    BATH = "bath"
    ROOFING = "roofing"
    SIDING = "siding"
    LOT_SIZE_OPENNESS = "lot_size_openness"
    AMENITIES = "amenities"
    ARCHITECTURAL_STYLE = "architectural_style"
    PROPERTY_EXTERIOR_QUALITY = "property_exterior_quality"
    ACCESSIBILITY = "accessibility"

class Report(models.Model):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    name = models.CharField(max_length=256)
    file_name = models.CharField(max_length=64, default="")
    summary = models.TextField(max_length=1024, default="")
    summary_status = models.CharField(max_length=256, default="Processing")
    chat_status = models.CharField(max_length=256, default="Processing")
    summary_version = models.IntegerField(default=0)
    homescore = models.IntegerField(default=0)
    homescore_version = models.IntegerField(default=0)
    address = models.TextField(max_length=256, default="")
    details = models.TextField(max_length=2048, default="")
    details_status = models.CharField(max_length=256, default="Done")
    image_details_status = models.CharField(max_length=256, default="Starting")
    room_summary_status = models.CharField(max_length=256, default="Starting")  # Change to CharField to store string statuses
    has_report = models.BooleanField(default=True)
    state = EnumField(ReportState, max_length=50, default=ReportState.CONSIDERING)

    def update(self, **kwargs):
        self.refresh_from_db()
        for key, value in kwargs.items():
            setattr(self, key, value)
        self.save(update_fields=kwargs.keys())

class Issue(models.Model):
    report = models.ForeignKey(Report, on_delete=models.CASCADE)
    issue = models.CharField(max_length=256, default="")
    category = models.CharField(max_length=64, default="")
    source = models.CharField(max_length=64, default="")
    urgency = models.CharField(max_length=64, default="Low")
    context = models.TextField(max_length=1024, default="")
    recommendation = models.TextField(max_length=1024, default="")
    cost_estimate_low = models.IntegerField(default=0)
    cost_estimate_high = models.IntegerField(default=0)

class Preferences(models.Model):
    report = models.ForeignKey(Report, on_delete=models.CASCADE)
    preference = models.CharField(max_length=256, default="")
    score = models.IntegerField(default=5)
    rationale = models.CharField(max_length=512, default="")

class Collaborators(models.Model):
    report = models.ForeignKey(Report, on_delete=models.CASCADE)
    collaborator = models.TextField(max_length=256)  # models.ForeignKey(CustomUser, on_delete=models.CASCADE)

class Images(models.Model):
    report = models.ForeignKey(Report, on_delete=models.CASCADE)
    image_url = models.URLField(max_length=256, default="")
    room_type = EnumField(RoomType, max_length=50, default=RoomType.LIVING_ROOM)

class ImageFeature(models.Model):
    image = models.ForeignKey(Images, on_delete=models.CASCADE, related_name='features')
    image_feature_type = EnumField(FeatureType, max_length=50, default=FeatureType.SIZE)
    choice = models.CharField(max_length=255, default="average")
    description = models.TextField(max_length=1024, default="")

class RoomSummary(models.Model):
    report = models.ForeignKey(Report, on_delete=models.CASCADE)
    room_type = EnumField(RoomType, max_length=50, default=RoomType.LIVING_ROOM)
    feature_type = EnumField(FeatureType, max_length=50, default=FeatureType.SIZE)
    summary = models.TextField(max_length=1024, default="")
    values = models.JSONField(default=list)

admin.site.register(Report)
admin.site.register(Issue)
admin.site.register(Preferences)
admin.site.register(Collaborators)
admin.site.register(Images)
admin.site.register(ImageFeature)
admin.site.register(RoomSummary)