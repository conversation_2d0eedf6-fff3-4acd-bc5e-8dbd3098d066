# Django rest serializer for reports app

from rest_framework import serializers

from apps.users.serializers import CustomUserSerializer

from .models import Collaborators, Images, Issue, Preferences, Report, ImageFeature, RoomSummary

class ReportSerializer(serializers.ModelSerializer):
    user = CustomUserSerializer()
    state = serializers.SerializerMethodField()

    class Meta:
        model = Report
        fields = "__all__"

    def get_state(self, obj):
        return obj.state.value  # This converts the ReportState enum to its string value


# Django rest serializer for Issue class
class IssueSerializer(serializers.ModelSerializer):
    class Meta:
        model = Issue
        fields = "__all__"


class PreferencesSerializer(serializers.ModelSerializer):
    class Meta:
        model = Preferences
        fields = "__all__"


class CollaboratorSerializer(serializers.ModelSerializer):
    class Meta:
        model = Collaborators
        fields = "__all__"

class ImageFeatureSerializer(serializers.ModelSerializer):
    class Meta:
        model = ImageFeature
        fields = "__all__"


class ImagesSerializer(serializers.ModelSerializer):
    class Meta:
        model = Images
        fields = "__all__"
        
class RoomSummarySerializer(serializers.ModelSerializer):
    class Meta:
        model = RoomSummary
        fields = "__all__"
