# Django imports
from django.conf import settings
from django.http import JsonR<PERSON>ponse
from django.shortcuts import render, get_object_or_404
from django.utils.decorators import method_decorator
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods

# REST framework imports
from rest_framework import permissions, status, views
from rest_framework.authentication import SessionAuthentication, BasicAuthentication
from rest_framework.decorators import api_view
from rest_framework.parsers import MultiPart<PERSON>ars<PERSON>, FormParser
from rest_framework.response import Response
from rest_framework.views import APIView

# Third-party imports
from bs4 import BeautifulSoup
from langchain.schema import Document
from langchain_openai import AzureOpenAIEmbeddings
from hashlib import md5
from celery import chain
from celery.result import AsyncResult

# Local imports
from .authentication import BearerTokenAuthentication
from .helpers import split_and_upload_to_pinecone, get_all_descriptions, generate_room_summary, map_task
from .tasks.age import AgeSection
from .tasks.permit import PermitSection
from .tasks.question import QuestionSection
from .tasks.structural import StructuralSection
from .tasks.opportunity import OpportunitySection
from apps.reports import utils
from .utils import get_report_data, StatusConstants
from .models import ReportUploadStatus, InspectionUploadStatus, ImageAnalysisStatus, RoomSummaryStatus
from .tasks.age import age_section_eval_task  # Add this import
from .tasks.permit import permit_section_eval_task  # Add this import
from .tasks.question import question_section_eval_task  # Add this import
from .tasks.opportunity import opportunity_section_eval_task  # Add this import
from .tasks.property_analysis import property_analysis_task  # Add this import
from .tasks.context_analysis import context_analysis_task  # Add this import
from .utils import task_timer

import os
import json
import logging
import time
from io import BytesIO



logger = logging.getLogger("homescore")  # Update logger to match settings.py configuration
storage: utils.Supabase | None = utils.storage

class Checker(APIView):
    authentication_classes = [SessionAuthentication, BasicAuthentication, BearerTokenAuthentication]

    def post(self, request, section, checker_id):
        try:
            # data = json.loads(request.body)
            # section = data.get("section")
            # prompt = data.get("prompt")
            prompt = request.body.decode("utf-8")
            # section = "permit"

            if not all([section, prompt]):
                return JsonResponse({
                    "error": "Missing required fields"
                }, status=400)

            current_dir = os.path.dirname(os.path.abspath(__file__))
            base_path = os.path.join(current_dir, "tasks", "static", str(checker_id))

            # Handle different section types
            match section:
                case "age":
                    response_data = AgeSection.eval(prompt, base_path)
                case "permit":
                    response_data = PermitSection.eval(prompt, base_path)
                case "question":
                    response_data = QuestionSection.eval(prompt, base_path)
                case "opportunity":
                    response_data = OpportunitySection.eval(prompt, base_path)
                case _:
                    return JsonResponse({
                        "error": "Invalid section",
                        "valid_options": ["age", "permit", "question", "opportunity"]
                    }, status=400)

            return JsonResponse({
                "status": "success",
                "checker_id": checker_id,
                "section": section,
                "prompt": prompt,
                **response_data
            })

        except ValueError:
            return JsonResponse({
                "error": "Invalid JSON format"
            }, status=400)

class DocumentProcessView(views.APIView):
    permission_classes = [permissions.IsAuthenticated]
    authentication_classes = [SessionAuthentication, BasicAuthentication, BearerTokenAuthentication]
    parser_classes = (MultiPartParser, FormParser)


    SECTION_IDS = [
        "section_top",
        "section_summary",
        "section_property_details",
        "section_building_permits",
        "section_images"  # Add images section to the list
    ]

    def extract_sections(self, soup):
        """Extract all standard sections and images from BeautifulSoup object"""
        sections = {}
        for section_id in self.SECTION_IDS:
            section = soup.find("div", {"class": "r-section", "id": section_id})
            if section:
                key = section_id.replace("section_", "")
                if key == "images":
                    # Extract image URLs from the images section
                    images = []
                    for img in section.find_all("img", {"class": "lazyload"}):
                        src = img.get("data-src")
                        images.append(src)
                    sections[key] = images
                    logger.debug(f"✓ Extracted {len(images)} images from {key} section")
                else:
                    sections[key] = section.get_text(strip=True)
                    logger.debug(f"✓ Extracted section: {key}")
        return sections

    def post(self, request):
    # Initial setup try block
        # Record the start time for the entire request
        request_start_time = time.perf_counter()
        try:
            # Create or get the upload status instance
            #For infopay reportid = checker_id
            checker_id = request.data.get("checker_id") or request.query_params.get("checker_id") or request.headers.get("reportid")

            if not checker_id:
                logger.error("❌ No reportid provided")
                return Response({"error": "checker_id is required"}, status=status.HTTP_400_BAD_REQUEST)

            try:
                checker_id = int(checker_id)
            except (ValueError, TypeError):
                logger.error("❌ Invalid reportid format")
                return Response({"error": "reportid must be an integer"}, status=status.HTTP_400_BAD_REQUEST)

            # Initialize all status tracking models for this checker_id
            report_status, _ = ReportUploadStatus.objects.get_or_create(checker_id=checker_id)
            image_status, _ = ImageAnalysisStatus.objects.get_or_create(checker_id=checker_id)
            room_status, _ = RoomSummaryStatus.objects.get_or_create(checker_id=checker_id)

            # Set initial status for all models using standardized constants
            report_status.update_status(StatusConstants.REPORT_INITIALIZING)
            image_status.update_status(StatusConstants.IMAGES_PENDING)
            room_status.update_status(StatusConstants.ROOM_PENDING)

            logger.info(f"✅ Initialized status tracking for checker_id: {checker_id}")
        except Exception as e:
            logger.error(f"❌ Initial setup failed: {str(e)}", exc_info=True)
            return Response(
                {"error": "Failed to initialize status tracking"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        # Process the document
        try:
            logger.debug("🚀 Starting document processing request")
            report_status.update_status("Report: Uploading HTML")

            # Initialize html_url to None
            html_url = None

            # 1. Handle file upload and parse HTML
            html_file = request.FILES.get("file")
            if not html_file:
                logger.warning("⚠️ No HTML file provided in request")
                report_status.update_status("Report: Failed - No HTML file provided")
                image_status.update_status("Images: Failed - No HTML file")
                room_status.update_status("Room Summary: Failed - No HTML file")
                return Response({"error": "No HTML file provided"},
                                status=status.HTTP_400_BAD_REQUEST)

            # Track timing for HTML parsing
            with task_timer("HTML parsing", checker_id):
                content = html_file.read().decode("utf-8")
                soup = BeautifulSoup(content, "html.parser")
                report_status.update_status("Parsing")

            # Upload the HTML file to Supabase
            try:
                report_status.update_status("Uploading to Storage")

                with task_timer("Supabase HTML upload", checker_id):
                    logger.info(f"📤 Uploading HTML file to Supabase storage with id: {checker_id}")
                    timestamp = time.strftime("%Y%m%d_%H%M%S")
                    file_name = md5(f"{checker_id}_html_{timestamp}".encode()).hexdigest() + ".html"

                    # Rewind the file to the beginning so we can read it again
                    html_file.seek(0)

                    # Upload to a different bucket or the same bucket with a different prefix
                    html_url = storage.put_file(settings.INFOPAY_BUCKET, file_name, html_file)
                    logger.info(f"✅ HTML file uploaded successfully, URL: {html_url}")

                    # Store the URL in the upload status
                    report_status.html_url = html_url
                    report_status.save(update_fields=['html_url'])
            except Exception as e:
                logger.error(f"❌ Supabase storage error for HTML file: {str(e)}")
                # Set html_url to None if upload fails
                html_url = None
                report_status.update_status("Warning: Storage upload failed, continuing processing")
                # Continue processing even if upload fails
                # We don't want to fail the entire process if just the storage fails

            # 2. Extract all sections
            report_status.update_status("Extracting sections")
            with task_timer("Section extraction", checker_id):
                sections = self.extract_sections(soup)

            if not sections:
                logger.error("❌ No sections found in HTML file")
                report_status.update_status("Failed: No valid sections found")
                image_status.update_status("Failed: No valid sections found")
                room_status.update_status("Failed: No valid sections found")
                return Response(
                    {"error": "No valid sections found in the HTML file"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # 3. Create Document object with full content
            report_status.update_status("Processing content")
            with task_timer("Content processing", checker_id):
                processed_content = soup.get_text()
                doc = Document(
                    page_content=processed_content,
                    metadata={
                        "user_id": request.user.id,
                        "source": html_file.name,
                        "content_type": html_file.content_type,
                        "checker_id": checker_id
                    }
                )

            # 4. Initialize embeddings and upload to Pinecone
            report_status.update_status("Initializing embeddings")
            with task_timer("Embeddings initialization", checker_id):
                logger.info("⚡ Initializing Azure OpenAI embeddings")
                embeddings = AzureOpenAIEmbeddings(
                    azure_endpoint=settings.AZURE_OPENAI_API_ENDPOINT,
                    openai_api_key=settings.AZURE_OPENAI_API_KEY,
                    model=settings.AZURE_OPENAI_EMBEDDINGS_MODEL
                )

            report_status.update_status("Uploading to vector database")
            with task_timer("Pinecone upload", checker_id):
                logger.debug("📤 Starting document upload to Pinecone")
                split_and_upload_to_pinecone(
                        docs=[doc],
                        embeddings=embeddings,
                        index_name=settings.PINECONE_INFOPAY_INDEX
                    )

            # 5. Process sections with their relevant content
            results = {}

            # Log extracted images if found
            if sections.get("images"):
                logger.info(f"📸 Found {len(sections['images'])} property images")

                try:
                    # Update image analysis status
                    image_status.update_status("Images: Queuing for processing")
                    room_status.update_status("Room Summary: Waiting for image analysis")

                    with task_timer("Image processing workflow setup", checker_id):
                        workflow = chain(
                            get_all_descriptions.s(sections['images'], checker_id),
                            map_task.s(sections['images']),  # map_task will receive (checker_id, descriptions, image_urls)
                            generate_room_summary.si(checker_id)  # Use immutable signature for last task
                        )

                        # Launch the workflow
                        task_result = workflow.apply_async()

                    # Update status with task ID for tracking
                    image_status.update_status("Processing")
                    logger.info(f"✅ Successfully queued image processing workflow with task ID: {task_result.task_id}")

                except Exception as e:
                    error_message = f"Failed to queue image processing: {str(e)}"
                    logger.error(f"❌ {error_message}")

                    # Update status models with error
                    image_status.update_status(f"Failed: {error_message}")
                    room_status.update_status(f"Failed: Image analysis failed")
                    report_status.update_status("Warning: Image processing failed, continuing with other analyses")

                    # Continue processing - don't return error response
                    # We want to complete other analyses even if image processing fails
            else:
                logger.info("⚠️ No images found in the report")
                image_status.update_status(StatusConstants.IMAGES_SKIPPED)
                room_status.update_status(StatusConstants.ROOM_SKIPPED)

            # Update report status for section analysis
            report_status.update_status("Analyzing sections")
            logger.info("🔄 Starting section analysis tasks")

            # Check if testing_sync is set to True
            testing_sync = request.query_params.get("testing_sync", "false").lower() == "true"
            start_time = time.perf_counter()

            if testing_sync:
                logger.info("🔄 Starting synchronous processing for all sections")
                # Synchronous processing for testing
                if sections.get("top"):
                    logger.info("🔄 Sync processing of age section started")
                    with task_timer("Age section analysis (sync)", checker_id):
                        results["age"] = age_section_eval_task.run(
                            content=sections.get("top"),
                            checker_id=checker_id
                        )

                if sections.get("building_permits"):
                    logger.info("🔄 Sync processing of permits section started")
                    with task_timer("Permits section analysis (sync)", checker_id):
                        results["permits"] = permit_section_eval_task.run(
                            content=sections["building_permits"],
                            checker_id=checker_id
                        )

                if processed_content:
                    logger.info("🔄 Sync processing of questions section started")
                    with task_timer("Questions section analysis (sync)", checker_id):
                        results["questions"] = question_section_eval_task.run(
                            content=processed_content,
                            checker_id=checker_id
                        )

                    logger.info("🔄 Sync processing of opportunities section started")
                    with task_timer("Opportunities section analysis (sync)", checker_id):
                        results["opportunities"] = opportunity_section_eval_task.run(
                            content=processed_content,
                            checker_id=checker_id
                        )

                # Update status after sync processing
                report_status.update_status(StatusConstants.REPORT_COMPLETED)
            else:
                # Asynchronous processing
                logger.info("🔄 Starting asynchronous processing for all sections")
                task_ids = []

                # Process age and permit together
                if sections.get("top"):
                    logger.info("🔄 Async processing of age section started")
                    age_content = sections.get("top")
                    permit_content = sections.get("building_permits") or ""
                    property_analysis_content = (age_content or "") + permit_content

                    with task_timer("Property analysis task queuing", checker_id):
                        task = property_analysis_task.delay(
                            content=property_analysis_content,
                            checker_id=checker_id
                        )
                        task_ids.append(task.task_id)
                        results["property_analysis"] = task.task_id
                        logger.info(f"✅ Property analysis task queued with ID: {task.task_id}")

                # Process questions and opportunities together
                if processed_content:
                    logger.info(f"🔄 Async processing of context analysis started for checker_id: {checker_id}")
                    with task_timer("Context analysis task queuing", checker_id):
                        task = context_analysis_task.delay(
                            content=processed_content,
                            checker_id=checker_id
                        )
                        task_ids.append(task.task_id)
                        results["context"] = task.task_id
                        logger.info(f"✅ Context analysis task queued with ID: {task.task_id}")

                # Update status after queuing async tasks to indicate processing is still ongoing
                report_status.update_status(StatusConstants.REPORT_PROCESSING)

                # Wait for all tasks to complete (only for testing)
                if request.query_params.get("wait_for_async", "false").lower() == "true":
                    logger.info("⏳ Waiting for all async tasks to complete...")
                    report_status.update_status("Waiting for analysis completion")

                    for task_id in task_ids:
                        with task_timer(f"Waiting for task {task_id}", checker_id):
                            AsyncResult(task_id).get()  # Wait for task completion

                    total_time = time.perf_counter() - start_time
                    logger.info(f"✅ All asynchronous sections completed in {total_time:.2f} seconds for checker_id: {checker_id}")
                    report_status.update_status(StatusConstants.REPORT_COMPLETED)



            # 6. Return success response
            logger.info(f"✅ Successfully processed HTML file: {html_file.name}")

            # Update status to indicate HTML processing is complete but analysis may still be ongoing
            if testing_sync:
                # If we processed everything synchronously, we can mark as fully completed
                report_status.update_status(StatusConstants.REPORT_COMPLETED)
            else:
                # For async processing, indicate that HTML is processed but analysis is still ongoing
                report_status.update_status(StatusConstants.REPORT_PROCESSING)

            # Only update image and room status if they're still in a pending state
            # (they might have been updated by async tasks already)
            if image_status.upload_status == StatusConstants.IMAGES_PENDING:
                image_status.update_status("Images: Waiting for processing")

            if room_status.upload_status == StatusConstants.ROOM_PENDING:
                room_status.update_status("Room Summary: Waiting for image analysis")

            # Calculate total request time and processing time
            total_request_time = time.perf_counter() - request_start_time
            section_processing_time = time.perf_counter() - start_time

            # Prepare minimal status information for response
            # Only include the most essential information needed immediately after upload
            status_info = {
                "status": StatusConstants.STATUS_PROCESSING,  # Simple overall status
                "processing_time": f"{total_request_time:.2f} seconds"
            }

            return Response({
                "message": "HTML file processed successfully",
                "filename": html_file.name,
                "id": checker_id,
                "status": status_info
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"❌ Document processing failed: {str(e)}", exc_info=True)

            # Update all status models to reflect failure with more descriptive messages
            error_message = str(e)[:100]  # Truncate very long error messages

            try:
                report_status.update_status(f"Report: Failed - {error_message}")
                image_status.update_status(f"Images: Failed - Report processing error")
                room_status.update_status(f"Room Summary: Failed - Report processing error")

                # Log the total time even in case of failure
                failure_time = time.perf_counter() - request_start_time
                logger.error(f"❌ Processing failed after {failure_time:.2f} seconds")
            except Exception as status_error:
                logger.error(f"❌ Failed to update status models: {str(status_error)}")

            return Response({
                "error": "An unexpected error occurred during document processing.",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class InspectionReportView(views.APIView):
    permission_classes = [permissions.IsAuthenticated]
    authentication_classes = [SessionAuthentication, BasicAuthentication, BearerTokenAuthentication]

    def post(self, request):
        # Record the start time for the entire request
        request_start_time = time.perf_counter()

        # Get checker_id from query params if not in request data
        checker_id = request.data.get("checker_id") or request.query_params.get("checker_id") or request.headers.get("reportid")

        if not checker_id:
            logger.error("❌ No checker_id provided")
            return Response(
                {"error": "checker_id is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create or get the upload status instance
        upload_status, created = InspectionUploadStatus.objects.get_or_create(checker_id=checker_id)

        logger.info(f"🔄 Starting inspection report processing for checker_id: {checker_id}")

        inspection_file = request.FILES.get("file")
        if not inspection_file:
            logger.error("❌ No inspection file provided")
            return Response(
                {"error": "No Inspection report file provided"},
                status=status.HTTP_400_BAD_REQUEST
            )

        logger.info(f"📁 Processing file: {inspection_file.name} (size: {inspection_file.size} bytes)")

        # Update upload status to 'Uploading' with more descriptive message
        upload_status.update_status("Inspection: Uploading")

        # Store file in Supabase with checker_id as filename
        try:
            logger.info(f"📤 Uploading file to Supabase storage with id: {checker_id}")
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            file_name = md5(f"{checker_id}_{timestamp}".encode()).hexdigest()

            url = storage.put_file(settings.REPORT_BUCKET, file_name, inspection_file)
            logger.info(f"✅ File uploaded successfully, URL: {url}")
        except Exception as e:
            logger.error(f"❌ Storage error: {str(e)}")
            upload_status.update_status("Inspection: Failed - Storage")
            return Response(
                {"error": "Failed to store file"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        # Update upload status to 'Uploaded' with more descriptive message
        upload_status.update_status("Inspection: Uploaded")

        # Process report
        logger.info("🔍 Starting report analysis")

        # Update upload status to 'Analyzing' with more descriptive message
        upload_status.update_status("Inspection: Analyzing")

        try:
            results = StructuralSection.eval(url, checker_id)
            logger.info("✅ Report analysis completed successfully")
        except Exception as e:
            logger.error(f"❌ Analysis error: {str(e)}")
            upload_status.update_status("Inspection: Failed - Analysis")
            return Response(
                {"error": "Failed to analyze report"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        # Finalize upload status with more descriptive message
        upload_status.update_status("Inspection: Completed")

        # Calculate total request time
        total_request_time = time.perf_counter() - request_start_time

        # Prepare minimal status information for response
        status_info = {
            "status": StatusConstants.STATUS_COMPLETED,  # Simple overall status
            "processing_time": f"{total_request_time:.2f} seconds"
        }

        return Response({
            "message": "Inspection report processed successfully",
            "checker_id": checker_id,
            "results": results,
            "status": status_info
        }, status=status.HTTP_200_OK)



class PropertyCardDataRetrieval(views.APIView):
    authentication_classes = [BearerTokenAuthentication]

    def get(self, request, checker_id):
        try:
            response = get_report_data(checker_id=checker_id)
            return Response({"data": response}, status=200)
        except Exception as e:
            logger.error(f"Failed to retrieve property card data: {str(e)}")
            return Response(
                {"error": "An error occurred while retrieving property card data"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class PropertyCardDataList(views.APIView):
    authentication_classes = [BearerTokenAuthentication]

    def get(self, request):
        try:
            report_ids = ReportUploadStatus.objects.filter(upload_status='Completed').values_list("checker_id", flat=True)
            if not report_ids:
                return Response({"error": "No report IDs found."}, status=404)
            return Response({"data": report_ids}, status=200)
        except Exception as e:
            return Response({"error": str(e)}, status=500)
