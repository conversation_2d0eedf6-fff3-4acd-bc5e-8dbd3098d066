# Generated by Django 4.2.13 on 2025-02-07 00:16

import apps.reports.models
from django.db import migrations, models
import enumfields.fields


class Migration(migrations.Migration):

    dependencies = [
        ("checker", "0008_checkerimage_checkerimagefeature_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="CheckerRoomSummary",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("room_type", models.CharField(default="", max_length=50)),
                ("summary", models.TextField(default="", max_length=1024)),
                ("values", models.JSO<PERSON>ield(default=list)),
                ("checker_id", models.CharField(default="", max_length=255)),
                (
                    "feature_type",
                    enumfields.fields.EnumField(default="size", enum=apps.reports.models.FeatureType, max_length=50),
                ),
            ],
            options={
                "indexes": [models.Index(fields=["checker_id", "room_type"], name="checker_che_checker_334e92_idx")],
                "unique_together": {("checker_id", "room_type", "feature_type")},
            },
        ),
    ]
