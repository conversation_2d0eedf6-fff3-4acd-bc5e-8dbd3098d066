# Generated by Django 4.2.13 on 2025-01-17 16:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("checker", "0005_propertyissues"),
    ]

    operations = [
        migrations.CreateModel(
            name="InspectionUploadStatus",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("checker_id", models.CharField(max_length=255, unique=True)),
                ("upload_status", models.CharField(default="Pending", max_length=50)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
