# Generated by Django 4.2.13 on 2025-02-06 04:30

import apps.reports.models
from django.db import migrations, models
import django.db.models.deletion
import enumfields.fields


class Migration(migrations.Migration):

    dependencies = [
        ("checker", "0007_reportuploadstatus"),
    ]

    operations = [
        migrations.CreateModel(
            name="CheckerImage",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("image_url", models.URLField(default="", max_length=256)),
                ("width", models.IntegerField(blank=True, null=True)),
                ("height", models.IntegerField(blank=True, null=True)),
                ("checker_id", models.CharField(default="", max_length=255)),
                (
                    "room_type",
                    enumfields.fields.EnumField(
                        default="living room", enum=apps.reports.models.RoomType, max_length=50
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="CheckerImageFeature",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("description", models.TextField(default="", max_length=1024)),
                ("choice", models.CharField(default="average", max_length=255)),
                (
                    "feature_type",
                    enumfields.fields.EnumField(default="size", enum=apps.reports.models.FeatureType, max_length=50),
                ),
                ("image", models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="checker.checkerimage")),
            ],
        ),
        migrations.AddIndex(
            model_name="checkerimage",
            index=models.Index(fields=["checker_id", "room_type"], name="checker_che_checker_68ee39_idx"),
        ),
        migrations.AlterUniqueTogether(
            name="checkerimage",
            unique_together={("checker_id", "image_url")},
        ),
        migrations.AddIndex(
            model_name="checkerimagefeature",
            index=models.Index(fields=["image", "feature_type"], name="checker_che_image_i_eb1425_idx"),
        ),
        migrations.AlterUniqueTogether(
            name="checkerimagefeature",
            unique_together={("image", "feature_type", "choice")},
        ),
    ]
