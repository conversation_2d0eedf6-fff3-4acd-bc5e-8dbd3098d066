# Generated by Django 4.2.13 on 2025-02-14 04:08

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("checker", "0009_checkerroomsummary"),
    ]

    operations = [
        migrations.CreateModel(
            name="CheckerHomeQuality",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("score", models.IntegerField(blank=True, default=0)),
                ("condition", models.CharField(default="", max_length=50)),
                ("description", models.TextField(default="", max_length=1024)),
                ("checker_id", models.Char<PERSON>ield(default="", max_length=255)),
            ],
            options={
                "indexes": [models.Index(fields=["checker_id"], name="checker_che_checker_906b02_idx")],
                "unique_together": {("checker_id", "condition")},
            },
        ),
    ]
