# Generated by Django 4.2.13 on 2024-12-31 18:08

import apps.checker.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("checker", "0001_initial"),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name="propertyrisk",
            unique_together=set(),
        ),
        migrations.AddField(
            model_name="propertyrisk",
            name="risk_type",
            field=models.CharField(
                choices=[
                    ("structural", "Structural"),
                    ("system", "System"),
                    ("safety", "Safety"),
                    ("maintenance", "Maintenance"),
                ],
                default=apps.checker.models.RiskType["MAINTENANCE"],
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="propertyrisk",
            name="section",
            field=models.CharField(
                choices=[("age", "Age Section"), ("permit", "Permit Section"), ("structural", "Structural Section")],
                default="age",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="propertyrisk",
            name="severity",
            field=models.CharField(
                choices=[("low", "Low"), ("medium", "Medium"), ("high", "High")],
                default=apps.checker.models.Severity["MEDIUM"],
                max_length=10,
            ),
        ),
        migrations.AlterField(
            model_name="propertyrisk",
            name="category",
            field=models.CharField(
                choices=[
                    ("asbestos", "Asbestos"),
                    ("lead", "Lead"),
                    ("wiring", "Wiring"),
                    ("plumbing", "Plumbing"),
                    ("sewer", "Sewer"),
                    ("windows", "Windows"),
                ],
                default=apps.checker.models.RiskCategory["PLUMBING"],
                max_length=20,
            ),
        ),
        migrations.AlterUniqueTogether(
            name="propertyrisk",
            unique_together={("checker_id", "section", "category")},
        ),
        migrations.CreateModel(
            name="PermitAnalysis",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("checker_id", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("permit_date", models.DateField(null=True)),
                ("permit_type", models.CharField(max_length=100)),
                ("description", models.TextField()),
                ("value", models.CharField(max_length=50, null=True)),
                ("importance", models.TextField()),
            ],
            options={
                "unique_together": {("checker_id", "permit_date", "permit_type")},
            },
        ),
    ]
