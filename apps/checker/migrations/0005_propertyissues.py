# Generated by Django 4.2.13 on 2025-01-10 20:24

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("checker", "0004_propertyopportunity"),
    ]

    operations = [
        migrations.CreateModel(
            name="PropertyIssues",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("checker_id", models.CharField(max_length=255)),
                ("issue", models.Char<PERSON>ield(default="", max_length=256)),
                ("category", models.CharField(default="", max_length=64)),
                ("source", models.Char<PERSON>ield(default="", max_length=64)),
                ("urgency", models.<PERSON><PERSON><PERSON><PERSON>(default="Low", max_length=64)),
                ("context", models.TextField(default="", max_length=1024)),
                ("recommendation", models.TextField(default="", max_length=1024)),
                ("cost_estimate_low", models.Integer<PERSON>ield(default=0)),
                ("cost_estimate_high", models.Integer<PERSON><PERSON>(default=0)),
            ],
            options={
                "unique_together": {("checker_id", "issue", "category", "source")},
            },
        ),
    ]
