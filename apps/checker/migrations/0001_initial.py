# Generated by Django 4.2.13 on 2024-12-20 21:46

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="PropertyRisk",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("checker_id", models.<PERSON>r<PERSON>ield(max_length=255)),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("asbestos", "Asbestos Risks"),
                            ("lead", "Lead Paint"),
                            ("wiring", "Knob-and-Tube Wiring"),
                            ("plumbing", "Galvanized Plumbing"),
                            ("sewer", "Cast Iron Sewer"),
                            ("windows", "Single-Pane Windows"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "unique_together": {("checker_id", "category")},
            },
        ),
    ]
