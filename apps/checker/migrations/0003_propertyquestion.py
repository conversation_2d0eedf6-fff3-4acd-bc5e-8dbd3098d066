# Generated by Django 4.2.13 on 2025-01-09 07:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("checker", "0002_alter_propertyrisk_unique_together_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="PropertyQuestion",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("checker_id", models.Char<PERSON>ield(max_length=100)),
                ("category", models.Char<PERSON>ield(max_length=50)),
                ("question", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "unique_together": {("checker_id", "question")},
            },
        ),
    ]
