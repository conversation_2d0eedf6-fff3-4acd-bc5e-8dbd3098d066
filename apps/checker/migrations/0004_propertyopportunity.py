# Generated by Django 4.2.13 on 2025-01-10 03:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("checker", "0003_propertyquestion"),
    ]

    operations = [
        migrations.CreateModel(
            name="PropertyOpportunity",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("checker_id", models.Char<PERSON>ield(max_length=255)),
                ("category", models.<PERSON>r<PERSON>ield(max_length=50)),
                ("title", models.<PERSON>r<PERSON>ield(max_length=200)),
                ("description", models.TextField()),
                ("estimated_cost", models.Char<PERSON>ield(max_length=100)),
                ("potential_value_add", models.Char<PERSON>ield(max_length=100)),
                ("created_at", models.DateTime<PERSON>ield(auto_now_add=True)),
            ],
            options={
                "unique_together": {("checker_id", "category", "title")},
            },
        ),
    ]
