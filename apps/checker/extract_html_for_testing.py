

file_path = "/Users/<USER>/code/homescore/homescore/apps/checker/2view-source_https___propertychecker.com_srep_675a0d76873eca752312d8f7_84-wellesley-st--weston--ma-02493_.html"
# file_path = "/Users/<USER>/code/homescore/homescore/apps/checker/billrica.html"
# file_path = "/Users/<USER>/code/homescore/homescore/apps/checker/londonderry.html"
# file_path = "/Users/<USER>/code/homescore/homescore/apps/checker/waltham.html"

from html_to_dict import extract_sections

with open(file_path, 'rb') as html_file:
    content = html_file.read().decode("utf-8")

sections = extract_sections(content)
for section_name, content in sections.items():
    print(f"\n=== {section_name} ===")
    print(content)

print('done')