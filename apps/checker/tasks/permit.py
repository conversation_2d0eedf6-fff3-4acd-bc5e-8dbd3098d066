from django.conf import settings
from openai import AzureOpenAI
from apps.checker.llm_utils import basic_llm_response
from apps.checker.models import *  # This will import all models including enums
from apps.checker.helpers import extract_tool_response  # Update import
import json
import logging
import os
from datetime import datetime
from celery import shared_task
from homescore.celery import app

logger = logging.getLogger("homescore")

client = AzureOpenAI(
    api_key=settings.AZURE_OPENAI_API_KEY,
    api_version=settings.AZURE_OPENAI_API_VERSION,
    azure_endpoint=settings.AZURE_OPENAI_API_ENDPOINT
)


PERMIT_TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "analyze_permits",
            "description": "Analyze building permits and identify significant renovations and risks",
            "parameters": {
                "type": "object",
                "properties": {
                    "significant_permits": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "date": {"type": "string"},
                                "type": {"type": "string"},
                                "description": {"type": "string"},
                                "value": {"type": "string"},
                                "importance": {
                                    "type": "string",
                                    "description": "Why this permit is important for a buyer"
                                }
                            },
                            "required": ["date", "type", "description", "importance"]
                        },
                    },
                    "potential_risks": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "risk_type": {
                                    "type": "string",
                                    "enum": [rt.value for rt in RiskType]
                                },
                                "description": {
                                    "type": "string",
                                    "description": "Detailed explanation of the risk"
                                },
                                "severity": {
                                    "type": "string",
                                    "enum": [s.value for s in Severity]
                                }
                            },
                            "required": ["risk_type", "description", "severity"]
                        },
                    }
                },
                "required": ["significant_permits", "potential_risks"]
            }
        }
    }
]

@app.task(name="permit_section_eval", queue="report_task")
def permit_section_eval_task(content: str, checker_id: str) -> dict:
    """Celery task for permit section evaluation"""
    try:
        return PermitSection.eval(content, checker_id)
    except Exception as e:
        logger.error(f"Error in permit_section_eval_task: {str(e)}")
        return {"type": "permit", "permits": [], "risks": []}

class PermitSection:
    @staticmethod
    def convert_date_format(date_str: str) -> str:
        """Convert date from MM/DD/YYYY to YYYY-MM-DD format"""
        try:
            if not date_str:
                return None
            date_obj = datetime.strptime(date_str, '%m/%d/%Y')
            return date_obj.strftime('%Y-%m-%d')
        except ValueError as e:
            logger.error(f"Error converting date {date_str}: {e}")
            return None

    @staticmethod
    def map_risk_type_to_category(risk_type: str) -> str:
        """Map risk type to an appropriate category"""
        mapping = {
            "structural": RiskCategory.WINDOWS.value,  # For structural issues
            "system": RiskCategory.PLUMBING.value,    # For system related
            "safety": RiskCategory.WIRING.value,      # For safety issues
            "maintenance": RiskCategory.SEWER.value   # For maintenance issues
        }
        return mapping.get(risk_type, RiskCategory.PLUMBING.value)

    @staticmethod
    def store_analysis(checker_id: str, analysis: dict):
        """Store permit analysis and risks in separate tables"""
        try:
            # Store significant permits
            for permit in analysis.get("significant_permits", []):
                formatted_date = PermitSection.convert_date_format(permit.get("date"))
                PermitAnalysis.objects.update_or_create(
                    checker_id=checker_id,
                    permit_date=formatted_date,
                    permit_type=permit.get("type"),
                    defaults={
                        "description": permit.get("description"),
                        "value": permit.get("value"),
                        "importance": permit.get("importance")
                    }
                )
            
            # Store potential risks using PropertyRisk with mapped categories
            for risk in analysis.get("potential_risks", []):
                risk_type = risk.get("risk_type")
                category = PermitSection.map_risk_type_to_category(risk_type)
                
                PropertyRisk.objects.update_or_create(
                    checker_id=checker_id,
                    section="permit",
                    category=category,  # Use mapped category instead of default
                    defaults={
                        "description": risk.get("description"),
                        "risk_type": risk_type,
                        "severity": risk.get("severity")
                    }
                )
                
        except Exception as e:
            logger.error(f"Error storing permit analysis: {e}")
            raise

    @staticmethod
    def eval(content: str, checker_id: str) -> dict:
        try:
            messages = [
                {"role": "system", "content": settings.PERMIT_PROMPT},
                {"role": "user", "content": f"Analyze these building permits: {content}"}
            ]
            
            response = client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=messages,
                tools=PERMIT_TOOLS,
                tool_choice="required",
                temperature=0
            )
            
            analysis = extract_tool_response(response, {"significant_permits": [], "potential_risks": []})
            
            # Store the analysis in database
            PermitSection.store_analysis(checker_id, analysis)
            
            return {
                "type": "permit",
                "permits": analysis.get("significant_permits", []),
                "risks": analysis.get("potential_risks", [])
            }

        except Exception as e:
            logger.error(f"Error in permit section evaluation: {str(e)}")
            return {"type": "permit", "permits": [], "risks": []}
