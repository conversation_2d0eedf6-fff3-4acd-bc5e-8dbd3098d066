from django.conf import settings
from openai import AzureOpenAI
from apps.checker.helpers import extract_tool_response
from apps.checker.models import PropertyQuestion
import json
import logging
from celery import shared_task
from homescore.celery import app

logger = logging.getLogger("homescore")

client = AzureOpenAI(
    api_key=settings.AZURE_OPENAI_API_KEY,
    api_version=settings.AZURE_OPENAI_API_VERSION,
    azure_endpoint=settings.AZURE_OPENAI_API_ENDPOINT
)

QUESTION_TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "generate_questions",
            "description": "Generate critical questions based on property report analysis",
            "parameters": {
                "type": "object",
                "properties": {
                    "categories": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "category": {
                                    "type": "string",
                                    "enum": [
                                        "Safety Concerns",
                                        "Structural Integrity",
                                        "Investment Quality",
                                        "Environmental Risks"
                                    ]
                                },
                                "questions": {
                                    "type": "array",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "question": {"type": "string"},
                                            "importance": {
                                                "type": "string",
                                                "enum": ["high", "medium", "low"]
                                            }
                                        },
                                        "required": ["question", "importance"]
                                    },
                                    "maxItems": 2
                                }
                            },
                            "required": ["category", "questions"]
                        },
                        "maxItems": 3
                    }
                },
                "required": ["categories"]
            }
        }
    }
]

@app.task(name="question_section_eval", queue="report_task")
def question_section_eval_task(content: str, checker_id: str) -> dict:
    """Celery task for question section evaluation"""
    try:
        return QuestionSection.eval(content, checker_id)
    except Exception as e:
        logger.error(f"Task failed: {str(e)}")
        return {"type": "question", "categories": []}

class QuestionSection:
    @staticmethod
    def store_questions(checker_id: str, categories: list):
        """Store generated questions in the database"""
        try:
            # First, delete existing questions for this checker_id
            PropertyQuestion.objects.filter(checker_id=checker_id).delete()

            # Store new questions
            for category in categories:
                category_name = category.get("category")
                questions = category.get("questions", [])

                for q in questions:
                    PropertyQuestion.objects.create(
                        checker_id=checker_id,
                        category=category_name,
                        question=f"{q.get('question')}"
                    )
                logger.info(f"✓ Stored {len(questions)} questions for category: {category_name}")

        except Exception as e:
            logger.error(f"Error storing questions: {e}")
            return

    @staticmethod
    def eval(content: str, checker_id: str) -> dict:
        try:
            # Generate questions based on content
            messages = [
                {"role": "system", "content": settings.QUESTION_PROMPT},
                {"role": "user", "content": f"Generate critical questions about this property: {content}"}
            ]

            response = client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=messages,
                tools=QUESTION_TOOLS,
                tool_choice="required",
                temperature=0
            )

            analysis = extract_tool_response(response, {"categories": []}, context="critical")

            # Store questions in database
            QuestionSection.store_questions(checker_id, analysis.get("categories", []))

            return {
                "type": "question",
                "categories": analysis.get("categories", [])
            }

        except Exception as e:
            logger.error(f"❌ Error in question generation: {str(e)}")
            return {"type": "question", "categories": []}