from django.conf import settings
from openai import AzureOpenAI
from apps.checker.helpers import extract_tool_response  # Update import
from apps.checker.models import PropertyRisk, RiskCategory, RiskType, Severity
from datetime import datetime
import json
import logging
import re
from celery import shared_task  # Add this import
from homescore.celery import app  # Add this import

logger = logging.getLogger("homescore")

client = AzureOpenAI(
    api_key=settings.AZURE_OPENAI_API_KEY,
    api_version=settings.AZURE_OPENAI_API_VERSION,
    azure_endpoint=settings.AZURE_OPENAI_API_ENDPOINT
)


RISK_TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "identify_risks",
            "description": "Identify age-based property risks",
            "parameters": {
                "type": "object",
                "properties": {
                    "risks": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "category": {
                                    "type": "string",
                                    "enum": [cat.value for cat in RiskCategory]
                                },
                                "description": {
                                    "type": "string",
                                    "description": "Brief explanation of the risk and its implications"
                                }
                            },
                            "required": ["category", "description"]
                        }
                    }
                },
                "required": ["risks"]
            }
        }
    }
]

@app.task(name="age_section_eval", queue="report_task")
def age_section_eval_task(content: str, checker_id: str) -> dict:
    """Celery task for age section evaluation"""
    try:
        return AgeSection.eval(content, checker_id)
    except Exception as e:
        logger.error(f"Task failed: {str(e)}")
        return {"type": "age", "risks": []}
class AgeSection:
    @staticmethod
    def extract_year_built(text):
        """Extract year built from summary text using multiple patterns"""
        # List of possible year patterns
        year_patterns = [
            r"Year Built:\s*(\d{4})",  # Year Built: 1970
            r"(\d{4})\s*Year Built",   # 1970 Year Built
            r"Built in\s*(\d{4})",     # Built in 1970
            r"Construction Year:\s*(\d{4})",  # Construction Year: 1970
            r"(\d{4})\s*(?=Year)",     # 1970Year (no space)
        ]


        for pattern in year_patterns:
            match = re.search(pattern, text)
            if match:
                year = match.group(1)
                logger.info(f"Found year {year}")
                return year

        # If no pattern matches, try to find any 4-digit number between 1800 and current year
        current_year = datetime.now().year
        years = re.findall(r"\b(19\d{2}|20[0-2]\d)\b", text)
        for year in years:
            year_int = int(year)
            if 1800 <= year_int <= current_year:
                logger.info(f"Found year {year} using general number pattern")
                return year


    @staticmethod
    def analyze_risks(context: str, year_built: str) -> list:
        try:
            messages = [
                {"role": "system", "content": settings.RISK_PROMPT.format(context=f"This property was built in {year_built}. {context}")},
                {"role": "user", "content": "Please identify the applicable risks for this property."}
            ]

            response = client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=messages,
                tools=RISK_TOOLS,
                tool_choice="required",
                temperature=0
            )

            analysis = extract_tool_response(response, {"risks": []}, context="critical")
            return analysis.get("risks", [])

        except Exception as e:
            logger.error(f"Error in risk analysis: {str(e)}")
            return []

    @staticmethod
    def eval(content: str, checker_id: str) -> dict:
        try:

            # Extract year built
            year_built = AgeSection.extract_year_built(content)
            logger.info(f"Year built: {year_built}")
            if not year_built:
                logger.error("Could not find year built in content")
                return {
                    "type": "age",
                    "risks": []
                }

            # Analyze risks based on age
            risks = AgeSection.analyze_risks(content, year_built)
            logger.info(f"Found {len(risks)} {risks} based on property age")


            # Store risks in PropertyRisk
            for risk in risks:
                PropertyRisk.objects.update_or_create(
                    checker_id=checker_id,
                    section="age",
                    category=risk["category"],
                    defaults={
                        "description": risk["description"],
                        "risk_type": RiskType.MAINTENANCE.value,
                        "severity": Severity.MEDIUM.value
                    }
                )

            return {
                "type": "age",
                "risks": risks
            }

        except Exception as e:
            logger.error(f"Error in age section evaluation: {str(e)}")
            raise
