from django.conf import settings
from openai import AzureOpenAI
from apps.checker.helpers import extract_tool_response  # Update import
from apps.checker.models import PropertyOpportunity
import json
import logging
import os
from celery import shared_task
from homescore.celery import app

logger = logging.getLogger("homescore")

client = AzureOpenAI(
    api_key=settings.AZURE_OPENAI_API_KEY,
    api_version=settings.AZURE_OPENAI_API_VERSION,
    azure_endpoint=settings.AZURE_OPENAI_API_ENDPOINT
)

OPPORTUNITY_TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "analyze_opportunities",
            "description": "Identify potential improvement opportunities in different categories",
            "parameters": {
                "type": "object",
                "properties": {
                    "opportunities": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "category": {
                                    "type": "string",
                                    "enum": ["Easy Repairs","Renovation Potential","Energy Efficiency Updates"]
                                },
                                "suggestions": {
                                    "type": "array",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "title": {
                                                "type": "string",
                                                "description": "Brief title of the suggestion"
                                            },
                                            "description": {
                                                "type": "string",
                                                "description": "Detailed explanation of the opportunity"
                                            },
                                            "estimated_cost": {
                                                "type": "string",
                                                "description": "Estimated cost range for implementing this suggestion"
                                            },
                                            "potential_value_add": {
                                                "type": "string",
                                                "description": "Potential value addition to the property"
                                            }
                                        },
                                        "required": ["title", "description", "estimated_cost", "potential_value_add"]
                                    },
                                    "minItems": 2,
                                    "maxItems": 4
                                }
                            },
                            "required": ["category", "suggestions"]
                        }
                    }
                },
                "required": ["opportunities"]
            }
        }
    }
]

@app.task(name="opportunity_section_eval", queue="report_task")
def opportunity_section_eval_task(content: str, checker_id: str) -> dict:
    """Celery task for opportunity section evaluation"""
    try:
        return OpportunitySection.eval(content, checker_id)
    except Exception as e:
        logger.error(f"Task failed: {str(e)}")
        return {"type": "opportunity", "opportunities": []}
class OpportunitySection:
    @staticmethod
    def store_opportunities(checker_id: str, opportunities: list):
        """Store opportunities in the database"""
        try:
            # First, delete existing opportunities for this checker_id
            PropertyOpportunity.objects.filter(checker_id=checker_id).delete()
            
            # Store new opportunities
            for opportunity in opportunities:
                category = opportunity.get('category')
                suggestions = opportunity.get('suggestions', [])
                
                for suggestion in suggestions:
                    PropertyOpportunity.objects.create(
                        checker_id=checker_id,
                        category=category,
                        title=suggestion.get('title'),
                        description=suggestion.get('description'),
                        estimated_cost=suggestion.get('estimated_cost'),
                        potential_value_add=suggestion.get('potential_value_add')
                    )
                logger.info(f"✓ Stored {len(suggestions)} opportunities for category: {category}")
                
        except Exception as e:
            logger.error(f"❌ Error storing opportunities: {e}")
            raise

    @staticmethod
    def eval(content: str, checker_id: str) -> dict:
        try:
            messages = [
                {"role": "system", "content": settings.OPPORTUNITY_PROMPT},
                {"role": "user", "content": f"Analyze this property and identify improvement opportunities: {content}"}
            ]

            response = client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=messages,
                tools=OPPORTUNITY_TOOLS,
                tool_choice="required",
                temperature=0
            )

            analysis = extract_tool_response(response, {"opportunities": []})
            
            # Store opportunities in database
            OpportunitySection.store_opportunities(checker_id, analysis.get('opportunities', []))

            return {
                'type': 'opportunity',
                'opportunities': analysis.get('opportunities', [])
            }

        except Exception as e:
            logger.error(f"Error in opportunity analysis: {str(e)}")
            return {"type": "opportunity", "opportunities": []}