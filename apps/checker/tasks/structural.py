import os
import json
import logging
from django.conf import settings

from openai import AzureOpenAI
from langchain_openai import OpenAIEmbeddings, AzureOpenAIEmbeddings

from apps.checker.models import PropertyIssues
from apps.checker.llm_utils import basic_llm_response
from apps.checker.helpers import get_text, get_index_summary, parse_and_store_json_summary, split_and_upload_to_pinecone, extract_tool_response, wait_for_docs

logger = logging.getLogger("homescore")

client = AzureOpenAI(
    api_key=settings.AZURE_OPENAI_API_KEY,
    api_version=settings.AZURE_OPENAI_API_VERSION,
    azure_endpoint=settings.AZURE_OPENAI_API_ENDPOINT
)


class StructuralSection:
    @staticmethod
    def eval(file_url: str, checker_id: str) -> dict:
        logger.info(f"🔄 Starting structural analysis for checker_id: {checker_id}")
        
        try:
            # Extract text
            logger.info("📄 Extracting text from document")
            pages, docs = get_text(checker_id, file_url)
            if not pages:
                logger.error("❌ No content extracted from document")
                return {"type": "structural", "issues": []}
            logger.info(f"✅ Extracted {len(pages)} pages of content")

            # Process and upload to Pinecone
            logger.info("🔍 Initializing embeddings for document processing")
            embeddings = AzureOpenAIEmbeddings(model=settings.AZURE_OPENAI_EMBEDDINGS_MODEL)
            
            logger.info("📤 Uploading documents to Pinecone")
            split_and_upload_to_pinecone(
                docs=docs,
                embeddings=embeddings,
                index_name=settings.PINECONE_INFOPAY_INDEX
            )
            logger.info("✅ Documents uploaded to Pinecone successfully")
            
            # Wait for documents to be indexed before analysis
            if not wait_for_docs(settings.PINECONE_INFOPAY_INDEX, embeddings, checker_id):
                logger.error("❌ Documents not indexed in Pinecone within the expected time")
                return Response(
                    {"error": "Document indexing timed out"}, 
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
                
            # Get and process summary
            logger.info("🤖 Generating document summary")
            raw_summary = get_index_summary(checker_id, file_url)
            if not raw_summary:
                logger.warning("⚠️ No summary generated")
                return {"type": "structural", "issues": []}
            
            summary = extract_tool_response(raw_summary, {"issues": []})
            
            logger.info("💾 Storing analysis results")
            logger.info(f"📝 Parsing and storing {type(summary)} summary")
            parse_and_store_json_summary(checker_id, summary)
            
            issues = summary.get("issues", [])
            logger.info(f"✅ Analysis complete - found {len(issues)} issues")
            
            return {
                "type": "structural",
                "issues": issues
            }
            
        except Exception as e:
            logger.error(f"❌ Structural analysis failed: {str(e)}", exc_info=True)
            return {"type": "structural", "issues": []}