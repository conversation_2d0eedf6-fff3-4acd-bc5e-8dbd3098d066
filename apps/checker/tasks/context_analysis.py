from django.conf import settings
from openai import AzureOpenAI
from apps.checker.helpers import extract_tool_response
from apps.checker.models import PropertyQuestion, PropertyOpportunity
import logging
from homescore.celery import app
from ..utils import task_timer, update_report_status_after_task

logger = logging.getLogger("homescore")

client = AzureOpenAI(
    api_key=settings.AZURE_OPENAI_API_KEY,
    api_version=settings.AZURE_OPENAI_API_VERSION,
    azure_endpoint=settings.AZURE_OPENAI_API_ENDPOINT
)

CONTEXT_TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "analyze_context",
            "description": "Analyze property report to generate questions and identify opportunities",
            "parameters": {
                "type": "object",
                "properties": {
                    "questions": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "category": {
                                    "type": "string",
                                    "enum": [
                                        "Safety Concerns",
                                        "Structural Integrity",
                                        "Investment Quality",
                                        "Environmental Risks"
                                    ]
                                },
                                "questions": {
                                    "type": "array",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "question": {"type": "string"},
                                            "importance": {
                                                "type": "string",
                                                "enum": ["high", "medium", "low"]
                                            }
                                        },
                                        "required": ["question", "importance"]
                                    },
                                    "maxItems": 2
                                }
                            },
                            "required": ["category", "questions"]
                        },
                        "maxItems": 3
                    },
                    "opportunities": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "category": {
                                    "type": "string",
                                    "enum": ["Easy Repairs", "Renovation Potential", "Energy Efficiency Updates"]
                                },
                                "suggestions": {
                                    "type": "array",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "title": {
                                                "type": "string",
                                                "description": "Brief title of the suggestion"
                                            },
                                            "description": {
                                                "type": "string",
                                                "description": "Detailed explanation of the opportunity"
                                            },
                                            "estimated_cost": {
                                                "type": "string",
                                                "description": "Estimated cost range for implementing this suggestion"
                                            },
                                            "potential_value_add": {
                                                "type": "string",
                                                "description": "Potential value addition to the property"
                                            }
                                        },
                                        "required": ["title", "description", "estimated_cost", "potential_value_add"]
                                    },
                                    "minItems": 2,
                                    "maxItems": 4
                                }
                            },
                            "required": ["category", "suggestions"]
                        }
                    }
                },
                "required": ["questions", "opportunities"]
            }
        }
    }
]

CONTEXT_PROMPT = """Analyze the property report in two aspects:

1. Critical Questions:
Generate two user-friendly questions for each category, but only show questions for up to 3 categories in total.
Use specific details from the property report to make questions relevant and actionable.

Categories and their focused areas:
- Safety Concerns: Questions about hazardous materials or other safety issues identified in the report
- Structural Integrity: Questions about plumbing, electrical systems, and foundation conditions based on inspection findings
- Investment Quality: Questions about repair costs, potential resale value, and necessary improvements
- Environmental Risks: Questions about flood zones, natural hazards, or environmental concerns noted in the report

For each category used:
1. Generate two user-friendly questions that reference specific details from the property report
2. Include importance level (high/medium/low) for each question based on potential impact

2. Improvement Opportunities:
Analyze the property details and identify high-impact opportunities for improvement.
Focus on specific, actionable suggestions backed by evidence from the property report.

Categorize opportunities into:

1. Quick Wins (Easy Repairs):
   - Focus on fast, cost-effective improvements
   - Target visible, high-impact areas
   - Identify specific fixtures, features, or cosmetic issues mentioned in the report

2. Major Value-Add Projects:
   - Highlight substantial renovation opportunities
   - Use specific details from the report (square footage, room conditions, layout potential)
   - Consider unfinished or outdated spaces mentioned

3. Energy & Efficiency Upgrades:
   - Identify opportunities to reduce operating costs
   - Focus on outdated systems or inefficient features mentioned
   - Consider both immediate and long-term energy-saving potential

For each suggestion:
- Cite specific evidence from the report
- Provide realistic cost estimates
- Estimate potential value addition
- Consider local market trends and buyer preferences

Keep responses factual and actionable, focused on evidence from the property report.
"""

@app.task(name="context_analysis", queue="report_task")
def context_analysis_task(content: str, checker_id: str) -> dict:
    """Combined task for questions and opportunities analysis"""
    logger.info(f"Starting Context Analysis for checker_id {checker_id}")

    with task_timer("Context Analysis", checker_id):
        try:
            messages = [
                {"role": "system", "content": CONTEXT_PROMPT},
                {"role": "user", "content": content}
            ]

            response = client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=messages,
                tools=CONTEXT_TOOLS,
                tool_choice="required",
                temperature=0
            )

            analysis = extract_tool_response(response, {"questions": [], "opportunities": []})

            # Store questions
            PropertyQuestion.objects.filter(checker_id=checker_id).delete()
            for category in analysis.get("questions", []):
                category_name = category.get("category")
                for q in category.get("questions", []):
                    PropertyQuestion.objects.create(
                        checker_id=checker_id,
                        category=category_name,
                        question=f"{q.get('question')} (Importance: {q.get('importance').capitalize()})"
                    )

            # Store opportunities
            PropertyOpportunity.objects.filter(checker_id=checker_id).delete()
            for opportunity in analysis.get("opportunities", []):
                category = opportunity.get("category")
                for suggestion in opportunity.get("suggestions", []):
                    PropertyOpportunity.objects.create(
                        checker_id=checker_id,
                        category=category,
                        title=suggestion.get("title"),
                        description=suggestion.get("description"),
                        estimated_cost=suggestion.get("estimated_cost"),
                        potential_value_add=suggestion.get("potential_value_add")
                    )

            # Update the report status to reflect completion of this task
            update_report_status_after_task(checker_id, 'context_analysis')

            # Return results in original format
            return {
                "question": {
                    "type": "question",
                    "categories": analysis.get("questions", [])
                },
                "opportunity": {
                    "type": "opportunity",
                    "opportunities": analysis.get("opportunities", [])
                }
            }

        except Exception as e:
            logger.error(f"Error in context analysis: {str(e)}")
            # Ensure status is updated even on failure
            update_report_status_after_task(checker_id, 'context_analysis')
            return {
                "question": {"type": "question", "categories": []},
                "opportunity": {"type": "opportunity", "opportunities": []}
            }
