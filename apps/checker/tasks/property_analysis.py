from django.conf import settings
from openai import AzureOpenAI
from apps.checker.helpers import extract_tool_response
from apps.checker.models import PropertyRisk, PermitAnalysis, RiskCategory, RiskType, Severity
from ..utils import task_timer, update_report_status_after_task
from .age import AgeSection
from .permit import PermitSection
import logging
from datetime import datetime
from homescore.celery import app
import re

logger = logging.getLogger("homescore")

client = AzureOpenAI(
    api_key=settings.AZURE_OPENAI_API_KEY,
    api_version=settings.AZURE_OPENAI_API_VERSION,
    azure_endpoint=settings.AZURE_OPENAI_API_ENDPOINT
)

COMBINED_TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "analyze_property",
            "description": "Analyze property age and permits to identify risks and significant renovations",
            "parameters": {
                "type": "object",
                "properties": {
                    "age_risks": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "category": {
                                    "type": "string",
                                    "enum": [cat.value for cat in RiskCategory]
                                },
                                "description": {
                                    "type": "string",
                                    "description": "Brief explanation of the risk and its implications"
                                }
                            },
                            "required": ["category", "description"]
                        }
                    },
                    "significant_permits": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "date": {"type": "string"},
                                "type": {"type": "string"},
                                "description": {"type": "string"},
                                "value": {"type": "string"},
                                "importance": {
                                    "type": "string",
                                    "description": "Why this permit is important for a buyer"
                                }
                            },
                            "required": ["date", "type", "description", "importance"]
                        }
                    },
                    "permit_risks": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "risk_type": {"type": "string", "enum": [rt.value for rt in RiskType]},
                                "description": {"type": "string"},
                                "severity": {"type": "string", "enum": [s.value for s in Severity]}
                            },
                            "required": ["risk_type", "description", "severity"]
                        }
                    }
                },
                "required": ["age_risks", "significant_permits", "permit_risks"]
            }
        }
    }
]

PROPERTY_PROMPT = """
Analyze the property information provided, focusing on both age-related risks and building permits:

1. Age-Related Risks:
Based on the construction year, identify potential risks from these specific criteria:

For each risk, provide:
a) Clear identification of the risk
b) Where to look for signs of the risk (visual indicators, locations)
c) Potential health or safety implications
d) Recommended next steps (inspection, testing, remediation)
e) Professional help needed (type of specialist to consult)

Specific risks to analyze by year:
- Asbestos (pre-1980): Found in insulation, tiles, popcorn ceilings, siding
- Lead Paint (pre-1978): Common on walls, trim, window frames
- Knob-and-Tube Wiring (1880-1930): Check exposed wiring in basements, attics
- Galvanized Plumbing (pre-1960s): Look for discolored water, low pressure
- Cast Iron Sewer Pipes (pre-1970): Inspect for cracks, rust, backup issues
- Single-Pane Windows (pre-1970): Check for drafts, condensation, efficiency

Only include risks that apply based on the construction year, with detailed explanations for each risk identified.

2. Building Permits:
- Identify and summarize significant permits
- Focus on major system upgrades and improvements
- Highlight their importance for potential buyers

3. Permit-Related Risks:
- Identify structural issues, system problems, or safety concerns
- Consider flooding risks, repair needs, and significant maintenance issues

Provide clear, actionable insights that would be valuable for a prospective buyer.
"""

def determine_age_risks(year_built: int) -> list:
    """Determine risks based on construction year"""
    risks = []
    current_year = datetime.now().year

    THRESHOLDS = {
        ("ASBESTOS", 1980): "Common in homes built before 1980, found in insulation, tiles, and other materials.",
        ("LEAD", 1978): "Homes built before 1978 likely used lead-based paint, posing health risks.",
        ("WIRING", 1930): "Knob-and-tube wiring common in homes from 1880-1930, potential fire hazard.",
        ("PLUMBING", 1960): "Pre-1960s homes often have galvanized plumbing, prone to corrosion.",
        ("SEWER", 1970): "Homes before 1970 may have cast iron sewer pipes, subject to deterioration.",
        ("WINDOWS", 1970): "Pre-1970 homes often have single-pane windows, less energy efficient."
    }

    if year_built and year_built <= current_year:
        for (risk_type, threshold_year), description in THRESHOLDS.items():
            if year_built < threshold_year:
                risks.append({
                    "category": getattr(RiskCategory, risk_type).value,
                    "description": description
                })
    return risks


@app.task(name="property_analysis", queue="report_task")
def property_analysis_task(content: str, checker_id: str) -> dict:
    """Combined task for property analysis including both age and permit evaluation"""
    logger.info(f"Starting Property Analysis for checker_id {checker_id}")

    with task_timer("Property Analysis", checker_id):
        try:
            # Extract year built (keep existing year extraction logic)
            year_patterns = [
                r"Year Built:\s*(\d{4})",
                r"(\d{4})\s*Year Built",
                r"Built in\s*(\d{4})",
                r"Construction Year:\s*(\d{4})",
                r"(\d{4})\s*(?=Year)"
            ]

            year_built = None
            for pattern in year_patterns:
                match = re.search(pattern, content)
                if match:
                    year_built = int(match.group(1))
                    logger.info(f"Found year {year_built}")
                    break

            # Combine the content with year built information
            analysis_content = f"This property was built in {year_built}. {content}" if year_built else content

            # Get combined analysis in single API call
            messages = [
                {"role": "system", "content": PROPERTY_PROMPT},
                {"role": "user", "content": analysis_content}
            ]

            response = client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=messages,
                tools=COMBINED_TOOLS,
                tool_choice="required",
                temperature=0
            )

            analysis = extract_tool_response(response, {
                "age_risks": [],
                "significant_permits": [],
                "permit_risks": []
            }, context="critical")

            # Store age risks
            for risk in analysis.get("age_risks", []):
                PropertyRisk.objects.update_or_create(
                    checker_id=checker_id,
                    section="age",
                    category=risk["category"],
                    defaults={
                        "description": risk["description"],
                        "risk_type": RiskType.MAINTENANCE.value,
                        "severity": Severity.MEDIUM.value
                    }
                )

            # Store permit analysis
            for permit in analysis.get("significant_permits", []):
                formatted_date = PermitSection.convert_date_format(permit.get("date"))
                PermitAnalysis.objects.update_or_create(
                    checker_id=checker_id,
                    permit_date=formatted_date,
                    permit_type=permit.get("type"),
                    defaults={
                        "description": permit.get("description"),
                        "value": permit.get("value"),
                        "importance": permit.get("importance")
                    }
                )

            # Store permit risks
            for risk in analysis.get("permit_risks", []):
                category = PermitSection.map_risk_type_to_category(risk.get("risk_type"))
                PropertyRisk.objects.update_or_create(
                    checker_id=checker_id,
                    section="permit",
                    category=category,
                    defaults={
                        "description": risk.get("description"),
                        "risk_type": risk.get("risk_type"),
                        "severity": risk.get("severity")
                    }
                )

            # Update the report status to reflect completion of this task
            update_report_status_after_task(checker_id, 'property_analysis')

            # Return in the expected format for both sections
            return {
                "age": {
                    "type": "age",
                    "risks": analysis.get("age_risks", [])
                },
                "permit": {
                    "type": "permit",
                    "permits": analysis.get("significant_permits", []),
                    "risks": analysis.get("permit_risks", [])
                }
            }

        except Exception as e:
            logger.error(f"Error in property analysis: {str(e)}")
            # Ensure status is updated even on failure
            update_report_status_after_task(checker_id, 'property_analysis')
            return {
                "age": {"type": "age", "risks": []},
                "permit": {"type": "permit", "permits": [], "risks": []}
            }
