from bs4 import BeautifulSoup
import re

def extract_sections(content):
    sections = {
        'section_top': '',
        'section_summary': '',
        'section_property_details': '',
        'section_building_permits': '',
        'section_images': []
    }

    soup = BeautifulSoup(content, "html.parser")

    #For debugging
    # processed_content = soup.get_text()
    # print(processed_content)

    #Get images
    section = soup.find('div', {'class': 'r-section', 'id': 'section_images'})
    if section:
        # Find all img tags within the section
        images = section.find_all('img')
        for img in images:
            # Get the src attribute of each image
            image_url = img.get('data-src')
            if image_url:
                sections['section_images'].append(image_url)

    for section_label in sections.keys():
        if section_label != 'section_images':  # Skip images since we handled them separately
            current_section = soup.find('div', {'class': 'r-section', 'id': section_label})

            if current_section:
                text = re.sub(r'\n{2,}', '\n', current_section.text)  # Replace 2 or more newlines with single newline
                sections[section_label] = text
            else:
                sections[section_label] = 'Section not found'

    return sections