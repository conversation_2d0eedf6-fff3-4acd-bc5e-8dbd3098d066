from django.urls import path

from . import views
from .chat import NewChat<PERSON>essage<PERSON>he<PERSON>,GetMessageResponseAPI, ChatAPI

app_name = "internal"

#Not sure why /checker doesn't work he
urlpatterns = [
    path("<str:section>/<int:checker_id>", views.Checker.as_view(), name="all sections"),
    path("reports/list/", views.PropertyCardDataList.as_view(), name="checker-report-list"),
    path("reports/upload", views.DocumentProcessView.as_view(), name="checker-report-upload"),
    path("reports/issues/upload_doc", views.InspectionReportView.as_view(), name="checker-report"),
    path("reports/info/<int:checker_id>", views.PropertyCardDataRetrieval.as_view(), name="checker-report-retrieval"),
    path("chat/list/", ChatAPI.as_view(), name="list_chat"),
    path("chat/<str:checker_id>/new_message/", NewChatMessageChecker.as_view(), name="api_new_chat_message_checker"),
    path(
        "chat/<int:chat_id>/get_response/<slug:task_id>/",
        GetMessageResponseAPI.as_view(),
        name="api_get_message_response",
    ),
]
