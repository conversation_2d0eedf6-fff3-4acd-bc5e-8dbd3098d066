
<!doctype html>
<html lang="en-US">
<head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5">
    <title></title>
        <link type="image/x-icon" href="/img/favicon.png" rel="shortcut icon">
<link href="/css/report.ksink.css?v=1.31" rel="stylesheet">
<link type="text/css" href="/css/dashboard.css?v=1.51" rel="stylesheet">
<link href="/css/footersoc.css" rel="stylesheet">
<script src="/js/jquery-3.6.4.min.js"></script>
<script src="/packages/markjs/mark.js-master/dist/jquery.mark.js"></script>    <!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-MLJRRBRG');</script>
<!-- End Google Tag Manager -->
    <script type="application/ld+json">
{
    "@context" : "https://schema.org",
    "@type" : "WebSite",
    "name" : "PropertyChecker",
    "alternateName" : "PropertyChecker.com",
    "url" : "https://propertychecker.com/"
}
</script></head>
<body id="top">
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MLJRRBRG"
                  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->

<meta name="robots" content="noindex,nofollow"><style>
    .col.ctr {
        display:none;
    }
</style>

<!--Error Details message-->
<!--"Too Many Results", "Insufficient information to perform search" and other messages from vendor-->
<!--	-->
<body class="members">
    <div class="site-container" id="premiumPropertyReportContent">
        <main id="main" class="site-content">
            <div class="wrapper">
                <div class="report report-property">
                                                                    <div class="r-header">
    <div class="row">
        <div class="col ttl">
                        <h1>Property Report for <span class="color-special search-for">84 Wellesley St, Weston, MA 02493</span></h1>
                    </div>
        <div class="col ctr">
                            <a class="ctr-pdf" href="/reportPdf?id=67589dbf435dd99e6303c6c0&isDownloadFile=1" title="Download PDF">Download PDF</a>
                        <a class="ctr-print" href="javascript:void(0)" onclick="window.print()" title="Print Page">Print Page</a>

                        <div id="sendShareables" class="lbox-container">
                <div class="lbox-container-inn lg">
                    <div class="lbox-head">
                        <h1 class="h3 center">Email This Property</h1>
                    </div>
                    <div class="lbox-body">
                        <div class="form mw-sm mxa">
                            <div class="notify">
                                <div class="row">
                                    <div class="col">
                                        <div class="itm danger">
                                            <div class="icn">
                                                <img src="/img/icns/fill/16/alert.svg">
                                            </div>
                                            <div class="txt">Please enter up to five valid receiver email addresses, separated by commas.</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col">
                                    <div class="form-input">
                                        <label class="form-label" for="recipientEmail">Recipient's Email <span class="color-warning">*</span></label>
                                        <input class="form-control md" id="recipientEmail" name="emails" value="" type="email" placeholder="Enter email">
                                        <div class="form-note">Separate multiple addresses with a comma.</div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col">
                                    <div class="form-input">
                                        <label class="form-label" for="yourEmail">Your Email <span class="color-warning">*</span></label>
                                        <input class="form-control md" id="yourEmail" name="fromEmail" value="<EMAIL>" type="email" placeholder="Enter email">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col">
                                    <div class="form-input">
                                        <label class="form-label" for="message">Include Message (optional):</label>
                                        <textarea class="form-control md" id="message" name="message" rows="" cols="">Check out this great find on PropertyChecker!</textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col cta">
                                    <button class="btn md arw send-shareable" type="button">Send Email</button>
                                </div>
                                <div class="col cta">
                                    <div class="get-link">
                                        <div class="lnk">
                                            <strong>
                                                <a href="javascript:;" style="color:#007BFF; display: inherit; font-size: inherit; filter:none" class="copy-share-link" data-link="">Get shareable link</a>
                                            </strong>
                                        </div>
                                        <div class="msg msg-link-copy" style="opacity:0">Link copied to clipboard</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <a class="lbox-close" href="javascript:void(0)" onclick="lbHide('sendShareables')" title="Close">Close</a>
                </div>
            </div>
                    </div>

    </div>
</div>        
                                                                        <div class="r-section" id="section_top">                    <div class="r-sum">
    <div class="row">
                                    <div class="col">
                    <div class="cnt">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/color/60/bedbath.svg" alt="" width="60" height="60">
                        </span>
                        <span class="txt">
                            <span class="val">4br / 3ba</span>
                            <span class="ttl">Beds/Baths</span>
                        </span>
                    </div>
                </div>
                            <div class="col">
                    <div class="cnt">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/color/60/home_magnifier.svg" alt="" width="60" height="60">
                        </span>
                        <span class="txt">
                            <span class="val">3,491 sqft</span>
                            <span class="ttl">Living Area</span>
                        </span>
                    </div>
                </div>
                            <div class="col">
                    <div class="cnt">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/color/60/map.svg" alt="" width="60" height="60">
                        </span>
                        <span class="txt">
                            <span class="val">1 acres</span>
                            <span class="ttl">Lot Size</span>
                        </span>
                    </div>
                </div>
                            <div class="col">
                    <div class="cnt">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/color/60/building.svg" alt="" width="60" height="60">
                        </span>
                        <span class="txt">
                            <span class="val">1805</span>
                            <span class="ttl">Year Built</span>
                        </span>
                    </div>
                </div>
                            <div class="col">
                    <div class="cnt">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/color/60/calendar.svg" alt="" width="60" height="60">
                        </span>
                        <span class="txt">
                            <span class="val">30yr 4mo</span>
                            <span class="ttl">Ownership Length</span>
                        </span>
                    </div>
                </div>
                            <div class="col">
                    <div class="cnt">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/color/60/home_arrow_up.svg" alt="" width="60" height="60">
                        </span>
                        <span class="txt">
                            <span class="val">$1,812,615</span>
                            <span class="ttl">Market Value</span>
                        </span>
                    </div>
                </div>
                        </div>
</div>
        
                                            </div>                            <div class="r-section" id="section_summary">                    <div class="hdr">
    <h2 class="r-h2">
        <span class="icn">
            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/32/home.svg" alt="" width="32" height="32">
        </span>
        <span class="txt">Summary</span>

    </h2>
    </div>
<div class="bdy">
    <div class="sec">
                <div class="r-cnt">
            <div class="row">
                                    <div class="col" id="summaryMapStaticStreetViewContainer">
                        <div class="r-map" style="background-color: transparent">
                            <a href="https://www.google.com/maps/place/84+WELLESLEY+ST%2C+WESTON%2C+MA+02493" target="_blank">
                                <img
                                    id="summaryMapStaticImg"
                                    class="display-static-map-lazy"
                                    alt="Map"
                                    style="border:0; margin: 0 auto;background-image:url('/tspecV2/PremiumPropertyReport/shared/img/spinner.svg');background-repeat:no-repeat;background-position:center center;background-size:20%"
                                    src="/tspecV2/PremiumPropertyReport/shared/img/noimage.svg"
                                                                    >
                                <!--                            <div id="summaryMapPanorama"></div>-->
                            </a>
                        </div>
                    </div>
                    <div class="col" id="summaryMapContainer">
                        <div class="r-map">
                            <a href="https://www.google.com/maps/place/84+WELLESLEY+ST%2C+WESTON%2C+MA+02493" target="_blank">
<!--                                <div id="summaryMap"></div>-->
                                <img
                                    id="summaryMap"
                                    class="display-static-map-lazy"
                                    alt="Map"
                                    style="border:0; margin: 0 auto;background-image:url('/tspecV2/PremiumPropertyReport/shared/img/spinner.svg');background-repeat:no-repeat;background-position:center center;background-size:20%"
                                    src="/tspecV2/PremiumPropertyReport/shared/img/noimage.svg"
                                />
                            </a>
                        </div>
                    </div>
                            </div>
        </div>
        
        <div class="r-list">
                            <div class="row">
                                            <div class="col">
                            <div class="icn">
                                <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/pin.svg" alt="" width="16" height="16">
                            </div>
                            <div class="txt">
                                <div class="ttl">Property Address:</div>
                                <div class="val">84 WELLESLEY ST</div>
                            </div>
                        </div>
                                            <div class="col">
                            <div class="icn">
                                <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/usa.svg" alt="" width="16" height="16">
                            </div>
                            <div class="txt">
                                <div class="ttl">City, State, Zipcode:</div>
                                <div class="val">WESTON, MA 02493</div>
                            </div>
                        </div>
                                            <div class="col">
                            <div class="icn">
                                <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/latitude.svg" alt="" width="16" height="16">
                            </div>
                            <div class="txt">
                                <div class="ttl">Latitude:</div>
                                <div class="val">42.360438</div>
                            </div>
                        </div>
                                            <div class="col">
                            <div class="icn">
                                <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/longitude.svg" alt="" width="16" height="16">
                            </div>
                            <div class="txt">
                                <div class="ttl">Longitude:</div>
                                <div class="val">-71.292809</div>
                            </div>
                        </div>
                                    </div>
                            <div class="row">
                                            <div class="col">
                            <div class="icn">
                                <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/flag.svg" alt="" width="16" height="16">
                            </div>
                            <div class="txt">
                                <div class="ttl">County:</div>
                                <div class="val">MIDDLESEX</div>
                            </div>
                        </div>
                                            <div class="col">
                            <div class="icn">
                                <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/document.svg" alt="" width="16" height="16">
                            </div>
                            <div class="txt">
                                <div class="ttl">APN:</div>
                                <div class="val">WEST M:033.0 L:0003 S:000.0</div>
                            </div>
                        </div>
                                            <div class="col">
                            <div class="icn">
                                <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/document_gavel.svg" alt="" width="16" height="16">
                            </div>
                            <div class="txt">
                                <div class="ttl">Legal Description:</div>
                                <div class="val">LOT:3 DIST:333 CITY/MUNI/TWP:WESTON</div>
                            </div>
                        </div>
                                            <div class="col">
                            <div class="icn">
                                <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/government.svg" alt="" width="16" height="16">
                            </div>
                            <div class="txt">
                                <div class="ttl">Legal Subdivision:</div>
                                <div class="val">N/A</div>
                            </div>
                        </div>
                                    </div>
                            <div class="row">
                                            <div class="col">
                            <div class="icn">
                                <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/home.svg" alt="" width="16" height="16">
                            </div>
                            <div class="txt">
                                <div class="ttl">Property Type:</div>
                                <div class="val">SINGLE FAMILY RESIDENCE</div>
                            </div>
                        </div>
                                            <div class="col">
                            <div class="icn">
                                <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/home_user.svg" alt="" width="16" height="16">
                            </div>
                            <div class="txt">
                                <div class="ttl">Owner Status:</div>
                                <div class="val">N/A</div>
                            </div>
                        </div>
                                            <div class="col">
                            <div class="icn">
                                <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/home_user.svg" alt="" width="16" height="16">
                            </div>
                            <div class="txt">
                                <div class="ttl">Owner Type:</div>
                                <div class="val">N/A</div>
                            </div>
                        </div>
                                            <div class="col">
                            <div class="icn">
                                <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/document.svg" alt="" width="16" height="16">
                            </div>
                            <div class="txt">
                                <div class="ttl">Trust Description:</div>
                                <div class="val">N/A</div>
                            </div>
                        </div>
                                    </div>
                            <div class="row">
                                            <div class="col">
                            <div class="icn">
                                <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/flag.svg" alt="" width="16" height="16">
                            </div>
                            <div class="txt">
                                <div class="ttl">Company Flag:</div>
                                <div class="val">NO</div>
                            </div>
                        </div>
                                            <div class="col">
                            <div class="icn">
                                <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/home_tag.svg" alt="" width="16" height="16">
                            </div>
                            <div class="txt">
                                <div class="ttl">Property Use:</div>
                                <div class="val">RESIDENTIAL</div>
                            </div>
                        </div>
                                            <div class="col">
                            <div class="icn">
                                <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/document.svg" alt="" width="16" height="16">
                            </div>
                            <div class="txt">
                                <div class="ttl">Stories:</div>
                                <div class="val">2</div>
                            </div>
                        </div>
                                            <div class="col">
                            <div class="icn">
                                <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/home.svg" alt="" width="16" height="16">
                            </div>
                            <div class="txt">
                                <div class="ttl">Structure Style:</div>
                                <div class="val">CONVENTIONAL</div>
                            </div>
                        </div>
                                    </div>
                                </div>

    </div>
</div>

    <style>
        #summaryMap, #summaryMapPanorama, #summaryMapStatic {
            min-width: 400px;
            height: 200px
        }
    </style>
    <!--lazy load map src-->
    <script src="/tspecV2/PremiumPropertyReport/shared/js/lazyLoadStaticMap.js"></script>
    <script>
        let staticMapUrl = 'https://maps.googleapis.com/maps/api/staticmap?center=84+WELLESLEY+ST%2C+WESTON%2C+MA+02493&sensor=false&size=585x200&zoom=15&markers=label%3A84+WELLESLEY+ST%2C+WESTON%2C+MA+02493%7C84+WELLESLEY+ST%2C+WESTON%2C+MA+02493&w=585&h=200&key=AIzaSyCtnZ-e78v92QBlLkUYVHXZPo913yldQu8';
        let mapData = JSON.parse('{"lat":42.360438,"lng":-71.292809}');
        document.addEventListener("DOMContentLoaded", function() {
            let staticStreetViewContainer = document.getElementById('summaryMapStaticStreetViewContainer');
            let summaryMapContainer = document.getElementById('summaryMapContainer');

            if (!mapData.hasOwnProperty('lat') || !mapData.hasOwnProperty('lng')) {
                console.error('No proper coordinates', mapData);
                staticStreetViewContainer.style.display = 'none';
                summaryMapContainer.style.display = 'none';
                return;
            }

            let summaryMapStaticImg = document.getElementById('summaryMapStaticImg');
            let mapId = 'summaryMap';

            // build streetview static meta url
            let apiKey = 'AIzaSyCtnZ-e78v92QBlLkUYVHXZPo913yldQu8';// StreetViewStaticMap should be allowed
            let staticStreetViewMetaUrl = new URL('https://maps.googleapis.com/maps/api/streetview/metadata?key=' + apiKey);
            let staticStreetViewMetaUrlParams = staticStreetViewMetaUrl.searchParams
            let staticStreetViewContainerOffsetWidth = staticStreetViewContainer.offsetWidth || 585;
            staticStreetViewMetaUrlParams.set('size', staticStreetViewContainerOffsetWidth + 'x200');
            staticStreetViewMetaUrlParams.set('location', mapData.lat + ',' + mapData.lng);

            // call meta
            // Street View Static API metadata requests are available at no charge. No quota is consumed when you request metadata.
            // Quota is only consumed when you load an image using the Street View Static API.
            let streetViewNotFound = false;
            fetch(staticStreetViewMetaUrl)
                .then(response => response.json())
                .then(results => {
                    if (results.status !== "OK") {
                        // hide static StreetView container
                        console.log('No Results', results);
                        streetViewNotFound = true; // not found
                        staticStreetViewContainer.style.display = 'none';

                        return;
                    }

                    // Build and Attach src to load Static StreetView
                    let staticStreetViewUrl = staticStreetViewMetaUrl.toString().replace(/\/metadata\?/, '\?');
                    loadMapImage(summaryMapStaticImg, staticStreetViewUrl);
                })
                .catch(er => {
                    console.log('staticStreetViewMetaUrl: ' + staticStreetViewMetaUrl);
                    console.error(er);
                    staticStreetViewContainer.style.display = 'none';
                })
                .finally(() => {
                    // Load static map to the specific container on full or half row depends on StreetView exists
                    let summaryMapContainerOffsetWidth = summaryMapContainer.offsetWidth || 585;
                    let scale = '1';
                    let height = '200';
                    let width = parseInt(summaryMapContainerOffsetWidth.toString());
                    if (width > 640) {
                        if ((width / 2) <= 640) {
                            width = Math.ceil(width / 2);
                            height = '100';
                            scale = '2';
                        } else {
                            width = '600';
                            height = '100';
                            scale = '2';
                        }
                    }
                    if (streetViewNotFound == true) {
                        let boundingRec = summaryMapContainer.getBoundingClientRect();
                        width = boundingRec.width;
                        height = boundingRec.height;
                    }
                    console.log (width + ' and ' + height)
                    let fullAddressCord = mapData.lat + ',' + mapData.lng;
                    let summaryMapImg = document.getElementById(mapId);
                    let staticMapUrl = new URL('https://maps.googleapis.com/maps/api/staticmap?key=' + apiKey);
                    let staticMapUrlParams =  staticMapUrl.searchParams;
                    staticMapUrlParams.set('size', width + 'x' + height)// max 640 but can be scaled x2
                    staticMapUrlParams.set('scale', scale)
                    staticMapUrlParams.set('zoom', '16')
                    if (streetViewNotFound == true) {
                        staticMapUrlParams.set('zoom', '15')
                        staticMapUrlParams.set('scale', '4')
                        summaryMapImg.style.backgroundSize = '10%';
                    }
                    staticMapUrlParams.set('center', mapData.lat + ',' + mapData.lng);
                    staticMapUrlParams.set('markers', 'color:red|size:small|' + fullAddressCord);
                    loadMapImage(summaryMapImg, staticMapUrl.toString());
                });

            function loadMapImage(el, src) {
                el.setAttribute('data-src', src);
            }
        });

    </script>
        
                                            </div>                            <div class="r-section" id="section_nav">                    <div class="bdy">
    <div class="sec">
        <div class="r-nav three-cols">
            <ul>
                                    <li>
                        <a class="smooth" href="#section_owners">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/home_user.svg" alt="" width="16" height="16">
                        </span>
                            <span class="txt">Owners</span>
                        </a>
                    </li>
                                    <li>
                        <a class="smooth" href="#section_foreclosures">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/home_gavel.svg" alt="" width="16" height="16">
                        </span>
                            <span class="txt">Foreclosures</span>
                        </a>
                    </li>
                                    <li>
                        <a class="smooth" href="#section_pre_foreclosures">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/home_dollar.svg" alt="" width="16" height="16">
                        </span>
                            <span class="txt">Pre-Foreclosures</span>
                        </a>
                    </li>
                                    <li>
                        <a class="smooth" href="#section_taxes">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/home_percent.svg" alt="" width="16" height="16">
                        </span>
                            <span class="txt">Taxes</span>
                        </a>
                    </li>
                                    <li>
                        <a class="smooth" href="#section_listings_history">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/listings.svg" alt="" width="16" height="16">
                        </span>
                            <span class="txt">Listings History</span>
                        </a>
                    </li>
                                    <li>
                        <a class="smooth" href="#section_values">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/home_dollar.svg" alt="" width="16" height="16">
                        </span>
                            <span class="txt">Values</span>
                        </a>
                    </li>
                                    <li>
                        <a class="smooth" href="#section_deeds">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/document_stamp.svg" alt="" width="16" height="16">
                        </span>
                            <span class="txt">Deeds</span>
                        </a>
                    </li>
                                    <li>
                        <a class="smooth" href="#section_property_details">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/home_magnifier.svg" alt="" width="16" height="16">
                        </span>
                            <span class="txt">Property Details</span>
                        </a>
                    </li>
                                    <li>
                        <a class="smooth" href="#section_loans">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/hand_dollar.svg" alt="" width="16" height="16">
                        </span>
                            <span class="txt">Loans</span>
                        </a>
                    </li>
                                    <li>
                        <a class="smooth" href="#section_building_permits">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/permit.svg" alt="" width="16" height="16">
                        </span>
                            <span class="txt">Building Permits</span>
                        </a>
                    </li>
                                    <li>
                        <a class="smooth" href="#section_liens_and_judgments">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/gavel.svg" alt="" width="16" height="16">
                        </span>
                            <span class="txt">Liens & Judgments</span>
                        </a>
                    </li>
                                    <li>
                        <a class="smooth" href="#section_residents">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/group.svg" alt="" width="16" height="16">
                        </span>
                            <span class="txt">Residents</span>
                        </a>
                    </li>
                                    <li>
                        <a class="smooth" href="#section_neighborhood">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/pin_map.svg" alt="" width="16" height="16">
                        </span>
                            <span class="txt">Neighborhood</span>
                        </a>
                    </li>
                                    <li>
                        <a class="smooth" href="#section_sex_offenders">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/avatar.svg" alt="" width="16" height="16">
                        </span>
                            <span class="txt">Sex Offenders</span>
                        </a>
                    </li>
                            </ul>
        </div>
    </div>
</div>

        
                                            </div>                            <div class="r-section" id="section_owners">                    <div class="hdr">
    <h2 class="r-h2">
        <span class="icn">
            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/32/home_user.svg" alt="" width="32" height="32">
        </span>
        <span class="txt">Owners</span>
    </h2>
</div>

<div class="bdy">
                        <div class="sec">
                <h3 class="r-h4 line">
                    <span class="txt">Owner's Name(s)</span>
                </h3>
                <div class="blk">
                                            <h4 class="r-h3">
                            <span class="icn">
                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/user.svg" alt="" width="16" height="16">
                            </span>
                            <span class="txt">
                                                                    <a href="/members/person-search?firstName=ROBERT&lastName=MOSHER&city=WESTON&state=MA" class="">Robert Mosher Jr.</a>
                                                             </span>
                        </h4>
                                    </div>
                                    <h3 class="r-h4 line">
                        <span class="txt">Owner's Mailing Address</span>
                    </h3>
                    <div class="blk">
                        <h4 class="r-h3">
                            <span class="icn">
                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/pin.svg" alt="" width="16" height="16">
                            </span>
                            <span class="txt">
                                <a href="#">84 Wellesley St, Weston, MA 02493</a><!--TODO: link to what?-->
                            </span>
                        </h4>
                    </div>
                            </div>
        
                    <div class="sec">
                <h3 class="r-h3">
                    <span class="txt">Ownership Timeline</span>
                </h3>
                <div class="r-tline">
                                            <div class="row">
                            <div class="year">2015</div>
                            <div class="line"></div>
                            <div class="info">
                                <div class="name">
                                                                                                                        <a href="/members/person-search?firstName=Robert&lastName=Mosher&city=WESTON&state=MA" class="">Robert Mosher</a><br>                                                                                    <a href="/members/person-search?firstName=Denise&lastName=Mosher&city=WESTON&state=MA" class="">Denise Mosher</a>                                                                                                            </div>
                                                                    <div class="data">
                                        <div class="tl">Transaction Type:</div>
                                        <div class="vl">DEED OF TRUST</div>
                                    </div>
                                                                                                    <div class="data">
                                        <div class="tl">Date of Transaction:</div>
                                        <div class="vl">08/03/2015</div>
                                    </div>
                                                                                            </div>
                        </div>
                                            <div class="row">
                            <div class="year">2015</div>
                            <div class="line"></div>
                            <div class="info">
                                <div class="name">
                                                                                                                        <a href="/members/person-search?firstName=Denise&lastName=Mosher&city=WESTON&state=MA" class="">Denise Mosher</a><br>                                                                                    <a href="/members/person-search?firstName=Robert&lastName=Mosher&city=WESTON&state=MA" class="">Robert Mosher</a>                                                                                                            </div>
                                                                    <div class="data">
                                        <div class="tl">Transaction Type:</div>
                                        <div class="vl">DEED OF TRUST</div>
                                    </div>
                                                                                                    <div class="data">
                                        <div class="tl">Date of Transaction:</div>
                                        <div class="vl">08/03/2015</div>
                                    </div>
                                                                                            </div>
                        </div>
                                            <div class="row">
                            <div class="year">2003</div>
                            <div class="line"></div>
                            <div class="info">
                                <div class="name">
                                                                                                                        <a href="/members/person-search?firstName=Denise&lastName=Mosher&city=WESTON&state=MA" class="">Denise Mosher</a>                                                                                                            </div>
                                                                    <div class="data">
                                        <div class="tl">Transaction Type:</div>
                                        <div class="vl">DEED OF TRUST</div>
                                    </div>
                                                                                                    <div class="data">
                                        <div class="tl">Date of Transaction:</div>
                                        <div class="vl">09/08/2003</div>
                                    </div>
                                                                                            </div>
                        </div>
                                            <div class="row">
                            <div class="year">2001</div>
                            <div class="line"></div>
                            <div class="info">
                                <div class="name">
                                                                                                                        <a href="/members/person-search?firstName=Robert&lastName=Mosher&city=WESTON&state=MA" class="">Robert Mosher Jr.</a>                                                                                                            </div>
                                                                    <div class="data">
                                        <div class="tl">Transaction Type:</div>
                                        <div class="vl">DEED OF TRUST</div>
                                    </div>
                                                                                                    <div class="data">
                                        <div class="tl">Date of Transaction:</div>
                                        <div class="vl">06/26/2001</div>
                                    </div>
                                                                                            </div>
                        </div>
                                            <div class="row">
                            <div class="year">1996</div>
                            <div class="line"></div>
                            <div class="info">
                                <div class="name">
                                                                                                                        <a href="/members/person-search?firstName=Robert&lastName=Mosher&city=WESTON&state=MA" class="">Robert Mosher Jr.</a>                                                                                                            </div>
                                                                    <div class="data">
                                        <div class="tl">Transaction Type:</div>
                                        <div class="vl">DEED OF TRUST</div>
                                    </div>
                                                                                                    <div class="data">
                                        <div class="tl">Date of Transaction:</div>
                                        <div class="vl">09/05/1996</div>
                                    </div>
                                                                                            </div>
                        </div>
                                            <div class="row">
                            <div class="year">1994</div>
                            <div class="line"></div>
                            <div class="info">
                                <div class="name">
                                                                                                                        <a href="/members/person-search?firstName=Robert&lastName=Mosher&city=WESTON&state=MA" class="">Robert Mosher Jr.</a>                                                                                                            </div>
                                                                    <div class="data">
                                        <div class="tl">Transaction Type:</div>
                                        <div class="vl">GRANT DEED</div>
                                    </div>
                                                                                                    <div class="data">
                                        <div class="tl">Date of Transaction:</div>
                                        <div class="vl">07/18/1994</div>
                                    </div>
                                                                                                    <div class="data">
                                        <div class="tl">Transaction Price:</div>
                                        <div class="vl">$225,000</div>
                                    </div>
                                                            </div>
                        </div>
                                            <div class="row">
                            <div class="year"></div>
                            <div class="line"></div>
                            <div class="info">
                                <div class="name">
                                                                            President Fellows Harv                                                                    </div>
                                                                                                                            </div>
                        </div>
                                    </div>
            </div>
        
            </div>

        
                                            </div>                            <div class="r-section" id="section_foreclosures">                    <div class="hdr">
    <h2 class="r-h2">
        <span class="icn">
            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/32/home_gavel.svg" alt="" width="32" height="32">
        </span>
        <span class="txt">Foreclosure</span>
    </h2>
</div>
<div class="bdy">
    <div class="sec">
        <div class="blk">
                            <p>This property is not classified as a foreclosure.</p>
                    </div>
<!--        <div class="r-results">-->
<!--            <div class="row">-->
<!--                --><!--                    <div class="col">-->
<!--                        <h4 class="r-h4">-->
<!--                            <span class="icn">-->
<!--                                <img src="--><!--" alt="" width="16" height="16">-->
<!--                            </span>-->
<!--                            <span class="txt">--><!--</span>-->
<!--                        </h4>-->
<!--                        <div class="r-table">-->
<!--                            --><!--                                <div class="row">-->
<!--                                    <div class="ttl">--><!--:</div>-->
<!--                                    <div class="val">--><!--</div>-->
<!--                                </div>-->
<!--                            --><!--                        </div>-->
<!--                        --><!--                            <div class="button">-->
<!--                                <div class="row">-->
<!--                                    <div class="col">-->
<!--                                        <a class="btn sm arw" href="--><!--">View Full Report</a>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        --><!--                    </div>-->
<!--                --><!--            </div>-->
<!--        </div>-->
    </div>
</div>
        
                                            </div>                            <div class="r-section" id="section_pre_foreclosures">                    <div class="hdr">
    <h2 class="r-h2">
        <span class="icn">
            <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/32/home_dollar.svg" alt="" width="32" height="32">
        </span>
        <span class="txt">Pre-Foreclosures</span>
    </h2>
</div>
<div class="bdy">
            <div class="notify">
    <div class="row">
        <div class="col">
            <div class="itm">
                <div class="icn filter-special">
                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/info.svg">
                </div>
                <div class="txt">We could not locate any pre-foreclosure records. It's important to keep in mind that the lack of information or results in our reports may not always be entirely accurate, comprehensive, or current. Therefore, we kindly advise against solely relying on our findings and recommend conducting your own thorough research.</div>
            </div>
        </div>
    </div>
</div>
    </div>

        
                                            </div>                            <div class="r-section" id="section_taxes">                    <div class="hdr">
    <h2 class="r-h2">
        <span class="icn">
            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/32/home_percent.svg" alt="" width="32" height="32">
        </span>
        <span class="txt">Taxes</span>
    </h2>
</div>
<div class="bdy">
            <div class="sec">
            <div class="r-list">
                                    <div class="row">
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/home_percent.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Tax Bill Amount:</div>
                                    <div class="val">$19,997</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/calendar_dollar.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Tax Year Assessed:</div>
                                    <div class="val">2024</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/home_dollar.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Total Assessed Value:</div>
                                    <div class="val">$1,798,300</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/map_pin.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Land Assessed Value:</div>
                                    <div class="val">$836,500</div>
                                </div>
                            </div>
                                            </div>
                                    <div class="row">
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/home_tag.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Additions Assessed Value:</div>
                                    <div class="val">$961,800</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/calculator.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Tax Assessed Change %:</div>
                                    <div class="val">53%</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/dollar_clock.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Previous Assessed Value:</div>
                                    <div class="val">$1,798,300</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/document_dollar_update.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Last Assessor Tax Roll Update:</div>
                                    <div class="val">01/01/2024</div>
                                </div>
                            </div>
                                            </div>
                                    <div class="row">
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/user_update.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Assessor Last Updated:</div>
                                    <div class="val">2024</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/home.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Zoning Category:</div>
                                    <div class="val">N/A</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/pin_number.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Zoned Code Local:</div>
                                    <div class="val">N/A</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/pin_number.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Zoning Type:</div>
                                    <div class="val">RESIDENTIAL</div>
                                </div>
                            </div>
                                            </div>
                                    <div class="row">
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/certificate.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Property Use Standardized:</div>
                                    <div class="val">385</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/pin.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Property Use Municode:</div>
                                    <div class="val">109</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/calendar.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Year Built:</div>
                                    <div class="val">1805</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/document_dollar_no.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Tax Exemption:</div>
                                    <div class="val">N/A</div>
                                </div>
                            </div>
                                            </div>
                                    <div class="row">
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/dollar_warning.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Tax Delinquent:</div>
                                    <div class="val">N/A</div>
                                </div>
                            </div>
                                            </div>
                            </div>
        </div>
    </div>        
                                            </div>                            <div class="r-section" id="section_tax_history">                    <div class="hdr">
    <h2 class="r-h2">
        <span class="icn">
            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/32/calendar_percent.svg" alt="" width="32" height="32">
        </span>
        <span class="txt">Tax History</span>
    </h2>
</div>
<div class="bdy">
            <div class="sec">
            <div class="r-tbl">
                <table>
                    <thead>
                        <tr>
                            <th>Year</th>
                            <th>Property Taxes</th>
                            <th>Change</th>
                            <th>Tax Assessment</th>
                            <th>Change</th>
                        </tr>
                    </thead>
                    <tbody>
                                                    <tr>
                                <td>2023</td>
                                <td>$18,466</td>
                                <td class="color-success">5.23%</td>
                                <td>$1,559,600</td>
                                <td class="color-success">13.85%</td>
                            </tr>
                                                    <tr>
                                <td>2022</td>
                                <td>$17,548</td>
                                <td class="color-success">3.55%</td>
                                <td>$1,369,900</td>
                                <td class="color-success">4.92%</td>
                            </tr>
                                                    <tr>
                                <td>2021</td>
                                <td>$16,947</td>
                                <td class="color-success">4.51%</td>
                                <td>$1,305,600</td>
                                <td class="color-success">3.30%</td>
                            </tr>
                                                    <tr>
                                <td>2020</td>
                                <td>$16,216</td>
                                <td class="color-success">5.73%</td>
                                <td>$1,263,900</td>
                                <td class="color-success">3.75%</td>
                            </tr>
                                                    <tr>
                                <td>2019</td>
                                <td>$15,337</td>
                                <td class="color-success">0.64%</td>
                                <td>$1,218,200</td>
                                <td class="color-warning">0.00%</td>
                            </tr>
                                                    <tr>
                                <td>2018</td>
                                <td>$15,240</td>
                                <td class="color-success">0.89%</td>
                                <td>$1,218,200</td>
                                <td class="color-warning">0.00%</td>
                            </tr>
                                                    <tr>
                                <td>2017</td>
                                <td>$15,106</td>
                                <td class="color-success">16.59%</td>
                                <td>$1,218,200</td>
                                <td class="color-success">14.33%</td>
                            </tr>
                                                    <tr>
                                <td>2016</td>
                                <td>$12,956</td>
                                <td class="color-success">3.02%</td>
                                <td>$1,065,500</td>
                                <td class="color-success">4.04%</td>
                            </tr>
                                                    <tr>
                                <td>2015</td>
                                <td>$12,576</td>
                                <td class="color-success">5.83%</td>
                                <td>$1,024,100</td>
                                <td class="color-success">9.71%</td>
                            </tr>
                                                    <tr>
                                <td>2014</td>
                                <td>$11,883</td>
                                <td class="color-success">5.45%</td>
                                <td>$933,500</td>
                                <td class="color-success">2.72%</td>
                            </tr>
                                                    <tr>
                                <td>2013</td>
                                <td>$11,269</td>
                                <td class="color-success">6.87%</td>
                                <td>$908,800</td>
                                <td class="color-success">4.36%</td>
                            </tr>
                                                    <tr>
                                <td>2012</td>
                                <td>$10,545</td>
                                <td class="color-none">-</td>
                                <td>$870,800</td>
                                <td class="color-none">-</td>
                            </tr>
                                            </tbody>
                </table>
            </div>
        </div>
    </div>        
                                            </div>                            <div class="r-section" id="section_listings_history">                    
<div class="hdr">
    <h2 class="r-h2">
        <span class="icn">
            <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/32/listings.svg" alt="" width="32" height="32">
        </span>
        <span class="txt">Listings History</span>
    </h2>
</div>
<div class="bdy">
            <div class="sec">
            <h3 class="r-h3">
                <span class="txt">Listing Description</span>
            </h3>
            <div class="blk">
                <p>This 3491 square foot apartment home has 4 bedrooms and 3.0 bathrooms. This home is located at 84 Wellesley St, Weston, MA 02493.</p>
            </div>
        </div>
                <div class="sec">
            <h3 class="r-h3">
                <span class="txt">Price History</span>
            </h3>
            <div class="r-results">
                <div class="row">
                                            <div class="col">
                            <h4 class="r-h4">
                                <span class="icn">
                                    <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/calendar.svg" alt="" width="16" height="16">
                                </span>
                                <span class="txt">1994-07-18</span>
                            </h4>
                            <div class="r-table">
                                                                    <div class="row">
                                        <div class="ttl">Date:</div>
                                        <div class="val">1994-07-18</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Price Change Rate:</div>
                                        <div class="val">0</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Event:</div>
                                        <div class="val">Sold</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Price:</div>
                                        <div class="val">--</div>
                                    </div>
                                                            </div>
                        </div>
                                    </div>
            </div>
        </div>
                <div class="sec">
            <h3 class="r-h3">
                <span class="txt">Interior</span>
            </h3>
            <div class="r-table two-cols">
                                    <div class="row">
                        <div class="ttl">Bedrooms:</div>
                        <div class="val">Not on File</div>
                    </div>
                                    <div class="row">
                        <div class="ttl">Bathrooms:</div>
                        <div class="val">Not on File</div>
                    </div>
                                    <div class="row">
                        <div class="ttl">Full Bathrooms:</div>
                        <div class="val">Not on File</div>
                    </div>
                                    <div class="row">
                        <div class="ttl">Half Bathrooms:</div>
                        <div class="val">Not on File</div>
                    </div>
                                    <div class="row">
                        <div class="ttl">Heating:</div>
                        <div class="val">Not on File</div>
                    </div>
                                    <div class="row">
                        <div class="ttl">Kitchen Features:</div>
                        <div class="val">Not on File</div>
                    </div>
                                    <div class="row">
                        <div class="ttl">Other Interior Features:</div>
                        <div class="val">Not on File</div>
                    </div>
                            </div>
        </div>
    </div>

        
                                            </div>                            <div class="r-section" id="section_values">                    <div class="hdr">
    <h2 class="r-h2">
        <span class="icn">
            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/32/home_dollar.svg" alt="" width="32" height="32">
        </span>
        <span class="txt">Values</span>
    </h2>
</div>
<div class="bdy">
            <div class="sec">
            <div class="r-list">
                                    <div class="row">
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/home_dollar.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Market Value Estimate:</div>
                                    <div class="val">$1,812,615</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/home_ruller.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Property Square Footage:</div>
                                    <div class="val">3,491 Sq Ft</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/map_dollar.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Market Value Per Sq Ft:</div>
                                    <div class="val">$519 / Sq Ft</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/home_tag.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Rent Estimate:</div>
                                    <div class="val">$8,613</div>
                                </div>
                            </div>
                                            </div>
                                    <div class="row">
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/calendar_dollar.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Last Sale Date:</div>
                                    <div class="val">07/18/1994</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/dollar.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Last Sale Amount:</div>
                                    <div class="val">$225,000</div>
                                </div>
                            </div>
                                            </div>
                            </div>
        </div>
    </div>        
                                            </div>                            <div class="r-section" id="section_deeds">                    <div class="hdr">
    <h2 class="r-h2">
        <span class="icn">
            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/32/document_stamp.svg" alt="" width="32" height="32">
        </span>
        <span class="txt">Deeds</span>
    </h2>
</div>
<div class="bdy">
            <div class="sec">
            <div class="r-list">
                                    <div class="row">
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/user.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Grantee’s Name(s):</div>
                                    <div class="val">ROBERT MOSHER</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/pin.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Deed Owner Address:</div>
                                    <div class="val">84 WELLESLEY ST, WESTON, MA 02493</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/home_user.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Ownership Category:</div>
                                    <div class="val">N/A</div>
                                </div>
                            </div>
                                            </div>
                                    <div class="row">
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/user.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Owner Occupied:</div>
                                    <div class="val">N/A</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/id.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Grantor’s Name(s):</div>
                                    <div class="val">N/A</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/dollar.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Transaction Amount:</div>
                                    <div class="val">N/A</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/hand_dollar.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Transaction Mortgage Amount:</div>
                                    <div class="val">N/A</div>
                                </div>
                            </div>
                                            </div>
                                    <div class="row">
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/cards.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Transaction Type:</div>
                                    <div class="val">N/A</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/certificate.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Document:</div>
                                    <div class="val">N/A</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/city.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Title Company:</div>
                                    <div class="val">N/A</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/calendar.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Transaction Date:</div>
                                    <div class="val">N/A</div>
                                </div>
                            </div>
                                            </div>
                                    <div class="row">
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/document.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Document Type:</div>
                                    <div class="val">N/A</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/home.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Property Type:</div>
                                    <div class="val">Residential</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/flag.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">County:</div>
                                    <div class="val">N/A</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/document_number.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Transfer Doc #:</div>
                                    <div class="val">N/A</div>
                                </div>
                            </div>
                                            </div>
                                    <div class="row">
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/bill.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Transfer Transaction #:</div>
                                    <div class="val">N/A</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/book_number.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Sale Document Book #:</div>
                                    <div class="val">24751</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/document_clock.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Last Document #:</div>
                                    <div class="val">24751</div>
                                </div>
                            </div>
                                            </div>
                            </div>
        </div>
    </div>
        
                                            </div>                            <div class="r-section" id="section_property_details">                    <div class="hdr">
    <h2 class="r-h2">
        <span class="icn">
            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/32/home_magnifier.svg" alt="" width="32" height="32">
        </span>
        <span class="txt">Property Details</span>
    </h2>
</div>
<div class="bdy">
            <div class="sec">
            <div class="r-list">
                                    <div class="row">
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/bed.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Bedrooms:</div>
                                    <div class="val">4</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/bath.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Bathrooms:</div>
                                    <div class="val">3</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/dividers.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Living Area:</div>
                                    <div class="val">3,491 Sq Ft</div>
                                </div>
                            </div>
                                            </div>
                                    <div class="row">
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/dividers.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Area Building Code:</div>
                                    <div class="val">10</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/ruller.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Lot Size:</div>
                                    <div class="val">1 Acres</div>
                                </div>
                            </div>
                                                    <div class="col">
                                <div class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/calendar.svg" alt="" width="16" height="16">
                                </div>
                                <div class="txt">
                                    <div class="ttl">Year Built:</div>
                                    <div class="val">1805</div>
                                </div>
                            </div>
                                            </div>
                            </div>
        </div>

                    <div class="sec acc">
                <h3 class="r-h3 acc-head">
                    <span class="icn">
                        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/24/room.svg" alt="" width="24" height="24">
                    </span>
                    <span class="txt">Rooms</span>
                </h3>
                <div class="acc-body">
                    <div class="r-table two-cols">
                                                    <div class="row">
                                <div class="ttl">Bath Count:</div>
                                <div class="val">3</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Bath Partial Count:</div>
                                <div class="val">0</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Bedrooms Count:</div>
                                <div class="val">4</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Rooms Count:</div>
                                <div class="val">8</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Stories Count:</div>
                                <div class="val">2</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Units Count:</div>
                                <div class="val">0</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Bonus Room:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Breakfast Nook:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Cellar Room:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Wine Cellar Room:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Exercise Room:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Family Room:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Game Room:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Great Room:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Hobby Room:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Laundry Room:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Media Room:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Mud Room:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Office Room:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Safe Room:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Sitting Room:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Storm Room:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Study Room:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Sunroom:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Utility Room:</div>
                                <div class="val">Not on File</div>
                            </div>
                                            </div>
                </div>
            </div>
                    <div class="sec acc">
                <h3 class="r-h3 acc-head">
                    <span class="icn">
                        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/24/home_dimentions.svg" alt="" width="24" height="24">
                    </span>
                    <span class="txt">Area</span>
                </h3>
                <div class="acc-body">
                    <div class="r-table two-cols">
                                                    <div class="row">
                                <div class="ttl">Building:</div>
                                <div class="val">3491</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Building Definition Code:</div>
                                <div class="val">Living Area</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Gross:</div>
                                <div class="val">7364</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">1st Floor:</div>
                                <div class="val">0</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">2nd Floor:</div>
                                <div class="val">0</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Upper Floors:</div>
                                <div class="val">0</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Lot Acres:</div>
                                <div class="val">1 Acres</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Lot SF:</div>
                                <div class="val">45738.00</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Lot Depth:</div>
                                <div class="val">0.0</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Lot Width:</div>
                                <div class="val">0.0</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Rooms Attic:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Rooms Basement:</div>
                                <div class="val">0</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Rooms Basement Finished:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Patio:</div>
                                <div class="val">0</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Deck:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Balcony:</div>
                                <div class="val">Not on File</div>
                            </div>
                                            </div>
                </div>
            </div>
                    <div class="sec acc">
                <h3 class="r-h3 acc-head">
                    <span class="icn">
                        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/24/parking.svg" alt="" width="24" height="24">
                    </span>
                    <span class="txt">Parking</span>
                </h3>
                <div class="acc-body">
                    <div class="r-table two-cols">
                                                    <div class="row">
                                <div class="ttl">Garage:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Garage Area:</div>
                                <div class="val">0</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Carport:</div>
                                <div class="val">0</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">RV Parking Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Space Count:</div>
                                <div class="val">0</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Driveway Area:</div>
                                <div class="val">0</div>
                            </div>
                                            </div>
                </div>
            </div>
                    <div class="sec acc">
                <h3 class="r-h3 acc-head">
                    <span class="icn">
                        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/24/home_hammer.svg" alt="" width="24" height="24">
                    </span>
                    <span class="txt">Construction Details</span>
                </h3>
                <div class="acc-body">
                    <div class="r-table two-cols">
                                                    <div class="row">
                                <div class="ttl">Roof Material:</div>
                                <div class="val">Asphalt</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Roof Construction:</div>
                                <div class="val">GABLE</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Structure Style:</div>
                                <div class="val">CONVENTIONAL</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Exterior Code:</div>
                                <div class="val">WOOD SIDING</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Foundation:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Construction:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Interior Structure:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Plumbing Fixtures Count:</div>
                                <div class="val">0</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Safety Fire Sprinklers Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                            </div>
                </div>
            </div>
                    <div class="sec acc">
                <h3 class="r-h3 acc-head">
                    <span class="icn">
                        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/24/pool.svg" alt="" width="24" height="24">
                    </span>
                    <span class="txt">Property Features</span>
                </h3>
                <div class="acc-body">
                    <div class="r-table two-cols">
                                                    <div class="row">
                                <div class="ttl">Fireplace:</div>
                                <div class="val">YES</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Fireplace Count:</div>
                                <div class="val">1</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Pool:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Pool Area:</div>
                                <div class="val">0</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Security Alarm Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Accessibility Elevator Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Accessibility Handicap Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Escalator Flag:</div>
                                <div class="val">0</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Central Vacuum Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Content Intercom Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Content Sound System Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Wet Bar Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Content Storm Shutter Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Content Overhead Door Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Porch Code:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Porch Area:</div>
                                <div class="val">0</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Deck Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Feature Balcony Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Breezeway Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Topography Code:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Fence Area:</div>
                                <div class="val">0</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Courtyard Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Arbor Pergola Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Golf Course Green Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Tennis Court Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Sports Court Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Arena Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Water Feature Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Pond Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Boat Lift Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Buildings Count:</div>
                                <div class="val">2</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Bath House Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Boat Access Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Boat House Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Cabin Area:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Cabin Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Canopy Area:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Canopy Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Gazebo Area:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Gazebo Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Grainery Area:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Grainery Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Green House Area:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Green House Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Guest House Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Kennel Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Milk House Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Outdoor Kitchen Fireplace Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Pool House Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Poultry House Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Quonset Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Shed Code:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Silo Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Stable Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Storage Building Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Pole Structure Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Publication Date:</div>
                                <div class="val">05/01/2024</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Parcel Shell Record:</div>
                                <div class="val">Not on File</div>
                            </div>
                                            </div>
                </div>
            </div>
                    <div class="sec acc">
                <h3 class="r-h3 acc-head">
                    <span class="icn">
                        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/24/home_bulb.svg" alt="" width="24" height="24">
                    </span>
                    <span class="txt">Utilities / Green Energy Details</span>
                </h3>
                <div class="acc-body">
                    <div class="r-table two-cols">
                                                    <div class="row">
                                <div class="ttl">HVAC Cooling Detail:</div>
                                <div class="val">YES</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">HVAC Heating Detail:</div>
                                <div class="val">HOT WATER</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">HVAC Heating Fuel:</div>
                                <div class="val">GAS</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Utilities Sewage Usage:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Utilities Water Source:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Utilities Mobile Home Hookup Flag:</div>
                                <div class="val">Not on File</div>
                            </div>
                                            </div>
                </div>
            </div>
                    <div class="sec acc">
                <h3 class="r-h3 acc-head">
                    <span class="icn">
                        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/24/map_pin.svg" alt="" width="24" height="24">
                    </span>
                    <span class="txt">Parcel</span>
                </h3>
                <div class="acc-body">
                    <div class="r-table two-cols">
                                                    <div class="row">
                                <div class="ttl">Raw:</div>
                                <div class="val">WEST M:033.0 L:0003 S:000.0</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Formatted:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Year Added:</div>
                                <div class="val">2007</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Alternate:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Map Book:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Map Page:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Year Change:</div>
                                <div class="val">0</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Previous:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Account Number:</div>
                                <div class="val">2227</div>
                            </div>
                                            </div>
                </div>
            </div>
                    <div class="sec acc">
                <h3 class="r-h3 acc-head">
                    <span class="icn">
                        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/24/gavel.svg" alt="" width="24" height="24">
                    </span>
                    <span class="txt">Legal</span>
                </h3>
                <div class="acc-body">
                    <div class="r-table two-cols">
                                                    <div class="row">
                                <div class="ttl">Description:</div>
                                <div class="val">LOT:3 DIST:333 CITY/MUNI/TWP:WESTON</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Range:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Township:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Section:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Quarter:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Subdivision:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Phase:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Tract Number:</div>
                                <div class="val">0</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Block 1:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Block 2:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Lot Number 1:</div>
                                <div class="val">3</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Lot Number 2:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Lot Number 3:</div>
                                <div class="val">Not on File</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Unit:</div>
                                <div class="val">Not on File</div>
                            </div>
                                            </div>
                </div>
            </div>
            </div>        
                                            </div>                            <div class="r-section" id="section_loans">                    <div class="hdr">
    <h2 class="r-h2">
        <span class="icn">
            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/32/hand_dollar.svg" alt="" width="32" height="32">
        </span>
        <span class="txt">Loans</span>
    </h2>
</div>
<div class="bdy">
                        <div class="sec">
                <h3 class="r-h3">
                    <span class="txt">Current Loans</span>
                </h3>
                <div class="r-results">
                    <div class="row">
                                                    <div class="col">
                                <h4 class="r-h4">
                                    <span class="icn">
                                        <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/hand_dollar.svg" alt="" width="16" height="16">
                                    </span>
                                    <span class="txt">Loan #1</span>
                                </h4>
                                <div class="r-table">
                                                                            <div class="row">
                                            <div class="ttl">Transaction Mortgage Amount:</div>
                                            <div class="val">$101,000</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Mortgage Date:</div>
                                            <div class="val">06/26/2001</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Lender Type:</div>
                                            <div class="val">C</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Loan Type:</div>
                                            <div class="val">N/A</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Lender:</div>
                                            <div class="val">DIRECT CU</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Lender Code:</div>
                                            <div class="val">39105</div>
                                        </div>
                                                                    </div>
                            </div>
                                                    <div class="col">
                                <h4 class="r-h4">
                                    <span class="icn">
                                        <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/hand_dollar.svg" alt="" width="16" height="16">
                                    </span>
                                    <span class="txt">Loan #2</span>
                                </h4>
                                <div class="r-table">
                                                                            <div class="row">
                                            <div class="ttl">Transaction Mortgage Amount:</div>
                                            <div class="val">$50,000</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Mortgage Date:</div>
                                            <div class="val">09/05/1996</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Lender Type:</div>
                                            <div class="val">C</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Loan Type:</div>
                                            <div class="val">N/A</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Lender:</div>
                                            <div class="val">DIRECT CU</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Lender Code:</div>
                                            <div class="val">39105</div>
                                        </div>
                                                                    </div>
                            </div>
                                                    <div class="col">
                                <h4 class="r-h4">
                                    <span class="icn">
                                        <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/hand_dollar.svg" alt="" width="16" height="16">
                                    </span>
                                    <span class="txt">Loan #3</span>
                                </h4>
                                <div class="r-table">
                                                                            <div class="row">
                                            <div class="ttl">Transaction Mortgage Amount:</div>
                                            <div class="val">$150,000</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Mortgage Date:</div>
                                            <div class="val">07/18/1994</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Lender Type:</div>
                                            <div class="val">C</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Loan Type:</div>
                                            <div class="val">10</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Lender:</div>
                                            <div class="val">NAVY FCU</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Lender Code:</div>
                                            <div class="val">3313</div>
                                        </div>
                                                                    </div>
                            </div>
                                            </div>
                </div>
            </div>
                    <div class="sec">
                <h3 class="r-h3">
                    <span class="txt">Previous Loans</span>
                </h3>
                <div class="r-results">
                    <div class="row">
                                                    <div class="col">
                                <h4 class="r-h4">
                                    <span class="icn">
                                        <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/hand_dollar.svg" alt="" width="16" height="16">
                                    </span>
                                    <span class="txt">Loan #1</span>
                                </h4>
                                <div class="r-table">
                                                                            <div class="row">
                                            <div class="ttl">Transaction Mortgage Amount:</div>
                                            <div class="val">$300,000</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Mortgage Date:</div>
                                            <div class="val">08/07/2015</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Lender Type:</div>
                                            <div class="val">C</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Loan Type:</div>
                                            <div class="val">10</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Lender:</div>
                                            <div class="val">DIRECT CU</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Lender Code:</div>
                                            <div class="val">39105</div>
                                        </div>
                                                                    </div>
                            </div>
                                                    <div class="col">
                                <h4 class="r-h4">
                                    <span class="icn">
                                        <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/hand_dollar.svg" alt="" width="16" height="16">
                                    </span>
                                    <span class="txt">Loan #2</span>
                                </h4>
                                <div class="r-table">
                                                                            <div class="row">
                                            <div class="ttl">Transaction Mortgage Amount:</div>
                                            <div class="val">$350,000</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Mortgage Date:</div>
                                            <div class="val">08/07/2015</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Lender Type:</div>
                                            <div class="val">C</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Loan Type:</div>
                                            <div class="val">10</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Lender:</div>
                                            <div class="val">DIRECT CU</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Lender Code:</div>
                                            <div class="val">39105</div>
                                        </div>
                                                                    </div>
                            </div>
                                                    <div class="col">
                                <h4 class="r-h4">
                                    <span class="icn">
                                        <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/hand_dollar.svg" alt="" width="16" height="16">
                                    </span>
                                    <span class="txt">Loan #3</span>
                                </h4>
                                <div class="r-table">
                                                                            <div class="row">
                                            <div class="ttl">Transaction Mortgage Amount:</div>
                                            <div class="val">$125,000</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Mortgage Date:</div>
                                            <div class="val">09/08/2003</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Lender Type:</div>
                                            <div class="val">C</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Loan Type:</div>
                                            <div class="val">N/A</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Lender:</div>
                                            <div class="val">DIRECT CU</div>
                                        </div>
                                                                            <div class="row">
                                            <div class="ttl">Lender Code:</div>
                                            <div class="val">39105</div>
                                        </div>
                                                                    </div>
                            </div>
                                            </div>
                </div>
            </div>
            <!--Old cached report data-->
    </div>        
                                            </div>                            <div class="r-section" id="section_building_permits">                    <div class="hdr">
    <h2 class="r-h2">
        <span class="icn">
            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/32/permit.svg" alt="" width="32" height="32">
        </span>
        <span class="txt">Building Permits</span>
    </h2>
</div>
<div class="bdy">
            <div class="sec">
            <div class="r-results">
                <div class="row">
                                            <div class="col">
                            <h4 class="r-h4">
                                <span class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/permit.svg" alt="" width="16" height="16">
                                </span>
                                <span class="txt">Permit #1</span>
                            </h4>
                            <div class="r-table">
                                                                    <div class="row">
                                        <div class="ttl">Effective Date:</div>
                                        <div class="val">10/13/2017</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Job Value:</div>
                                        <div class="val">$0</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Status:</div>
                                        <div class="val">complete</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Type:</div>
                                        <div class="val">Electrical; Gas</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Sub Type:</div>
                                        <div class="val">Remodel</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Business Name:</div>
                                        <div class="val">Matt Woodward</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Home Owner:</div>
                                        <div class="val">Mosher, Robert, Jr;</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Fees:</div>
                                        <div class="val">$60</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Permit Number:</div>
                                        <div class="val">GP-2017-190</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Description:</div>
                                        <div class="val">N/A</div>
                                    </div>
                                                            </div>
                        </div>
                                            <div class="col">
                            <h4 class="r-h4">
                                <span class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/permit.svg" alt="" width="16" height="16">
                                </span>
                                <span class="txt">Permit #2</span>
                            </h4>
                            <div class="r-table">
                                                                    <div class="row">
                                        <div class="ttl">Effective Date:</div>
                                        <div class="val">10/06/2015</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Job Value:</div>
                                        <div class="val">$7,000</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Status:</div>
                                        <div class="val">issued</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Type:</div>
                                        <div class="val">Electrical</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Sub Type:</div>
                                        <div class="val">Addition</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Business Name:</div>
                                        <div class="val">Parlee Electric</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Home Owner:</div>
                                        <div class="val">Mosher, Robert, Jr.</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Fees:</div>
                                        <div class="val">$100</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Permit Number:</div>
                                        <div class="val">EP-2015-466</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Description:</div>
                                        <div class="val">N/A</div>
                                    </div>
                                                            </div>
                        </div>
                                            <div class="col">
                            <h4 class="r-h4">
                                <span class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/permit.svg" alt="" width="16" height="16">
                                </span>
                                <span class="txt">Permit #3</span>
                            </h4>
                            <div class="r-table">
                                                                    <div class="row">
                                        <div class="ttl">Effective Date:</div>
                                        <div class="val">09/28/2015</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Job Value:</div>
                                        <div class="val">$0</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Status:</div>
                                        <div class="val">complete</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Type:</div>
                                        <div class="val">Gas; Plumbing</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Sub Type:</div>
                                        <div class="val">New Structure</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Business Name:</div>
                                        <div class="val">Kirkland & Shaw Inc; Kirkland and Shaw Inc</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Home Owner:</div>
                                        <div class="val">Mosher, Robert, Jr;</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Fees:</div>
                                        <div class="val">$140</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Permit Number:</div>
                                        <div class="val">PP-2015-246</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Description:</div>
                                        <div class="val">N/A</div>
                                    </div>
                                                            </div>
                        </div>
                                            <div class="col">
                            <h4 class="r-h4">
                                <span class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/permit.svg" alt="" width="16" height="16">
                                </span>
                                <span class="txt">Permit #4</span>
                            </h4>
                            <div class="r-table">
                                                                    <div class="row">
                                        <div class="ttl">Effective Date:</div>
                                        <div class="val">09/28/2015</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Job Value:</div>
                                        <div class="val">$0</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Status:</div>
                                        <div class="val">complete</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Type:</div>
                                        <div class="val">Electrical; Gas</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Sub Type:</div>
                                        <div class="val">New Structure</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Business Name:</div>
                                        <div class="val">Kirkland & Shaw Inc; Kirkland and Shaw Inc</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Home Owner:</div>
                                        <div class="val">Mosher, Robert, Jr;</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Fees:</div>
                                        <div class="val">$90</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Permit Number:</div>
                                        <div class="val">GP-2015-216</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Description:</div>
                                        <div class="val">N/A</div>
                                    </div>
                                                            </div>
                        </div>
                                            <div class="col">
                            <h4 class="r-h4">
                                <span class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/permit.svg" alt="" width="16" height="16">
                                </span>
                                <span class="txt">Permit #5</span>
                            </h4>
                            <div class="r-table">
                                                                    <div class="row">
                                        <div class="ttl">Effective Date:</div>
                                        <div class="val">08/25/2015</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Job Value:</div>
                                        <div class="val">$250,000</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Status:</div>
                                        <div class="val">complete</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Type:</div>
                                        <div class="val">Building</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Sub Type:</div>
                                        <div class="val">Remodel</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Business Name:</div>
                                        <div class="val">Dott Construction</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Home Owner:</div>
                                        <div class="val">Mosher, Robert, Jr.</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Fees:</div>
                                        <div class="val">$2,500</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Permit Number:</div>
                                        <div class="val">BP-2015-372</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Description:</div>
                                        <div class="val">N/A</div>
                                    </div>
                                                            </div>
                        </div>
                                            <div class="col">
                            <h4 class="r-h4">
                                <span class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/permit.svg" alt="" width="16" height="16">
                                </span>
                                <span class="txt">Permit #6</span>
                            </h4>
                            <div class="r-table">
                                                                    <div class="row">
                                        <div class="ttl">Effective Date:</div>
                                        <div class="val">11/05/2014</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Job Value:</div>
                                        <div class="val">N/A</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Status:</div>
                                        <div class="val">complete</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Type:</div>
                                        <div class="val">Gas</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Sub Type:</div>
                                        <div class="val">Remodel</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Business Name:</div>
                                        <div class="val">Dino Depasquale Plumbing</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Home Owner:</div>
                                        <div class="val">Mosher, Robert, Jr.</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Fees:</div>
                                        <div class="val">$60</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Permit Number:</div>
                                        <div class="val">PP-2014-293</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Description:</div>
                                        <div class="val">N/A</div>
                                    </div>
                                                            </div>
                        </div>
                                            <div class="col">
                            <h4 class="r-h4">
                                <span class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/permit.svg" alt="" width="16" height="16">
                                </span>
                                <span class="txt">Permit #7</span>
                            </h4>
                            <div class="r-table">
                                                                    <div class="row">
                                        <div class="ttl">Effective Date:</div>
                                        <div class="val">10/27/2014</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Job Value:</div>
                                        <div class="val">$13,000</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Status:</div>
                                        <div class="val">complete</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Type:</div>
                                        <div class="val">Building</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Sub Type:</div>
                                        <div class="val">Remodel</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Business Name:</div>
                                        <div class="val">Weber Depaula</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Home Owner:</div>
                                        <div class="val">Mosher, Robert, Jr.</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Fees:</div>
                                        <div class="val">$130</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Permit Number:</div>
                                        <div class="val">BP-2014-443</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Description:</div>
                                        <div class="val">N/A</div>
                                    </div>
                                                            </div>
                        </div>
                                            <div class="col">
                            <h4 class="r-h4">
                                <span class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/permit.svg" alt="" width="16" height="16">
                                </span>
                                <span class="txt">Permit #8</span>
                            </h4>
                            <div class="r-table">
                                                                    <div class="row">
                                        <div class="ttl">Effective Date:</div>
                                        <div class="val">02/07/2014</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Job Value:</div>
                                        <div class="val">$1,500</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Status:</div>
                                        <div class="val">complete</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Type:</div>
                                        <div class="val">Building</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Sub Type:</div>
                                        <div class="val">Remodel</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Business Name:</div>
                                        <div class="val">Bill Tracia Electrical Contractor Llc</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Home Owner:</div>
                                        <div class="val">Mosher, Robert, Jr.</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Fees:</div>
                                        <div class="val">$40</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Permit Number:</div>
                                        <div class="val">EP-2014-67</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Description:</div>
                                        <div class="val">N/A</div>
                                    </div>
                                                            </div>
                        </div>
                                            <div class="col">
                            <h4 class="r-h4">
                                <span class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/permit.svg" alt="" width="16" height="16">
                                </span>
                                <span class="txt">Permit #9</span>
                            </h4>
                            <div class="r-table">
                                                                    <div class="row">
                                        <div class="ttl">Effective Date:</div>
                                        <div class="val">01/29/2014</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Job Value:</div>
                                        <div class="val">$28,000</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Status:</div>
                                        <div class="val">ISSUED</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Type:</div>
                                        <div class="val">Building</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Sub Type:</div>
                                        <div class="val">Remodel</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Business Name:</div>
                                        <div class="val">Owens Corning Basement; Owens Corning Basement Finishing</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Home Owner:</div>
                                        <div class="val">Mosher, Robert, Jr</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Fees:</div>
                                        <div class="val">$280</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Permit Number:</div>
                                        <div class="val">BP-2014-38</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Description:</div>
                                        <div class="val">N/A</div>
                                    </div>
                                                            </div>
                        </div>
                                            <div class="col">
                            <h4 class="r-h4">
                                <span class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/permit.svg" alt="" width="16" height="16">
                                </span>
                                <span class="txt">Permit #10</span>
                            </h4>
                            <div class="r-table">
                                                                    <div class="row">
                                        <div class="ttl">Effective Date:</div>
                                        <div class="val">01/13/2012</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Job Value:</div>
                                        <div class="val">$2,000</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Status:</div>
                                        <div class="val">complete</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Type:</div>
                                        <div class="val">Building</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Sub Type:</div>
                                        <div class="val">Addition</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Business Name:</div>
                                        <div class="val">Chant Electric</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Home Owner:</div>
                                        <div class="val">Mosher, Robert, Jr.</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Fees:</div>
                                        <div class="val">$50</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Permit Number:</div>
                                        <div class="val">EP-2012-20</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Description:</div>
                                        <div class="val">N/A</div>
                                    </div>
                                                            </div>
                        </div>
                                            <div class="col">
                            <h4 class="r-h4">
                                <span class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/permit.svg" alt="" width="16" height="16">
                                </span>
                                <span class="txt">Permit #11</span>
                            </h4>
                            <div class="r-table">
                                                                    <div class="row">
                                        <div class="ttl">Effective Date:</div>
                                        <div class="val">11/18/2011</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Job Value:</div>
                                        <div class="val">$72,000</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Status:</div>
                                        <div class="val">ISSUED</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Type:</div>
                                        <div class="val">Building</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Sub Type:</div>
                                        <div class="val">Garage</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Business Name:</div>
                                        <div class="val">MOSHER, ROBERT, JR</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Home Owner:</div>
                                        <div class="val">N/A</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Fees:</div>
                                        <div class="val">N/A</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Permit Number:</div>
                                        <div class="val">BP-2011-0572</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Description:</div>
                                        <div class="val">N/A</div>
                                    </div>
                                                            </div>
                        </div>
                                            <div class="col">
                            <h4 class="r-h4">
                                <span class="icn">
                                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/permit.svg" alt="" width="16" height="16">
                                </span>
                                <span class="txt">Permit #12</span>
                            </h4>
                            <div class="r-table">
                                                                    <div class="row">
                                        <div class="ttl">Effective Date:</div>
                                        <div class="val">08/31/2004</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Job Value:</div>
                                        <div class="val">N/A</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Status:</div>
                                        <div class="val">complete</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Type:</div>
                                        <div class="val">Building</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Sub Type:</div>
                                        <div class="val">Addition</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Business Name:</div>
                                        <div class="val">N/A</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Home Owner:</div>
                                        <div class="val">Mosha</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Fees:</div>
                                        <div class="val">N/A</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Permit Number:</div>
                                        <div class="val">EP-2004-0325</div>
                                    </div>
                                                                    <div class="row">
                                        <div class="ttl">Description:</div>
                                        <div class="val">N/A</div>
                                    </div>
                                                            </div>
                        </div>
                                    </div>
            </div>
        </div>
    </div>        
                                            </div>                            <div class="r-section" id="section_liens_and_judgments">                    <div class="hdr">
    <h2 class="r-h2">
        <span class="icn">
            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/32/gavel.svg" alt="" width="32" height="32">
        </span>
        <span class="txt">Liens & Judgments</span>
    </h2>
</div>
<div class="bdy">
    <!--Disclaimer-->
    <div class="notify">
    <div class="row">
        <div class="col">
            <div class="itm">
                <div class="icn filter-special">
                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/info.svg">
                </div>
                <div class="txt">Liens and Judgments information can vary in accuracy and may be subject to change over time due to updates or corrections made by relevant authorities. Additionally, please be aware that the address information used to match Lien and Judgment records may not always precisely match the property address.</div>
            </div>
        </div>
    </div>
</div>

            <div class="notify">
    <div class="row">
        <div class="col">
            <div class="itm">
                <div class="icn filter-special">
                    <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/info.svg">
                </div>
                <div class="txt">We could not locate any liens or judgments. It's important to keep in mind that the lack of information or results in our reports may not always be entirely accurate, comprehensive, or current. Therefore, we kindly advise against solely relying on our findings and recommend conducting your own thorough research.</div>
            </div>
        </div>
    </div>
</div>
    
    <!--Liens-->
    
    <!--Judgments-->
    </div>        
                                            </div>                            <div class="r-section" id="section_residents">                    
<div class="hdr">
    <h2 class="r-h2">
        <span class="icn">
            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/32/group.svg" alt="" width="32" height="32">
        </span>
        <span class="txt">Residents</span>
    </h2>
</div>
<div class="bdy">
            <div class="sec">
            <div class="blk">
                                    <h4 class="r-h3">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/user.svg" alt="" width="16" height="16">
                        </span>
                        <span class="txt">
                                                            <a href="/members/person-search?firstName=CYNTHIA&lastName=MOSHER&city=WESTON&state=MA" class="">Cynthia Mosher</a>
                                                    </span>
                    </h4>
                                    <h4 class="r-h3">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/user.svg" alt="" width="16" height="16">
                        </span>
                        <span class="txt">
                                                            <a href="/members/person-search?firstName=C&lastName=MOSHER&city=WESTON&state=MA" class="">C Mosher</a>
                                                    </span>
                    </h4>
                                    <h4 class="r-h3">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/user.svg" alt="" width="16" height="16">
                        </span>
                        <span class="txt">
                                                            <a href="/members/person-search?firstName=CAROLYN&lastName=MCIVER&city=WESTON&state=MA" class="">Carolyn Mciver</a>
                                                    </span>
                    </h4>
                                    <h4 class="r-h3">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/user.svg" alt="" width="16" height="16">
                        </span>
                        <span class="txt">
                                                            <a href="/members/person-search?firstName=DENISE&lastName=MOSHER&city=WESTON&state=MA" class="">Denise Mosher</a>
                                                    </span>
                    </h4>
                                    <h4 class="r-h3">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/user.svg" alt="" width="16" height="16">
                        </span>
                        <span class="txt">
                                                            <a href="/members/person-search?firstName=OLIVIA&lastName=PARROTT&city=WESTON&state=MA" class="">Olivia Parrott</a>
                                                    </span>
                    </h4>
                                    <h4 class="r-h3">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/user.svg" alt="" width="16" height="16">
                        </span>
                        <span class="txt">
                                                            <a href="/members/person-search?firstName=APRIL&lastName=STEINERT&city=WESTON&state=MA" class="">April Steinert</a>
                                                    </span>
                    </h4>
                                    <h4 class="r-h3">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/user.svg" alt="" width="16" height="16">
                        </span>
                        <span class="txt">
                                                            <a href="/members/person-search?firstName=VIBEKE&lastName=LOU&city=WESTON&state=MA" class="">Vibeke Lou</a>
                                                    </span>
                    </h4>
                                    <h4 class="r-h3">
                        <span class="icn">
                            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/user.svg" alt="" width="16" height="16">
                        </span>
                        <span class="txt">
                                                            <a href="/members/person-search?firstName=ROBERT&lastName=MOSHER&city=WESTON&state=MA" class="">Robert Mosher</a>
                                                    </span>
                    </h4>
                            </div>
        </div>
    </div>        
                                            </div>                            <div class="r-section" id="section_images">                    <div class="hdr">
    <h2 class="r-h2">
        <span class="icn">
            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/32/images.svg" alt="" width="32" height="32">
        </span>
        <span class="txt">Images</span>
    </h2>
</div>
<div class="bdy">
    <div class="sec">
        <div class="r-imgs no-slick">
                            <div class="col">
                    <div class="cnt">
                        <img class="lazyload" data-src="https://photos.zillowstatic.com/fp/baa7fa3f746ab4d9c9ed401a85b110b9-p_c.jpg" alt="" width="316" height="234">
                    </div>
                </div>
                    </div>
    </div>
</div>        
                                            </div>                            <div class="r-section" id="section_neighborhood">                    <div class="hdr">
    <h2 class="r-h2">
        <span class="icn">
            <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/32/pin_map.svg" alt="" width="32" height="32">
        </span>
        <span class="txt">Neighborhood</span>
    </h2>
</div>
<div class="bdy">
            <div class="sec">
            <h3 class="r-h3">
                <span class="icn-round">
                    <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/32/pin.svg" alt="" width="32" height="32">
                </span>
                <span class="txt">Area Information</span>
            </h3>
            <div class="r-cnt">
                <div class="row">
                    <div class="col">
                        <div class="blk">
                                                        <p>
                                                                    Nestled near major cities like Boston, Weston, MA, beckons investors with its promising real estate prospects. With a population of 12,000, the community boasts a median age of 45, reflecting a mature and established demographic. The racial makeup is diverse, comprising 80% White, 10% Asian, 5% Black, and 5% Hispanic residents, fostering a rich cultural blend. The average sale price for properties in Weston stands at $1.5 million, while rentals are attractively priced at $3,000 per month. The estimated home value continues to rise steadily, offering lucrative investment opportunities. Compared to neighboring areas, Weston properties are competitively priced, typically selling at par with or slightly above the regional average. The area is served by several top-notch schools, ensuring access to quality education. With a low crime rate, Weston offers a safe and secure environment for families and businesses. The average income is $200,000, fueling a growing trend in property acquisitions. In summary, Weston, MA, presents a compelling investment landscape characterized by luxury, prestige, and promising appreciation potential.                                                            </p>
                        </div>
                    </div>
                                    </div>
            </div>
        </div>
        <div class="sec">
        <div class="blk">
            <div class="niche-base">
                                    <div class="icn">
                        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                    </div>
                                <div class="inf">
                    <h3 class="r-h3">
                        <span class="icn-round">
                            <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/32/city.svg" alt="" width="32" height="32">
                        </span>
                        <span class="txt">
                            Weston, Massachusetts Ratings
                        </span>
                    </h3>
                </div>
            </div>
        </div>
                    <div class="niche-list2">
                <ul>
                                            <li>
                            <span class="icn">
                                                                    <img class="rat lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                                                                <img class="img lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/ratings/school.svg" alt="" width="32" height="32">
                            </span>
                            <span class="txt">Public Schools</span>
                        </li>
                                            <li>
                            <span class="icn">
                                                                    <img class="rat lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/c_plus.svg" alt="" width="76" height="76">
                                                                <img class="img lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/ratings/money.svg" alt="" width="32" height="32">
                            </span>
                            <span class="txt">Cost of Living</span>
                        </li>
                                            <li>
                            <span class="icn">
                                                                    <img class="rat lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                                                                <img class="img lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/ratings/health.svg" alt="" width="32" height="32">
                            </span>
                            <span class="txt">Health & Fitness</span>
                        </li>
                                            <li>
                            <span class="icn">
                                                                    <img class="rat lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a.svg" alt="" width="76" height="76">
                                                                <img class="img lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/ratings/mountain.svg" alt="" width="32" height="32">
                            </span>
                            <span class="txt">Outdoor Activities</span>
                        </li>
                                            <li>
                            <span class="icn">
                                                                    <img class="rat lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/c_plus.svg" alt="" width="76" height="76">
                                                                <img class="img lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/ratings/discoball.svg" alt="" width="32" height="32">
                            </span>
                            <span class="txt">Nightlife</span>
                        </li>
                                            <li>
                            <span class="icn">
                                                                    <img class="rat lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/c_plus.svg" alt="" width="76" height="76">
                                                                <img class="img lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/ratings/weather.svg" alt="" width="32" height="32">
                            </span>
                            <span class="txt">Weather</span>
                        </li>
                                            <li>
                            <span class="icn">
                                                                    <img class="rat lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_minus.svg" alt="" width="76" height="76">
                                                                <img class="img lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/ratings/commute.svg" alt="" width="32" height="32">
                            </span>
                            <span class="txt">Commute</span>
                        </li>
                                            <li>
                            <span class="icn">
                                                                    <img class="rat lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/c_plus.svg" alt="" width="76" height="76">
                                                                <img class="img lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/ratings/bar.svg" alt="" width="32" height="32">
                            </span>
                            <span class="txt">Access to Bars</span>
                        </li>
                                            <li>
                            <span class="icn">
                                                                    <img class="rat lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/b_minus.svg" alt="" width="76" height="76">
                                                                <img class="img lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/ratings/coffee.svg" alt="" width="32" height="32">
                            </span>
                            <span class="txt">Access to Coffee Shops</span>
                        </li>
                                            <li>
                            <span class="icn">
                                                                    <img class="rat lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a.svg" alt="" width="76" height="76">
                                                                <img class="img lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/ratings/kayak.svg" alt="" width="32" height="32">
                            </span>
                            <span class="txt">Access to Outdoor Activities</span>
                        </li>
                                            <li>
                            <span class="icn">
                                                                    <img class="rat lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_minus.svg" alt="" width="76" height="76">
                                                                <img class="img lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/ratings/park.svg" alt="" width="32" height="32">
                            </span>
                            <span class="txt">Access to Parks</span>
                        </li>
                                            <li>
                            <span class="icn">
                                                                    <img class="rat lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/c_plus.svg" alt="" width="76" height="76">
                                                                <img class="img lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/ratings/restaurant.svg" alt="" width="32" height="32">
                            </span>
                            <span class="txt">Access to Restaurants</span>
                        </li>
                                            <li>
                            <span class="icn">
                                                                    <img class="rat lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_minus.svg" alt="" width="76" height="76">
                                                                <img class="img lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/ratings/badge.svg" alt="" width="32" height="32">
                            </span>
                            <span class="txt">Crime & Safety</span>
                        </li>
                                            <li>
                            <span class="icn">
                                                                    <img class="rat lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/b_minus.svg" alt="" width="76" height="76">
                                                                <img class="img lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/ratings/diversity.svg" alt="" width="32" height="32">
                            </span>
                            <span class="txt">Diversity</span>
                        </li>
                                            <li>
                            <span class="icn">
                                                                    <img class="rat lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                                                                <img class="img lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/ratings/family.svg" alt="" width="32" height="32">
                            </span>
                            <span class="txt">Good for Families</span>
                        </li>
                                            <li>
                            <span class="icn">
                                                                    <img class="rat lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/b_plus.svg" alt="" width="76" height="76">
                                                                <img class="img lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/ratings/oldman.svg" alt="" width="32" height="32">
                            </span>
                            <span class="txt">Good for Retirees</span>
                        </li>
                                            <li>
                            <span class="icn">
                                                                    <img class="rat lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/b.svg" alt="" width="76" height="76">
                                                                <img class="img lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/ratings/young.svg" alt="" width="32" height="32">
                            </span>
                            <span class="txt">Good for Young Professionals</span>
                        </li>
                                            <li>
                            <span class="icn">
                                                                    <img class="rat lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/b_minus.svg" alt="" width="76" height="76">
                                                                <img class="img lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/ratings/houses.svg" alt="" width="32" height="32">
                            </span>
                            <span class="txt">Housing</span>
                        </li>
                                            <li>
                            <span class="icn">
                                                                    <img class="rat lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_minus.svg" alt="" width="76" height="76">
                                                                <img class="img lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/ratings/suitcase.svg" alt="" width="32" height="32">
                            </span>
                            <span class="txt">Jobs</span>
                        </li>
                                            <li>
                            <span class="icn">
                                                                    <img class="rat lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/b.svg" alt="" width="76" height="76">
                                                                <img class="img lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/ratings/walking.svg" alt="" width="32" height="32">
                            </span>
                            <span class="txt">Walkability</span>
                        </li>
                                    </ul>
            </div>
            <div class="powered-by">
    <div class="tx">Powered by</div>
    <div class="im">
        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/niche.svg" alt="" width="141" height="22">
    </div>
</div>            </div>
    <div class="sec">
        <div class="blk">
            <div class="niche-base">
                                    <div class="icn">
                        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/b_minus.svg" alt="" width="76" height="76">
                    </div>
                                <div class="inf">
                    <h3 class="r-h3">
                        <span class="icn-round">
                            <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/32/housing.svg" alt="" width="32" height="32">
                        </span>
                        <span class="txt">Housing</span>
                    </h3>
                </div>
            </div>
        </div>
                    <div class="r-list">
                <div class="row">
                                            <div class="col">
                            <div class="icn">
                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/home_dollar.svg" alt="" width="16" height="16">
                            </div>
                            <div class="txt">
                                <div class="ttl">Median Home Value:</div>
                                <div class="val">$1,434,000</div>
                            </div>
                        </div>
                                            <div class="col">
                            <div class="icn">
                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/tag.svg" alt="" width="16" height="16">
                            </div>
                            <div class="txt">
                                <div class="ttl">Median Rent:</div>
                                <div class="val">$2,072</div>
                            </div>
                        </div>
                                            <div class="col">
                            <div class="icn">
                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/home_percent.svg" alt="" width="16" height="16">
                            </div>
                            <div class="txt">
                                <div class="ttl">Median Real Estate Tax:</div>
                                <div class="val">0.70%</div>
                            </div>
                        </div>
                                            <div class="col">
                            <div class="icn">
                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/key.svg" alt="" width="16" height="16">
                            </div>
                            <div class="txt">
                                <div class="ttl">Rent vs. Own:</div>
                                <div class="val">87%</div>
                            </div>
                        </div>
                                    </div>
            </div>
                            <div class="r-charts">
                                    <div class="row">
                        <figure class="highcharts-figure">
                            <div id="commuteMethod"></div>
                        </figure>
                    </div>
                                    <div class="row">
                        <figure class="highcharts-figure">
                            <div id="commuteTime"></div>
                        </figure>
                    </div>
                                    <div class="row">
                        <figure class="highcharts-figure">
                            <div id="homeAgeBreakdown"></div>
                        </figure>
                    </div>
                                    <div class="row">
                        <figure class="highcharts-figure">
                            <div id="homeSizeBreakdown"></div>
                        </figure>
                    </div>
                                    <div class="row">
                        <figure class="highcharts-figure">
                            <div id="homeTypeBreakdowns"></div>
                        </figure>
                    </div>
                            </div>
            </div>
    <div class="sec">
        <div class="blk">
            <div class="niche-base">
                                    <div class="icn">
                        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_minus.svg" alt="" width="76" height="76">
                    </div>
                                <div class="inf">
                    <h3 class="r-h3">
                        <span class="icn-round">
                            <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/32/police.svg" alt="" width="32" height="32">
                        </span>
                        <span class="txt">
                            Crime &amp; Safety
                            <small>Based on violent and property crime rates.</small>
                        </span>
                    </h3>
                </div>
            </div>

        </div>
<!--        <div class="blk">-->
<!--            <h3 class="r-h4">-->
<!--                <span class="txt">Violent Crimes <small>(calculated annually per 100,000 residents)</small></span>-->
<!--            </h3>-->
<!--            --><!--                <div class="r-list">-->
<!--                    <div class="row">-->
<!--                        --><!--                            <div class="col">-->
<!--                                <div class="icn">-->
<!--                                    <img class="lazyloaded" data-src="--><!--" src="--><!--" alt="" width="16" height="16">-->
<!--                                </div>-->
<!--                                <div class="txt">-->
<!--                                    <div class="ttl">--><!--:</div>-->
<!--                                    <div class="val">--><!--</div>-->
<!--                                    <div class="nte">--><!--</div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        --><!--                    </div>-->
<!--                </div>-->
<!--            --><!--        </div>-->
<!--        <div class="blk">-->
<!--            <h3 class="r-h4">-->
<!--                <span class="txt">Property Crimes <small>(calculated annually per 100,000 residents)</small></span>-->
<!--            </h3>-->
<!--            --><!--                <div class="r-list">-->
<!--                    <div class="row">-->
<!--                        --><!--                            <div class="col">-->
<!--                                <div class="icn">-->
<!--                                    <img class="lazyloaded" data-src="--><!--" src="--><!--" alt="" width="16" height="16">-->
<!--                                </div>-->
<!--                                <div class="txt">-->
<!--                                    <div class="ttl">--><!--:</div>-->
<!--                                    <div class="val">--><!--</div>-->
<!--                                    <div class="nte">--><!--</div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        --><!--                    </div>-->
<!--                </div>-->
<!--            --><!--        </div>-->
    </div>
    <div class="sec">
        <div class="blk">
            <div class="niche-base">
                                    <div class="icn">
                        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_minus.svg" alt="" width="76" height="76">
                    </div>
                                <div class="inf">
                    <h3 class="r-h3">
                        <span class="icn-round">
                            <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/32/suitcase.svg" alt="" width="32" height="32">
                        </span>
                        <span class="txt">
                            Jobs
                            <small>Based on employment rates, job and business growth, and cost of living.</small>
                        </span>
                    </h3>
                </div>
            </div>
        </div>
                    <div class="r-charts">
                                    <div class="row">
                                                    <div class="r-med">
                                                                    <div class="t1">Median Household Income</div>
                                                                                                    <div class="t2">$250,001</div>
                                                                                                    <div class="t3">National N/A</div>
                                                            </div>
                                                                    </div>
                                    <div class="row">
                                                                            <figure class="highcharts-figure">
                                <div id="populationAgeBreakdown"></div>
                            </figure>
                                            </div>
                            </div>
            </div>

    <div class="sec">
        <div class="blk">
            <div class="niche-base">
                <!--                -->                <!--                    <div class="icn">-->
                <!--                        <img class="lazyload" data-src="--><!--" alt="" width="76" height="76">-->
                <!--                    </div>-->
                <!--                -->                <div class="inf">
                    <h3 class="r-h3">
                        <span class="icn-round">
                            <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/32/education.svg" alt="" width="32" height="32">
                        </span>
                        <span class="txt">Education Breakdown</span>
                    </h3>
                </div>

            </div>
        </div>

                    <div class="r-charts">
                                    <div class="row">
                        <figure class="highcharts-figure">
                            <div id="educationBreakdown"></div>
                        </figure>
                    </div>
                            </div>
                <!--SCHOOLS-->
                <div class="schools">
                            <div class="res">
                    <div class="niche-base">
                                                <div class="inf">
                            <h4 class="r-h3">Cadence Academy Preschool - Weston</h4>
                            <div class="txt">
                                <ul>
                                    <li>
                                        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/pin.svg" alt="" width="16" height="16">
                                        210 SOUTH AVE, WESTON, MA 02493                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                                            <div class="niche-list-inline">
                            <ul>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/c_plus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Diversity</span>
                                    </li>
                                                            </ul>
                        </div>
                                    </div>
                            <div class="res">
                    <div class="niche-base">
                                                    <div class="icn">
                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a.svg" alt="" width="76" height="76">
                            </div>
                                                <div class="inf">
                            <h4 class="r-h3">Country Elementary School</h4>
                            <div class="txt">
                                <ul>
                                    <li>
                                        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/pin.svg" alt="" width="16" height="16">
                                        2 ALPHABET LN, WESTON, MA 02493                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                                            <div class="niche-list-inline">
                            <ul>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Academics</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Teachers</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/b_plus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Diversity</span>
                                    </li>
                                                            </ul>
                        </div>
                                    </div>
                            <div class="res">
                    <div class="niche-base">
                                                    <div class="icn">
                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                            </div>
                                                <div class="inf">
                            <h4 class="r-h3">Field Elementary School</h4>
                            <div class="txt">
                                <ul>
                                    <li>
                                        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/pin.svg" alt="" width="16" height="16">
                                        16 ALPHABET LN, WESTON, MA 02493                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                                            <div class="niche-list-inline">
                            <ul>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Academics</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Teachers</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_minus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Diversity</span>
                                    </li>
                                                            </ul>
                        </div>
                                    </div>
                            <div class="res">
                    <div class="niche-base">
                                                    <div class="icn">
                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                            </div>
                                                <div class="inf">
                            <h4 class="r-h3">The Cambridge School of Weston</h4>
                            <div class="txt">
                                <ul>
                                    <li>
                                        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/pin.svg" alt="" width="16" height="16">
                                        45 GEORGIAN RD, WESTON, MA 02493                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                                            <div class="niche-list-inline">
                            <ul>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Academics</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Teachers</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_minus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Clubs & Activities</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/c_plus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Sports</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Resources & Facilities</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Diversity</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">College Prep</span>
                                    </li>
                                                            </ul>
                        </div>
                                    </div>
                            <div class="res">
                    <div class="niche-base">
                                                <div class="inf">
                            <h4 class="r-h3">The Gifford School</h4>
                            <div class="txt">
                                <ul>
                                    <li>
                                        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/pin.svg" alt="" width="16" height="16">
                                        177 BOSTON POST RD, WESTON, MA 02493                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                                    </div>
                            <div class="res">
                    <div class="niche-base">
                                                <div class="inf">
                            <h4 class="r-h3">The Goddard School - Weston</h4>
                            <div class="txt">
                                <ul>
                                    <li>
                                        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/pin.svg" alt="" width="16" height="16">
                                        2 NORTH AVE, WESTON, MA 02493                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                                    </div>
                            <div class="res">
                    <div class="niche-base">
                                                <div class="inf">
                            <h4 class="r-h3">The Meadowbrook School of Weston</h4>
                            <div class="txt">
                                <ul>
                                    <li>
                                        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/pin.svg" alt="" width="16" height="16">
                                        10 FARM RD, WESTON, MA 02493                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                                            <div class="niche-list-inline">
                            <ul>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_minus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Diversity</span>
                                    </li>
                                                            </ul>
                        </div>
                                    </div>
                            <div class="res">
                    <div class="niche-base">
                                                    <div class="icn">
                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                            </div>
                                                <div class="inf">
                            <h4 class="r-h3">The Rivers School</h4>
                            <div class="txt">
                                <ul>
                                    <li>
                                        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/pin.svg" alt="" width="16" height="16">
                                        333 WINTER ST, WESTON, MA 02493                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                                            <div class="niche-list-inline">
                            <ul>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Academics</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Teachers</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Clubs & Activities</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Sports</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Resources & Facilities</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/b_plus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Diversity</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">College Prep</span>
                                    </li>
                                                            </ul>
                        </div>
                                    </div>
                            <div class="res">
                    <div class="niche-base">
                                                    <div class="icn">
                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                            </div>
                                                <div class="inf">
                            <h4 class="r-h3">Weston High School</h4>
                            <div class="txt">
                                <ul>
                                    <li>
                                        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/pin.svg" alt="" width="16" height="16">
                                        444 WELLESLEY ST, WESTON, MA 02493                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                                            <div class="niche-list-inline">
                            <ul>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Academics</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Teachers</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Clubs & Activities</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Sports</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_minus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Resources & Facilities</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/b_plus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Diversity</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">College Prep</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/b_plus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Administration</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/b.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Food</span>
                                    </li>
                                                            </ul>
                        </div>
                                    </div>
                            <div class="res">
                    <div class="niche-base">
                                                    <div class="icn">
                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                            </div>
                                                <div class="inf">
                            <h4 class="r-h3">Weston Middle School</h4>
                            <div class="txt">
                                <ul>
                                    <li>
                                        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/pin.svg" alt="" width="16" height="16">
                                        456 WELLESLEY ST, WESTON, MA 02493                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                                            <div class="niche-list-inline">
                            <ul>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Academics</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Teachers</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_minus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Diversity</span>
                                    </li>
                                                            </ul>
                        </div>
                                    </div>
                            <div class="res">
                    <div class="niche-base">
                                                    <div class="icn">
                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                            </div>
                                                <div class="inf">
                            <h4 class="r-h3">Woodland Elementary School</h4>
                            <div class="txt">
                                <ul>
                                    <li>
                                        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/pin.svg" alt="" width="16" height="16">
                                        10 ALPHABET LN, WESTON, MA 02493                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                                            <div class="niche-list-inline">
                            <ul>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Academics</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/a_plus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Teachers</span>
                                    </li>
                                                                    <li>
                                                                                    <span class="icn">
                                                <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/b_plus.svg" alt="" width="76" height="76">
                                            </span>
                                                                                <span class="txt">Diversity</span>
                                    </li>
                                                            </ul>
                        </div>
                                    </div>
                    </div>
            </div>
    <div class="sec">
        <div class="blk">
            <div class="niche-base">
                                    <div class="icn">
                        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/niche2/b_minus.svg" alt="" width="76" height="76">
                    </div>
                                <div class="inf">
                    <h3 class="r-h3">
                        <span class="icn-round">
                            <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/32/people.svg" alt="" width="32" height="32">
                        </span>
                        <span class="txt">
                            Diversity
                            <small>Based on ethnic and economic diversity.</small>
                        </span>
                    </h3>
                </div>
            </div>
        </div>
                    <div class="r-list">
                <div class="row">
                                            <div class="col">
                            <div class="icn">
                                <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/group.svg" alt="" width="16" height="16">
                            </div>
                            <div class="txt">
                                <div class="ttl">Population:</div>
                                <div class="val">11,759</div>
                            </div>
                        </div>
                                            <div class="col">
                            <div class="icn">
                                <img src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/16/suitcase_no.svg" alt="" width="16" height="16">
                            </div>
                            <div class="txt">
                                <div class="ttl">Unemployment Rate:</div>
                                <div class="val">1.14%</div>
                            </div>
                        </div>
                                    </div>
            </div>
                            <div class="r-charts">
                <div class="row">
                    <figure class="highcharts-figure">
                        <div id="diversityBreakdown"></div>
                    </figure>
                </div>
            </div>
                <div class="powered-by">
    <div class="tx">Powered by</div>
    <div class="im">
        <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/niche.svg" alt="" width="141" height="22">
    </div>
</div>    </div>
</div>

<script>
    /**
     * init highcharts
     * @param charts
     */
    function initCharts(charts) {
        Object.values(charts).forEach((chartData) => {
            if (!chartData) {
                return;
            }

            let chartOptions = {
                chart: {
                    type: chartData['type'],
                },
                title: {
                    text: chartData['title'],
                },
                series: chartData['series'],
                yAxis: {
                    title: {
                        text: chartData['title'],
                    }
                },
            };

            Highcharts.chart(chartData['id'], chartOptions);
        })
    }

    let housingCharts = [{"id":"commuteMethod","type":"column","title":"Commute Method","series":[{"data":[{"name":"Bicycle","y":0},{"name":"Carpooled","y":3.465},{"name":"Drove Alone","y":60.27},{"name":"Public Transportation","y":3.88},{"name":"Motorcycle","y":0},{"name":"Taxicab","y":0},{"name":"Walked","y":7.0120000000000005},{"name":"Worked at Home","y":24.564},{"name":"Other","y":0.8089999999999999}]}]},{"id":"commuteTime","type":"column","title":"Commute Time (in Minutes)","series":[{"data":[{"name":"Under 5","y":5.3629999999999995},{"name":"5 - 9","y":10.451},{"name":"10 - 14","y":11.193999999999999},{"name":"15 - 19","y":8.663},{"name":"20 - 24","y":10.864},{"name":"25 - 29","y":4.043},{"name":"30 - 34","y":20.71},{"name":"35 - 39","y":4.813},{"name":"40 - 44","y":3.19},{"name":"45 - 59","y":13.806},{"name":"60 - 89","y":6.436},{"name":"Over 90","y":0.468}]}]},{"id":"homeAgeBreakdown","type":"column","title":"Home Age Breakdown","series":[{"data":[{"name":"Before 1939","y":23.544},{"name":"1940 - 1959","y":24.124000000000002},{"name":"1960 - 1979","y":20.469},{"name":"1980 - 1999","y":14.696000000000002},{"name":"2000 - Present","y":17.166999999999998}]}]},{"id":"homeSizeBreakdown","type":"column","title":"Home Size Breakdown","series":[{"data":[{"name":"No Bedrooms","y":0.22699999999999998},{"name":"1 Bedroom","y":6.0249999999999995},{"name":"2 Bedrooms","y":8.873000000000001},{"name":"3 Bedrooms","y":23.645},{"name":"4 Bedrooms","y":34.232},{"name":"5 or More Bedrooms","y":26.998}]}]},{"id":"homeTypeBreakdowns","type":"column","title":"Home Type Breakdowns","series":[{"data":[{"name":"Large Apartment Building","y":4.386},{"name":"Mobile Home","y":0.151},{"name":"Single Family","y":85.304},{"name":"Small Apartment Building","y":6.1},{"name":"Townhouse","y":4.058}]}]}];
    let jobsCharts = [null,{"id":"populationAgeBreakdown","type":"column","title":"Population Age Breakdown","series":[{"data":[{"name":"Under 10","y":12.059000000000001},{"name":"10 - 17","y":12.806999999999999},{"name":"18 - 24","y":9.814},{"name":"25 - 34","y":5.162},{"name":"35 - 44","y":9.525},{"name":"45 - 54","y":15.503},{"name":"55 - 64","y":13.947000000000001},{"name":"65 Plus","y":21.184}]}]}];
    let educationCharts = {"educationBreakdown":{"id":"educationBreakdown","type":"column","title":"Education Breakdown","series":[{"data":[{"name":"Below High School","y":0.781},{"name":"Nursery Grade 4","y":0},{"name":"Grade 5-6","y":0},{"name":"Grade 7-8","y":0},{"name":"Grade 9","y":0.365},{"name":"Grade 10","y":0.104},{"name":"Grade 11","y":0.404},{"name":"Grade 12","y":0.208},{"name":"High School","y":6.8870000000000005},{"name":"College Less","y":2.213},{"name":"Some College","y":3.02},{"name":"Associate Degree","y":3.8280000000000003},{"name":"Bachelor Degree","y":34.239999999999995},{"name":"Masters Degree","y":26.611},{"name":"Professional Degree","y":12.225},{"name":"Doctorate","y":9.113}]}]}};
    let $diversityCharts = [{"id":"diversityBreakdown","type":"column","title":"Diversity Breakdown","series":[{"data":[{"name":"African-American","y":3.0700000000000003},{"name":"Asian","y":13.266},{"name":"Hispanic","y":3.75},{"name":"Multiracial","y":3.4869999999999997},{"name":"Native American","y":0.026},{"name":"Pacific Islander","y":0},{"name":"White","y":73.91799999999999},{"name":"Other","y":2.483}]}]}];
    document.addEventListener('DOMContentLoaded', () => {
        initCharts(housingCharts);
        initCharts(jobsCharts);
        initCharts(educationCharts);
        initCharts($diversityCharts);
    });
</script>

<style>
    .niche-base-txt {
        color: #7A7C83;
        font-size: 14px;
    }
</style>        
                                            </div>                            <div class="r-section" id="section_sex_offenders">                    <div class="hdr">
    <h2 class="r-h2">
        <span class="icn">
            <img class="lazyload" data-src="/tspecV2/PremiumPropertyReport/shared/img/icns/fill/32/avatar.svg" alt="" width="32" height="32">
        </span>
        <span class="txt">Sex Offenders</span>
    </h2>
</div>
<div class="bdy">
        <div class="sec">
        <div class="r-res-list" id="sexOffendersListContainer">
                        <div class="row acc ">
                <div class="head acc-head">
                    <div class="img">
                        <img class="lazyload" data-src="https://storage.googleapis.com/sex-offenders-images-v3/FL6139.jpeg" alt="" width="96" height="96">
                    </div>
                    <div class="inf">
                        <div class="t1">Henry T Fischer</div>
                        <div class="t2"><strong>Offense:</strong> 3/21/1995-LEWD,LASCIVIOUS CHILD U/16; F.S. 800.04 (PRINCIPAL)</div>
<!--                        <div class="t3">--><!--</div>-->
                    </div>
                    <div class="cta">
                        <span class="btn sm special">Full Report</span>
                    </div>
                </div>
                <div class="body acc-body">
                    <div class="r-table two-cols">
                                                    <div class="row">
                                <div class="ttl">Name:</div>
                                <div class="val">Henry T Fischer</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Gender:</div>
                                <div class="val">Male</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Date of Birth:</div>
                                <div class="val">September 07, 1932</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Race:</div>
                                <div class="val">White</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Height:</div>
                                <div class="val">5ft 07in</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Weight:</div>
                                <div class="val">145</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Eye:</div>
                                <div class="val">Brown</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Hair:</div>
                                <div class="val">Brown</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Age:</div>
                                <div class="val">92</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Scars Marks:</div>
                                <div class="val">No markings found.</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Address:</div>
                                <div class="val">Last Reported Address - Out of State, Weston, MA 02493-2483</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">State:</div>
                                <div class="val">MA</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Location:</div>
                                <div class="val">Weston</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">ZipCode:</div>
                                <div class="val">02493</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Aliases:</div>
                                <div class="val">Henry Fischer;HENRY FISHER</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Changes:</div>
                                <div class="val">Home-Last Reported Address - Out of State, Weston, MA 02493-2483</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Is Predator:</div>
                                <div class="val">false</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Is Absconder:</div>
                                <div class="val">false</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Last Updated:</div>
                                <div class="val">June 28, 2024</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Created:</div>
                                <div class="val">June 28, 2024</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Last Synced:</div>
                                <div class="val">December 04, 2024</div>
                            </div>
                                                    <div class="row">
                                <div class="ttl">Is Synced:</div>
                                <div class="val">1</div>
                            </div>
                                            </div>
                </div>
            </div>
                    </div>
            </div>
    </div>

        
                                            </div>                    </div>
            </div>
        </main>
    </div>
<script type="text/javascript" src="/tspecV2/PremiumPropertyReport/shared/js/smoothscroll.js"></script>
<script type="text/javascript" src="/tspecV2/PremiumPropertyReport/shared/js/lazysizes.js"></script>
<script type="text/javascript" src="/tspecV2/PremiumPropertyReport/shared/js/app.js?v=1.1"></script>
<script type="text/javascript" src="https://code.highcharts.com/highcharts.js"></script>
<script type="text/javascript" src="https://code.highcharts.com/modules/exporting.js"></script>
<script type="text/javascript" src="https://code.highcharts.com/modules/accessibility.js"></script>
<script type="text/javascript" src="/tspecV2/PremiumPropertyReport/shared/js/highchartsOptionsColumn.js"></script>
</body>

<style>
    .nhm {
		width: 100%;
        margin: 30px 0;
        padding: 25px 30px;
        text-align: center;
        box-shadow: 0 0 15px 0 rgb(53 156 243 / 30%);
    }
	.nhm .ttl {
		font-size: 18px;
		font-weight: 700;
		line-height: 24px;
	}
</style>

<script src="/js/jquery.cookie.min.js"></script>
<script src="/js/jquery-ui.min.js"></script>
<script src="/js/modernizr.js"></script>
<script src="/js/jquery.slicknav.js"></script>
<script src="/js/slick.min.js"></script>
<script src="/js/smoothscroll.js"></script>
<script src="/js/lazysizes.js"></script>
<script src="/js/lightbox.js?v=1.1"></script>
<script src="/js/app.js?v=2.9.13"></script>
<script src="/js/menu.js?v=1.0"></script>
<script src="https://propertychecker.com/js/commonrend.js?m=64&amp;sKey=advanced-frontend"></script></body>
</html>
