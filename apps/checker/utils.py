import re
from apps.checker.models import (
    PropertyRisk, PermitAnalysis, PropertyQuestion, PropertyIssues,
    InspectionUploadStatus, ReportUploadStatus, CheckerRoomSummary, PropertyOpportunity, CheckerHomeQuality,
    ImageAnalysisStatus, RoomSummaryStatus
)
import time
import logging
from contextlib import contextmanager



logger = logging.getLogger("homescore")

def parse_property_question(question_str: str):
    logger.debug(f"🔍 Parsing property question: {question_str}")
    # Regular expression to match the desired pattern
    pattern = r"^(.*\?)\s*\(Importance:\s*(\w+)\)$"
    match = re.match(pattern, question_str)

    if match:
        question = match.group(1).strip()
        importance = match.group(2).strip().lower()
        logger.debug(f"✅ Successfully parsed question. Question: {question}, Importance: {importance}")
        return question, importance
    else:
        logger.warning(f"⚠️ Could not parse importance from question: {question_str}")
        return question_str, None

def get_report_data(checker_id: int):
    """
    Retrieve and format all report data for a given checker_id.

    Args:
        checker_id: The unique identifier for the checker instance

    Returns:
        dict: Formatted report data including risks, analyses, and status information
    """
    logger.info(f"🔄 Fetching report data for checker_id: {checker_id}")
    try:
        # Fetch all required data in a single block
        report_upload_status = ReportUploadStatus.objects.filter(checker_id=checker_id)
        inspection_upload_status = InspectionUploadStatus.objects.filter(checker_id=checker_id)

        # Check if any data exists for this checker_id
        if not (report_upload_status.exists() or inspection_upload_status.exists()):
            logger.warning(f"⚠️ No report or inspection data found for checker_id: {checker_id}")
            return None

        # Fetch remaining data only if we have a valid checker_id
        property_risk_data = PropertyRisk.objects.filter(checker_id=checker_id, section="age")
        permit_analysis_data = PermitAnalysis.objects.filter(checker_id=checker_id)
        permit_risks_data = PropertyRisk.objects.filter(checker_id=checker_id, section="permit")
        property_question_data = PropertyQuestion.objects.filter(checker_id=checker_id)
        property_opportunity_data = PropertyOpportunity.objects.filter(checker_id=checker_id)
        property_issues_data = PropertyIssues.objects.filter(checker_id=checker_id)
        image_analysis_status = ImageAnalysisStatus.objects.filter(checker_id=checker_id)
        room_summary_status = RoomSummaryStatus.objects.filter(checker_id=checker_id)
        room_summaries = CheckerRoomSummary.objects.filter(checker_id=checker_id)
        home_quality_data = CheckerHomeQuality.objects.filter(checker_id=checker_id)

        # Process age risks
        logger.debug(f"📊 Found {property_risk_data.count()} age risks")
        age_risk = [
            {"category": risk.category, "description": risk.description}
            for risk in property_risk_data
        ]

        # Process permit analyses
        logger.debug(f"📋 Found {permit_analysis_data.count()} permit analyses")
        permits_analysis = [
            {
                "description": permit.description,
                "permit_type": permit.permit_type.replace(";", " &"),
                "permit_date": permit.permit_date,
                "importance": permit.importance
            }
            for permit in permit_analysis_data
        ]

        # Process permit risks
        permit_risks = [
            {
                "category": risk.category,
                "description": risk.description,
                "risk_type": risk.risk_type,
                "severity": risk.severity
            }
            for risk in permit_risks_data
        ]

        # Process property questions
        logger.debug(f"❓ Found {property_question_data.count()} property questions")
        property_questions = {}
        for question in property_question_data:
            if question.category not in property_questions:
                property_questions[question.category] = {
                    "category": question.category,
                    "questions": []
                }

            q, i = parse_property_question(question.question)
            property_questions[question.category]["questions"].append({
                "question": q,
                "importance": i,
            })

        # Process property opportunities
        property_opportunities = [
            {
                "category": opportunity.category,
                "suggestions": [{
                    "title": opportunity.title,
                    "description": opportunity.description,
                    "estimated_cost": opportunity.estimated_cost,
                    "potential_value_add": opportunity.potential_value_add
                }]
            }
            for opportunity in property_opportunity_data
        ]

        # Process property issues
        property_issues = None
        if property_issues_data.exists():
            logger.debug(f"⚠️ Found {property_issues_data.count()} property issues")
            property_issues = [
                {
                    "issue": issue.issue,
                    "category": issue.category,
                    "source": issue.source,
                    "urgency": issue.urgency,
                    "context": issue.context,
                    "recommendation": issue.recommendation,
                    "cost_estimate_low": issue.cost_estimate_low,
                    "cost_estimate_high": issue.cost_estimate_high
                }
                for issue in property_issues_data
            ]

        # Process home quality data
        home_quality = None
        quality_data = home_quality_data.first()
        if quality_data:
            home_quality = {
                "score": quality_data.score,
                "condition": quality_data.condition,
                "description": quality_data.description
            }


        # Prepare basic status information by fetching the first object from querysets
        report_obj = report_upload_status.first()
        inspection_obj = inspection_upload_status.first()
        image_obj = image_analysis_status.first()
        room_obj = room_summary_status.first()

        status_info = _prepare_basic_status_info(
            report_obj,         # Pass the object or None
            inspection_obj,     # Pass the object or None
            image_obj,          # Pass the object or None
            room_obj,           # Pass the object or None
            room_summaries,     # This is expected to be a queryset/list
            home_quality_data   # This is expected to be a queryset/list
        )

        # Add overall status and task completion information
        if report_obj: # Check if the report object exists
            status_info["original_report_status"] = report_obj.upload_status

            # Determine overall status
            _update_overall_status(status_info, report_obj)

            # Add timestamps for report processing
            _add_report_timestamps(status_info, report_obj)

        # Add inspection status and timestamps if available
        if inspection_obj: # Check if the inspection object exists
            status_info["original_inspection_status"] = inspection_obj.upload_status
            _add_inspection_timestamps(status_info, inspection_obj)

        return {
            "age_risk": age_risk,
            "permits_analysis": permits_analysis,
            "permit_risks": permit_risks,
            "property_questions": list(property_questions.values()),
            "property_opportunities": property_opportunities,
            "property_issues": property_issues,
            "home_quality": home_quality,
            "status": status_info
        }

    except Exception as e:
        logger.error(f"❌ Error getting report data for checker_id {checker_id}: {str(e)}", exc_info=True)
        raise

def update_report_status_after_task(checker_id: str, task_type: str):
    """
    Update the report completion flags and overall status after a task completes.

    Args:
        checker_id: The checker ID for the report
        task_type: The type of task that completed ('property_analysis', 'context_analysis', 'room_summary')
    """
    try:
        report_status, created = ReportUploadStatus.objects.get_or_create(checker_id=checker_id)
        image_analysis_status_obj = ImageAnalysisStatus.objects.filter(checker_id=checker_id).first()

        updated_fields = []
        # Update the specific task completion flag
        if task_type == 'property_analysis' and not report_status.property_analysis_complete:
            report_status.property_analysis_complete = True
            updated_fields.append('property_analysis_complete')
        elif task_type == 'context_analysis' and not report_status.context_analysis_complete:
            report_status.context_analysis_complete = True
            updated_fields.append('context_analysis_complete')
        elif task_type == 'room_summary' and not report_status.room_summary_complete:
            report_status.room_summary_complete = True
            updated_fields.append('room_summary_complete')

        # Determine if room summary can be considered complete due to skipped images
        room_summary_effectively_complete = report_status.room_summary_complete
        if not room_summary_effectively_complete and image_analysis_status_obj and \
           image_analysis_status_obj.upload_status == StatusConstants.IMAGES_SKIPPED:
            # If images were skipped, room summary task won't run, so consider it complete for overall status.
            # We can also explicitly set the flag if desired, or rely on this logic for all_complete.
            # For clarity, let's ensure the flag is also set if we consider it done.
            if not report_status.room_summary_complete: # Check to avoid redundant save if already set
                report_status.room_summary_complete = True
                updated_fields.append('room_summary_complete')
            room_summary_effectively_complete = True

        # Check if all tasks are now effectively complete
        all_complete = (
            report_status.property_analysis_complete and
            report_status.context_analysis_complete and
            room_summary_effectively_complete # Use the potentially overridden value
        )

        new_status = ""
        if all_complete:
            new_status = StatusConstants.REPORT_COMPLETED
        # Only update to processing if not already completed or failed
        elif report_status.upload_status not in [StatusConstants.REPORT_COMPLETED, StatusConstants.REPORT_FAILED]:
             new_status = StatusConstants.REPORT_PROCESSING

        # Update status string only if it changes and we have other updates or status needs change
        if new_status and report_status.upload_status != new_status:
            report_status.upload_status = new_status
            updated_fields.append('upload_status')

        # Save all changes if any fields were updated
        if updated_fields:
            updated_fields.append('updated_at') # Always update timestamp if changing status/flags
            report_status.save(update_fields=updated_fields)
            logger.info(f"Updated report status flags={updated_fields}, status='{report_status.upload_status}' after {task_type} completion")
        else:
             logger.info(f"No status update needed for {checker_id} after {task_type} (already complete/flag set?)")

    except ReportUploadStatus.DoesNotExist:
        logger.error(f"❌ ReportUploadStatus not found for checker_id {checker_id} during status update.")
    except Exception as e:
        logger.error(f"❌ Error updating report status after task completion: {str(e)}", exc_info=True)


# Helper functions for status processing
def _prepare_basic_status_info(report_status_obj, inspection_status_obj, image_status_obj, room_status_obj, room_summaries, home_quality):
    """Prepare the basic status information dictionary using model objects."""
    return {
        "report_processing_status": report_status_obj.upload_status if report_status_obj else None,
        "inspection_processing_status": inspection_status_obj.upload_status if inspection_status_obj else None,
        "image_analysis_status": image_status_obj.upload_status if image_status_obj else None,
        "room_summary_status": room_status_obj.upload_status if room_status_obj else None,
        "has_inspection": inspection_status_obj is not None,
        "has_images": image_status_obj is not None and not any(
            status in (image_status_obj.upload_status if image_status_obj else "")
            for status in [StatusConstants.IMAGES_PENDING, StatusConstants.IMAGES_FAILED, StatusConstants.IMAGES_SKIPPED]
        ),
        "has_room_summaries": room_summaries.exists(),
        "has_home_quality": home_quality.exists()
    }

def _update_overall_status(status_info, report_obj):
    """Update the overall status for the API response based on task completion flags."""
    if not report_obj:
        status_info["overall_property_report_status"] = StatusConstants.STATUS_PENDING
        return

    # Use boolean flags for completion status
    prop_done = report_obj.property_analysis_complete
    ctx_done = report_obj.context_analysis_complete
    room_done = report_obj.room_summary_complete
    all_complete = prop_done and ctx_done and room_done

    # Store the specific status string for debugging/reference
    status_info["original_report_status"] = report_obj.upload_status

    # Determine overall status for API
    if report_obj.upload_status == StatusConstants.REPORT_FAILED:
        status_info["overall_property_report_status"] = StatusConstants.STATUS_FAILED
    elif all_complete:
        status_info["overall_property_report_status"] = StatusConstants.STATUS_COMPLETED
    else:
        # If not failed and not all complete, it's processing
        status_info["overall_property_report_status"] = StatusConstants.STATUS_PROCESSING

    # Add detailed progress information based on flags
    completed_tasks = []
    pending_tasks = []
    if prop_done: completed_tasks.append("Property Analysis")
    else: pending_tasks.append("Property Analysis")
    if ctx_done: completed_tasks.append("Context Analysis")
    else: pending_tasks.append("Context Analysis")
    if room_done: completed_tasks.append("Room Summary")
    else: pending_tasks.append("Room Summary")

    # Only show task lists if status is processing
    if status_info["overall_property_report_status"] == StatusConstants.STATUS_PROCESSING:
        status_info["completed_tasks"] = completed_tasks
        status_info["pending_tasks"] = pending_tasks
    # Optionally clear them otherwise
    # else:
    #     status_info.pop('completed_tasks', None)
    #     status_info.pop('pending_tasks', None)

def _add_report_timestamps(status_info, report):
    """Add formatted timestamps for report processing"""
    if report.created_at:
        status_info["report_created_at"] = report.created_at.strftime("%Y-%m-%d %H:%M:%S")

    if report.updated_at:
        status_info["report_updated_at"] = report.updated_at.strftime("%Y-%m-%d %H:%M:%S")
        
        # Only calculate duration for completed reports
        if report.created_at and report.upload_status == StatusConstants.REPORT_COMPLETED:
            duration_seconds = min(
                (report.updated_at - report.created_at).total_seconds(),
                600  # Cap at 10 minutes (600 seconds) as safety net
            )
            # Always show in seconds with one decimal place
            status_info["report_processing_duration"] = f"{duration_seconds:.1f} seconds"

def _add_inspection_timestamps(status_info, inspection):
    """Add formatted timestamps for inspection processing"""
    if inspection.created_at:
        status_info["inspection_created_at"] = inspection.created_at.strftime("%Y-%m-%d %H:%M:%S")

    if inspection.updated_at:
        status_info["inspection_updated_at"] = inspection.updated_at.strftime("%Y-%m-%d %H:%M:%S")

        # Calculate processing duration if both timestamps exist
        if inspection.created_at:
            duration_seconds = (inspection.updated_at - inspection.created_at).total_seconds()
            status_info["inspection_processing_duration"] = f"{duration_seconds:.2f} seconds"

# Status constants for standardization
class StatusConstants:
    # Overall status values
    STATUS_PENDING = "Pending"
    STATUS_PROCESSING = "processing"
    STATUS_COMPLETED = "completed"
    STATUS_FAILED = "failed"
    STATUS_SKIPPED = "skipped"

    # Task-specific status prefixes (still useful for logging/display)
    PREFIX_REPORT = "Report: "
    PREFIX_INSPECTION = "Inspection: "
    PREFIX_IMAGES = "Images: "
    PREFIX_ROOM_SUMMARY = "Room Summary: "

    # General status messages
    REPORT_INITIALIZING = "Report: Initializing"
    REPORT_PROCESSING = "Report: Processing" # Generic processing state
    REPORT_COMPLETED = "Report: Completed"
    REPORT_FAILED = "Report: Failed"

    # Task completion status
    PROPERTY_ANALYSIS_COMPLETE = "Property Analysis"
    CONTEXT_ANALYSIS_COMPLETE = "Context Analysis"
    ROOM_SUMMARY_COMPLETE = "Room Summary"

    IMAGES_PENDING = "Images: Pending"
    IMAGES_PROCESSING = "Images: Processing"
    IMAGES_COMPLETED = "Images: Completed"
    IMAGES_FAILED = "Images: Failed"
    IMAGES_SKIPPED = "Images: Skipped: No images found"

    ROOM_PENDING = "Room Summary: Pending"
    ROOM_PROCESSING = "Room Summary: Processing"
    ROOM_COMPLETED = "Room Summary: Completed"
    ROOM_FAILED = "Room Summary: Failed"
    ROOM_SKIPPED = "Room Summary: Skipped: No images found"


@contextmanager
def task_timer(task_name: str, checker_id: str = None):
    """Context manager for timing tasks with consistent formatting"""
    logger.debug(f"🚀 Starting task: {task_name}" + (f" for checker_id: {checker_id}" if checker_id else ""))
    task_start = time.perf_counter()  # Use perf_counter for more precise timing
    try:
        yield
    except Exception as e:
        logger.error(f"❌ Error in task {task_name}: {str(e)}", exc_info=True)
        raise
    finally:
        duration = time.perf_counter() - task_start
        if checker_id:
            logger.info(f"⏱️ {task_name} completed in {duration:.2f} seconds for checker_id: {checker_id}")
        else:
            logger.info(f"⏱️ {task_name} completed in {duration:.2f} seconds")
