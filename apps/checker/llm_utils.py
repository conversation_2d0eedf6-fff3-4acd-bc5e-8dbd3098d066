import logging
from django.conf import settings
from openai import AzureOpenAI

logger = logging.getLogger(__file__)

client = AzureOpenAI(
    api_key=settings.AZURE_OPENAI_API_KEY,
    api_version=settings.AZURE_OPENAI_API_VERSION,
    azure_endpoint=settings.AZURE_OPENAI_API_ENDPOINT
)

def basic_llm_response(prompt: str, context_str: str) -> str:
    context = "Use additional context below to answer the question: \n"+context_str
    message = prompt+' '+context

    messages = [{"role": "user", "content": message}]

    openai_response = client.chat.completions.create(
        model=settings.OPENAI_MODEL, messages=messages)
    response_body = openai_response.choices[0].message.content.strip()

    return response_body
