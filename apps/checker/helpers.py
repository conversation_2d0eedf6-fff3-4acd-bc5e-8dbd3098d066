# Standard library imports
import os
import re
import json
import time
import logging
from functools import wraps
from typing import Optional, Dict, Any
from collections import defaultdict


# Third-party imports
from celery import group
from pinecone import <PERSON>cone
from langchain_community.vectorstores import Pinecone as PineconeCommunity
from langchain.text_splitter import TokenTextSplitter
from langchain.schema import Document
import tiktoken
from azure.ai.formrecognizer import DocumentAnalysisClient
from azure.core.credentials import AzureKeyCredential
from openai import AzureOpenAI

# Django imports
from django.db import transaction
from django.conf import settings
from django.core.cache import cache

# Local application imports
from apps.reports.tasks import cached_with_logging
from apps.checker.models import PropertyIssues, CheckerImage, CheckerImageFeature, CheckerRoomSummary, CheckerHomeQuality
from langchain_openai import AzureOpenAIEmbeddings
from homescore.celery import app
from .models import ImageAnalysisStatus, RoomSummaryStatus
from .utils import StatusConstants

# Setup Logger
logger = logging.getLogger("homescore")  # Update logger to match settings.py configuration

client = AzureOpenAI(
    api_key=settings.AZURE_OPENAI_API_KEY,
    api_version=settings.AZURE_OPENAI_API_VERSION,
    azure_endpoint=settings.AZURE_OPENAI_API_ENDPOINT
)


@cached_with_logging(timeout=60*60*24*7)
def get_text(checker_id: str, url: str) -> tuple:
    """Extract text from document URL"""
    logger.info(f"🔄 Starting text extraction for checker_id: {checker_id}")

    try:
        client = DocumentAnalysisClient(
            endpoint=os.getenv("FR_ENDPOINT"),
            credential=AzureKeyCredential(os.getenv("FR_KEY"))
        )

        logger.info(f"📄 Analyzing document at URL: {url}")
        poller = client.begin_analyze_document_from_url("prebuilt-read", url)
        result = poller.result()

        pages = []
        docs = []

        logger.info("📑 Processing document pages")
        for page_num, page in enumerate(result.pages):
            page_content = f"page {page_num+1}:" + "".join([line.content for line in page.lines])
            pages.append(page_content)

            docs.append(Document(
                page_content=f"page: {page_num}\n{page_content}",
                metadata={
                    "checker_id": checker_id,
                    "source": f"pg: {page_num}"
                }
            ))
        logger.debug(f"✓ Processed pages: {len(pages)}")

        splitter = TokenTextSplitter(chunk_size=1000, chunk_overlap=250)
        docs = splitter.split_documents(docs)
        logger.info(f"✅ Text extraction complete - {len(pages)} pages processed")
        return pages, docs

    except Exception as e:
        logger.error(f"❌ Text extraction failed: {str(e)}", exc_info=True)
        raise

def truncate_input(text: str, token_limit: int = 3000) -> str:
    """Truncate text to fit within token limit"""
    encoding = tiktoken.encoding_for_model(settings.OPENAI_MODEL)
    tokens = encoding.encode(text)
    return encoding.decode(tokens[:token_limit]) if len(tokens) > token_limit else text

@cached_with_logging(timeout=60*60*24*7)
def get_index_summary(checker_id: str, file_url) -> str:
    """Get summary using semantic search and LLM"""
    logger.info(f"🔄 Starting semantic search for checker_id: {checker_id}")

    try:
        embeddings = AzureOpenAIEmbeddings(model=settings.AZURE_OPENAI_EMBEDDINGS_MODEL)
        doc_search = PineconeCommunity.from_existing_index(settings.PINECONE_INFOPAY_INDEX, embeddings)

        logger.info("🔍 Searching for relevant documents")
        similar_docs = []
        seen_content = set()

        for issue_type in settings.MAJOR_ISSUES:
            short_issue = issue_type.split(":", 1)[0]
            logger.debug(f"Searching for issue type: {short_issue}")
            docs = doc_search.similarity_search_with_score(
                issue_type,
                k=settings.TOPK,
                filter={"checker_id": checker_id}
            )

            # Filter out docs with low similarity score
            for doc, score in docs:
                if (score > settings.SIMILARITY_THRESHOLD and
                    doc.page_content not in seen_content):
                    similar_docs.append((doc, score))
                    seen_content.add(doc.page_content)

        logger.info(f"✅ Found total {len(docs)} documents from the index")
        logger.info(f"✅ Found {len(similar_docs)} relevant documents for all the issues")

        if not similar_docs:
            logger.warning("⚠️ No relevant documents found")
            return None

        # Prepare context from similar docs
        context = "Use additional context below:\n" + "\n".join([
            f"page: {doc.metadata.get('source')} (score: {score:.2f})\n{doc.page_content}"
            for doc, score in similar_docs
        ])

        # Get LLM response
        response = client.chat.completions.create(
            model=settings.OPENAI_MODEL,
            messages=[
                {"role": "system", "content": settings.INSPECTION_SUMMARY_PROMPT},
                {"role": "user", "content": truncate_input(context, 12000)}
            ],
            tools=settings.ISSUE_TOOLS,
            tool_choice="required",
            temperature=0
        )

        logger.info("✅ Summary generation complete")
        return response

    except Exception as e:
        logger.error(f"❌ Summary generation failed: {str(e)}", exc_info=True)
        raise

def parse_and_store_json_summary(checker_id: str, summary: str) -> str:
    """Parse and store summary in database"""
    logger.info(f"🔄 Starting summary parsing for checker_id: {checker_id}")

    if not summary:
        logger.error("❌ Empty summary provided")
        raise ValueError("Empty summary")

    logger.info(f"🔍 Parsing summary")
    try:
        # Handle both string and dict inputs
        if isinstance(summary, str):
            parsed = json.loads(summary)
        elif isinstance(summary, dict):
            parsed = summary
        else:
            raise ValueError(f"Invalid summary type: {type(summary)}")

        issues = parsed.get("issues", [])

        if not issues:
            logger.warning("⚠️ No issues found in summary")
            raise ValueError("No issues found in summary")

        logger.info(f"🗑️ Clearing existing issues for checker_id: {checker_id}")
        PropertyIssues.objects.filter(checker_id=checker_id).delete()

        logger.info(f"💾 Storing {len(issues)} new issues")
        # Store new issues
        results = []
        for issue in issues:
            issue["checker_id"] = checker_id

            # Basic issue validation
            if not issue.get("issue") or not issue.get("urgency"):
                logger.warning(f"Skipping issue due to missing required fields: {issue}")
                continue

            # Ensure cost estimates are valid
            if (issue.get("cost_estimate_low") is not None and issue.get("cost_estimate_high") is not None
                and issue["cost_estimate_low"] > issue["cost_estimate_high"]):
                logger.warning(f"Skipping issue due to invalid cost estimates: {issue}")
                continue

            # Create or update the issue
            issue_obj, _ = PropertyIssues.objects.update_or_create(**issue)
            issue_obj.save()
            results.append(
                f"{issue.get('issue')} [{issue.get('source')}] ({issue.get('urgency')} Urgency)\n"
                f"{issue.get('category')}\n"
                f"{issue.get('context')}\n"
                f"{issue.get('recommendation')}\n"
                f"Estimate: {issue.get('cost_estimate_low')}-{issue.get('cost_estimate_high')}"
            )

        logger.info("✅ Summary parsing and storage complete")
        return "\n\n".join(results)

    except json.JSONDecodeError as e:
        logger.error(f"❌ JSON parsing failed: {str(e)}")
        raise ValueError(f"Invalid JSON format: {e}") from e

def upload_documents_to_pinecone(docs, index_name, embeddings):
    """
    Uploads documents to Pinecone index.

    Args:
        docs (List[Document]): List of documents to upload
        index_name (str): Name of Pinecone index
        embeddings: Embedding model instance

    Returns:
        Pinecone: The Pinecone vectorstore instance
    """

    start_time = time.time()
    try:
        logger.debug(f"🔌 Initializing Pinecone connection", extra={
            "index_name": index_name
        })

        pc = Pinecone(api_key=settings.PINECONE_INFOPAY_API_KEY)
        init_time = time.time()
        logger.debug(f"⚡ Pinecone initialization completed", extra={
            "duration": f"{init_time - start_time:.2f}s"
        })
        logger.debug(f"🔍 Connecting to existing Pinecone with index: {index_name}")

        doc_search = PineconeCommunity.from_existing_index(index_name, embeddings)
        index_time = time.time()
        logger.info(f"✓ Index connection established in {index_time - init_time:.2f} seconds")

        logger.debug("📤 Uploading documents to Pinecone", extra={
            "doc_count": len(docs),
            "index_name": index_name
        })

        doc_search.add_documents(docs)
        upload_time = time.time()
        logger.info(f"✅ Document upload completed in {upload_time - index_time:.2f} seconds")

        total_time = time.time() - start_time
        logger.info("🏁 Process completed", extra={
            "total_duration": f"{total_time:.2f}s",
            "doc_count": len(docs)
        })
        return doc_search

    except Exception as e:
        elapsed = time.time() - start_time
        logger.error("❌ Upload failed", exc_info=True, extra={
            "duration": f"{elapsed:.2f}s",
            "error": str(e)
        })
        raise


def split_and_upload_to_pinecone(docs, embeddings, index_name, chunk_size=1000, chunk_overlap=250):
    """
    Splits documents into smaller chunks and uploads them to Pinecone.

    Args:
        docs (List[Document]): List of documents to split and upload
        embeddings: Embedding model instance
        index_name (str): Name of Pinecone index
        chunk_size (int): Size of each document chunk
        chunk_overlap (int): Overlap between chunks

    Returns:
        Pinecone: The Pinecone vectorstore instance
    """
    start_time = time.time()
    try:
        logger.debug("✂️ Starting document split", extra={
            "chunk_size": chunk_size,
            "chunk_overlap": chunk_overlap,
            "doc_count": len(docs)
        })

        splitter = TokenTextSplitter(chunk_size=chunk_size, chunk_overlap=chunk_overlap)
        split_docs = splitter.split_documents(docs)
        split_time = time.time()

        logger.debug("📑 Document split completed", extra={
            "original_docs": len(docs),
            "split_chunks": len(split_docs),
            "duration": f"{split_time - start_time:.2f}s"
        })

        logger.info("🚀 Starting upload to Pinecone")
        doc_search = upload_documents_to_pinecone(split_docs, index_name, embeddings)
        upload_time = time.time()

        total_time = upload_time - start_time
        logger.info("🎉 Process completed", extra={
            "total_duration": f"{total_time:.2f}s"
        })
        return doc_search

    except Exception as e:
        elapsed = time.time() - start_time
        logger.error("❌ Split and upload failed", exc_info=True, extra={
            "duration": f"{elapsed:.2f}s",
            "error": str(e)
        })
        raise

def extract_tool_response(response: Any, default_return: Dict = None, context: str = "critical") -> Optional[Dict]:
    """
    Safely extract and parse tool response from OpenAI API response.

    Args:
        response: OpenAI API response object
        default_return: Default value to return if parsing fails
        context: Context for error logging - "critical" logs errors, "optional" logs warnings

    Returns:
        Parsed JSON response or default_return if parsing fails
    """
    try:
        logger.info("🔄 Starting tool response extraction")

        response_data = response.model_dump() if hasattr(response, "model_dump") else response
        logger.debug("⏬ Raw response data analysis")

        choices = response_data.get("choices", [])
        if not choices:
            if context == "critical":
                logger.error("❌ No choices in response")
            else:
                logger.warning("⚠️ No choices in response")
            return default_return

        message = choices[0].get("message", {})
        tool_calls = message.get("tool_calls", [])
        if not tool_calls:
            if context == "critical":
                logger.error("❌ No tool calls in response")
            else:
                logger.debug("ℹ️ No tool calls in response (expected for room summary processing)")
            return default_return

        function_call = tool_calls[0].get("function", {})
        tool_response = function_call.get("arguments", "{}")
        logger.debug(f"🔎 Tool response string: {tool_response}")

        try:
            parsed_response = json.loads(tool_response)
            logger.info("✔️ Tool response parsed successfully")
            return parsed_response
        except json.JSONDecodeError as e:
            if context == "critical":
                logger.error(f"❌ Failed to parse tool response: {e}")
            else:
                logger.warning(f"⚠️ Failed to parse tool response: {e}")
            return default_return

    except Exception as e:
        if context == "critical":
            logger.error(f"❌ Error extracting tool response: {str(e)}", exc_info=True)
        else:
            logger.warning(f"⚠️ Error extracting tool response: {str(e)}")
        return default_return

def wait_for_docs(index_name: str, embeddings, checker_id: int, max_retries=5, wait_seconds=10):
    """
    Wait for documents to be indexed in Pinecone.

    Args:
        index_name (str): Name of Pinecone index
        embeddings: Embedding model instance
        checker_id (int): Checker ID to filter documents
        max_retries (int): Maximum number of retries
        wait_seconds (int): Wait time between retries in seconds

    Returns:
        bool: True if documents are found, False otherwise
    """
    start_time = time.time()
    logger.info(f"🔄 Starting document wait for checker_id: {checker_id}")

    doc_search = PineconeCommunity.from_existing_index(index_name, embeddings)
    for attempt in range(max_retries):
        logger.debug(f"📍 Attempt {attempt + 1}/{max_retries}")
        docs = doc_search.similarity_search_with_score("inspection", k=3, filter={"checker_id": checker_id})
        if docs:
            total_time = time.time() - start_time
            logger.info(f"✅ Documents found after {total_time:.2f}")
            return True
        logger.debug(f"⏳ Waiting {wait_seconds} seconds before next attempt")
        time.sleep(wait_seconds)

    total_time = time.time() - start_time
    logger.warning(f"⚠️ Documents not found after {total_time:.2f} seconds")
    return False

# Synchronous function to process all image descriptions
@app.task(queue="image_task")
def get_all_descriptions(image_urls, checker_id):
    """Process all images and return their descriptions with checker_id."""
    logger.info(f"Processing {len(image_urls)} images for checker_id: {checker_id}")

    status, _ = ImageAnalysisStatus.objects.get_or_create(checker_id=checker_id)
    status.update_status(StatusConstants.IMAGES_PROCESSING)

    descriptions = []
    valid_urls_count = 0
    skipped_urls_count = 0

    try:
        for url in image_urls:
            try:
                # Check if URL is valid before processing
                if not is_valid_image_url(url):
                    logger.warning(f"Skipping invalid image URL format (not HTTP/HTTPS): {url}")
                    descriptions.append(None)
                    skipped_urls_count += 1
                    continue

                description = get_image_description(url)
                descriptions.append(description)
                valid_urls_count += 1

            except Exception as e:
                logger.error(f"Failed to get description for {url}: {e}")
                descriptions.append(None)
                skipped_urls_count += 1

        # Update status using standardized status constants
        if valid_urls_count > 0:
            # If any images were processed successfully, mark as completed
            status.update_status(StatusConstants.IMAGES_COMPLETED)
        else:
            # If no images were processed (all skipped), mark as skipped
            status.update_status(StatusConstants.IMAGES_SKIPPED)

        # Log detailed counts for internal debugging
        logger.info(f"Image processing summary for checker_id {checker_id}: {valid_urls_count} processed, {skipped_urls_count} skipped")

        return checker_id, descriptions  # Return both checker_id and descriptions

    except Exception as e:
        status.update_status(StatusConstants.IMAGES_FAILED)
        logger.error(f"Image analysis failed: {str(e)}")
        raise

@app.task(queue="image_task")
def handle_error(request, exc, traceback):
    """Handle errors in the task chain."""
    logger.error(f"Task {request.id} failed: {exc}")
    return None

def is_valid_image_url(url):
    """
    Check if the URL is a valid HTTP/HTTPS URL for image processing.

    Args:
        url (str): The URL to validate

    Returns:
        bool: True if the URL is valid, False otherwise
    """
    # Check if the URL starts with http:// or https://
    if url and isinstance(url, str):
        return url.startswith('http://') or url.startswith('https://')
    return False

# Synchronous function to get the description for each image
@app.task(queue="image_task")
def get_image_description(url):
    logger.info(f"Fetching description for image URL: {url}")

    # Validate URL before processing
    if not is_valid_image_url(url):
        logger.warning(f"Skipping invalid image URL format (not HTTP/HTTPS): {url}")
        return []  # Return empty result without raising exception

    messages = [
        {
            "role": "user",
            "content": [
                {"type": "text", "text": settings.IMAGE_ANALYSIS_PROMPT},
                {"type": "image_url", "image_url": {"url": url}}
            ]
        }
    ]

    image_analysis = None

    try:
        response = client.chat.completions.create(
            model=settings.OPENAI_QUIZ_MODEL,
            messages=messages,
            tools=settings.IMAGE_ANALYSIS_TOOLS,
            tool_choice="required",
            temperature=0
        )
        image_analysis = extract_tool_response(response, context="critical")

        logger.info(f"Got description for image URL: {url}")
        try:
            return image_analysis if image_analysis else []
        except json.JSONDecodeError:
            logger.error(f"JSON decode error for image URL: {url}")
            return []

    except Exception as e:
        logger.error(f"API Error while processing image URL: {url}. Error: {e}")
        return []

# Synchronous function to save image descriptions to the database
@app.task(queue="image_task")
def save_to_database(image_description, checker_id, url):
    """Save single image description to database.

    Args:
        image_description (dict): Single image description
        checker_id (str): Checker ID
        url (str): Image URL
    """

    if not image_description:
        logger.error(f"Empty description for image URL: {url}")
        return

    room_types = image_description.get("room_type", {})
    if not room_types:
        logger.error(f"No room_type data found in image description for URL: {url}")
        return

    try:
        with transaction.atomic():
            # Create images with room types
            image_objects = []
            for room_type, features in room_types.items():
                img_obj = CheckerImage(
                    checker_id=checker_id,
                    image_url=url,
                    room_type=room_type.lower()  # Room types from AI already match enum
                )
                image_objects.append(img_obj)

            # Bulk create images
            created_images = CheckerImage.objects.bulk_create(
                image_objects,
                ignore_conflicts=True
            )

            # Get saved images
            saved_images = CheckerImage.objects.filter(
                checker_id=checker_id,
                image_url=url
            )

            # Create mapping of room types to saved images
            room_type_to_image = {
                str(img.room_type.value): img for img in saved_images
            }

            # Process features
            image_feature_objects = []
            for room_type, features in room_types.items():
                image_instance = room_type_to_image.get(room_type.lower())

                if not image_instance:
                    logger.warning(
                        f"Room type mismatch - Original: {room_type}, "
                        f"Available types: {list(room_type_to_image.keys())}"
                    )
                    continue

                # Process features for this image
                for feature, details in features.items():
                    if not all(k in details for k in ("value", "summary")):
                        logger.error(f"Missing required feature data for {feature}")
                        continue
                    logger.debug(f"Saving feature: {feature} for image URL: {url}")
                    image_feature = CheckerImageFeature(
                        image=image_instance,
                        feature_type=feature.lower().replace(" ", "_"),  # Feature types from AI already match enum
                        description=details["summary"],
                        choice=details["value"].lower()
                    )
                    image_feature_objects.append(image_feature)

            # Bulk create features
            if image_feature_objects:
                CheckerImageFeature.objects.bulk_create(
                    image_feature_objects,
                    ignore_conflicts=True
                )

            logger.info(f"✅ Saved {len(created_images)} images and {len(image_feature_objects)} features")

    except Exception as e:
        logger.error(f"❌ Failed to save image data: {str(e)}")
        raise

    logger.info(f"All image details for {url} saved to the database.")

@app.task(queue="image_task")
def aggregate_room_features(checker_id: str) -> Dict:
    """
    Task function to aggregate room features from the CheckerImage model for a specific checker_id,
    process the data, and generate a final summary.

    Args:
        checker_id (str): The unique identifier for the checker instance

    Returns:
        Dict: Aggregated room features with their descriptions and values
    """
    logger.info(f"🔄 Starting room feature aggregation for checker_id: {checker_id}")

    try:
        # Fetch image details from the database based on the checker_id
        images = CheckerImage.objects.filter(checker_id=checker_id).select_related()

        if not images.exists():
            logger.warning(f"⚠️ No images found for checker_id: {checker_id}")
            return {}

        logger.info(f"📸 Found {images.count()} images to process")

        # Aggregating image details into a dictionary
        # Using defaultdict to automatically handle nested dictionary creation
        image_description = defaultdict(lambda: defaultdict(lambda: {"values": set(), "summaries": []}))

        for image in images:
            room_type = image.room_type.value  # Using enum value for consistency

            # Fetch all features related to the image using reverse relation
            features = image.checkerimagefeature_set.all()
            logger.debug(f"Processing {features.count()} features for room type: {room_type}")

            for feature in features:
                feature_type = feature.feature_type.value  # Using enum value
                value = feature.choice
                summary = feature.description

                # Validate feature data before adding
                if value and summary:
                    image_description[room_type][feature_type]["values"].add(value)
                    image_description[room_type][feature_type]["summaries"].append(summary)
                    logger.debug(f"Added feature: {feature} for room: {room_type}")
                else:
                    logger.warning(f"Skipping invalid feature data - Room: {room_type}, Feature: {feature_type}")

        # Convert sets to lists for JSON serialization
        processed_description = {
            room: {
                feature: {
                    "values": list(details["values"]),
                    "summaries": details["summaries"]
                }
                for feature, details in features.items()
            }
            for room, features in image_description.items()
        }

        logger.info(f"✅ Successfully aggregated features for {len(processed_description)} rooms")
        return processed_description

    except Exception as e:
        logger.error(f"❌ Error aggregating room features: {str(e)}", exc_info=True)
        return {}

# Function to generate the final summary for each room type
@app.task(rate_limit="1/s", queue="image_task")
def process_feature(feature, feature_details):
    """Process and generate summary for a single feature."""
    logger.info(f"🔄 Processing feature: {feature}")

    try:
        # Build feature summary list
        feature_summary_list = []
        for value, summary in zip(feature_details["values"], feature_details["summaries"]):
            if not value or not summary:
                logger.warning(f"⚠️ Skipping invalid value/summary pair for feature: {feature}")
                continue
            feature_summary_list.append(f"{value} {feature} has summary like {summary}.")

        if not feature_summary_list:
            logger.warning(f"⚠️ No valid summaries found for feature: {feature}")
            return ""

        combined_summaries = ' '.join(feature_summary_list)
        detailed_message = f"The feature is {feature}:\n{combined_summaries}"

        response = client.chat.completions.create(
            model=settings.OPENAI_QUIZ_MODEL,
            messages=[
                {"role": "system", "content": settings.BY_ROOM_PROMPT},
                {"role": "user", "content": detailed_message}
            ],
            temperature=0,
        )

        # First try to extract tool response (use "optional" context for room summary processing)
        feature_summary = extract_tool_response(response, default_return=None, context="optional")

        # If no tool response, get content directly from message
        if feature_summary is None:
            response_data = response.model_dump() if hasattr(response, "model_dump") else response
            choices = response_data.get("choices", [])
            if choices:
                message = choices[0].get("message", {})
                feature_summary = message.get("content", "").strip()
            else:
                feature_summary = ""

        logger.info(f"✅ Successfully processed feature: {feature}")
        return feature_summary

    except Exception as e:
        logger.error(f"❌ Error processing feature {feature}: {str(e)}", exc_info=True)
        return ""

def capitalize_string(s: str) -> str:
    return " ".join(word.capitalize() for word in re.split(r"[-_\s]+", s))

def normalize_string(s: str) -> str:
    return re.sub(r"[-_]+", " ", s).lower()

@app.task(rate_limit="1/s", queue="image_task")
def process_room(room: str, features: dict, checker_id: str) -> dict:
    """Process room features and generate room summary."""
    logger.info(f"🔄 Processing room: {room} for checker_id: {checker_id}")

    try:
        room_normalized = normalize_string(room)
        room_capitalized = capitalize_string(room_normalized)

        room_data = {
            "room_type": room_capitalized,
            "features": []
        }

        # Track summaries for Pinecone
        room_feature_summaries = []

        # Process each feature
        for feature, feature_details in features.items():
            try:
                feature_summary = process_feature(feature, feature_details)
                if not feature_summary:
                    continue

                processed_values = [
                    value.capitalize()
                    for value in feature_details["values"]
                    if value is not None
                ]

                # Add to room data
                room_data["features"].append({
                    "feature": capitalize_string(feature),
                    "summary": feature_summary,
                    "values": processed_values
                })

                # Save to database
                room_summary, created = CheckerRoomSummary.objects.update_or_create(
                    checker_id=checker_id,
                    room_type=room_normalized,
                    feature_type=feature,
                    defaults={
                        "summary": feature_summary,
                        "values": processed_values
                    }
                )

                room_feature_summaries.append(
                    f"{capitalize_string(feature)}: {feature_summary}"
                )

            except Exception as e:
                logger.error(f"❌ Error processing feature {feature}: {str(e)}")
                continue

        # Index room summary in Pinecone if we have summaries
        if room_feature_summaries:
            try:
                room_summary_text = "\n".join(room_feature_summaries)
                embeddings = AzureOpenAIEmbeddings(
                    model=settings.AZURE_OPENAI_EMBEDDINGS_MODEL,
                )

                doc = Document(
                    page_content=room_summary_text,
                    metadata={
                        "checker_id": checker_id,
                        "room_type": room_normalized,
                        "embedding_type": "room_summary"
                    }
                )

                upload_documents_to_pinecone(
                    [doc],
                    settings.PINECONE_INFOPAY_INDEX,
                    embeddings
                )
            except Exception as e:
                logger.error(f"❌ Failed to index room summary: {str(e)}")

        logger.info(f"✅ Successfully processed room: {room}")
        return room_data

    except Exception as e:
        logger.error(f"❌ Error processing room {room}: {str(e)}", exc_info=True)
        return {"room_type": room, "features": []}

@app.task(rate_limit="1/s", queue="image_task")
def generate_room_summary(checker_id: str) -> dict:
    """Generate comprehensive room summaries for a property."""
    logger.info(f"🔄 Generating room summary for checker_id: {checker_id}")

    status, _ = RoomSummaryStatus.objects.get_or_create(checker_id=checker_id)
    status.update_status(StatusConstants.ROOM_PROCESSING)

    try:
        # Get aggregated room features
        image_description = aggregate_room_features(checker_id)
        if not image_description:
            logger.warning(f"⚠️ No room features found for checker_id: {checker_id}")
            status.update_status(StatusConstants.ROOM_COMPLETED)  # Still mark as completed
            # Ensure the overall status reflects this task's completion even if no features were found
            from .utils import update_report_status_after_task
            update_report_status_after_task(checker_id, 'room_summary')
            return {"images": {}}

        # Process each room
        room_details = {"images": {}}
        for room, features in image_description.items():
            try:
                room_data = process_room(room, features, checker_id)
                if room_data["features"]:
                    room_details["images"][room_data["room_type"]] = room_data
            except Exception as e:
                logger.error(f"❌ Failed to process room {room}: {str(e)}")
                continue

        status.update_status(StatusConstants.ROOM_COMPLETED)
        # Add quality analysis
        quality_assessment = analyze_room_quality(checker_id)
        room_details["quality_rooms"] = quality_assessment

        # Update the overall report status to reflect completion of room summary task
        from .utils import update_report_status_after_task
        update_report_status_after_task(checker_id, 'room_summary')

        logger.info(f"✅ Successfully generated summaries and quality assessment")
        return room_details

    except Exception as e:
        logger.error(f"❌ Room summary generation failed: {str(e)}", exc_info=True)
        status.update_status(StatusConstants.ROOM_FAILED)

        # Update the overall report status even in case of failure
        from .utils import update_report_status_after_task
        update_report_status_after_task(checker_id, 'room_summary')

        return {"images": {}, "quality_rooms": {
            "score": 0,
            "condition": "error",
            "description": "Failed to analyze quality"
        }}

@app.task(queue="image_task")
def map_task(results, image_urls):
    """Maps descriptions to save_to_database tasks and runs them in parallel.

    Args:
        results (tuple): (checker_id, descriptions) from get_all_descriptions
        image_urls (list): List of image URLs
    """
    checker_id, descriptions = results  # Unpack the results
    logger.info(f"Mapping {len(descriptions)} descriptions to {len(image_urls)} URLs for checker_id: {checker_id}")

    valid_count = 0
    skipped_count = 0

    # Process each description in parallel
    for url, description in zip(image_urls, descriptions):
        if description:  # Only process if we have a valid description
            try:
                save_to_database.delay(description, checker_id, url)
                valid_count += 1
            except Exception as e:
                logger.error(f"Failed to create task for URL {url}: {e}")
                skipped_count += 1
        else:
            # Log skipped URLs at debug level to avoid cluttering logs
            logger.debug(f"Skipping URL with no description: {url}")
            skipped_count += 1

    # Log processing summary for internal debugging
    logger.info(f"Map task summary for checker_id {checker_id}: {valid_count} valid images processed, {skipped_count} invalid/failed images skipped")

    # Update image status if needed (only if all images were skipped)
    if valid_count == 0 and skipped_count > 0:
        # If all images were skipped, update the status to IMAGES_SKIPPED
        try:
            status, _ = ImageAnalysisStatus.objects.get_or_create(checker_id=checker_id)
            status.update_status(StatusConstants.IMAGES_SKIPPED)
        except Exception as e:
            logger.error(f"Failed to update image status: {e}")

    return True  # Return success to continue the chain

@app.task(rate_limit="1/s", queue="image_task")
def analyze_room_quality(checker_id: str) -> dict:
    """Analyze room details to generate overall house quality assessment."""
    logger.info(f"🔄 Starting room quality analysis for checker_id: {checker_id}")

    try:
        # Get all room summaries for this checker_id
        room_summaries = CheckerRoomSummary.objects.filter(checker_id=checker_id)

        if not room_summaries.exists():
            logger.warning(f"⚠️ No room data found for checker_id: {checker_id}")
            default_response = {
                "score": 0,
                "condition": "unknown",
                "description": "Insufficient data to assess property quality"
            }
            # Save default response to database
            room_quality, created = CheckerHomeQuality.objects.update_or_create(
                checker_id=checker_id,
                defaults=default_response
            )
            return default_response

        # Group summaries by room
        room_data = defaultdict(list)
        for summary in room_summaries:
            room_data[summary.room_type].append({
                "feature": summary.feature_type,
                "summary": summary.summary,
                "values": summary.values
            })

        # Prepare evaluation context for LLM
        evaluation_context = []
        for room_type, features in room_data.items():
            room_context = f"Room: {room_type}\n"
            for feature in features:
                room_context += f"- {feature['feature']}: {feature['summary']}\n"
            evaluation_context.append(room_context)

        # Get quality assessment from LLM
        response = client.chat.completions.create(
            model=settings.OPENAI_QUIZ_MODEL,
            messages=[
                {"role": "system", "content": settings.QUALITY_ANALYSIS_PROMPT},
                {"role": "user", "content": "\n".join(evaluation_context)}
            ],
            tools=settings.QUALITY_ANALYSIS_TOOLS,
            tool_choice="required",
            temperature=0
        )

        quality_assessment = extract_tool_response(response, default_return=None, context="critical")

        if not quality_assessment:
            raise ValueError("Failed to generate quality assessment")

        # Create dictionary with values to save/return
        quality_data = {
            "score": quality_assessment.get("score", 0),  # Use dict.get() with default value
            "condition": quality_assessment.get("condition", "unknown"),
            "description": quality_assessment.get("description", "No description provided")
        }

        # Save to database
        room_quality, created = CheckerHomeQuality.objects.update_or_create(
            checker_id=checker_id,
            defaults=quality_data
        )

        logger.info(f"✅ Successfully analyzed room quality for checker_id: {checker_id}")
        return quality_data

    except Exception as e:
        logger.error(f"❌ Room quality analysis failed: {str(e)}", exc_info=True)
        error_response = {
            "score": 0,
            "condition": "error",
            "description": f"Analysis failed: {str(e)}"
        }
        # Save error state to database
        room_quality, created = CheckerHomeQuality.objects.update_or_create(
            checker_id=checker_id,
            defaults=error_response
        )
        return error_response