How to test checker chat

#Chat with inspection report

1. upload HTML
curl --location 'http://localhost:8000/internal/reports/upload' \
--header 'reportid: 321321' \
--header 'API-KEY: 9d1abea64691ccd8be0ded5c1905ad793caa3b35' \
--header 'Cookie: csrftoken=FArsNS2OuI1f8CHFkKSDLKFHlwMH8q1O; sessionid=xh3m1v4xj2i46xc7b3e1xj9nielolxqv' \
--form 'file=@"/homescore/homescore/apps/checker/2view-source_https___propertychecker.com_srep_675a0d76873eca752312d8f7_84-wellesley-st--weston--ma-02493_.html"'

2. upload inspection - checker_id should be in header

curl --location 'http://localhost:8000/internal/reports/issues/upload_doc' \
--header 'Referer: http://localhost:8000' \
--form 'file=@"postman-cloud:///1ef4b028-c17c-4300-b5f9-c7a7087601e4"' \
--form 'checker_id="321321"'

3. cards to see context

curl --location 'http://localhost:8000/internal/reports/info/321321' \
--header 'Authorization: ••••••' \

"property_issues": [
            {
                "issue": "Dehumidification",
                "category": "HVAC",
                "source": "pg 10",
                "urgency": "Medium",
                "context": "A properly sized dehumidification system should be installed in the basement and crawlspace areas to prevent mold growth and maintain a healthy indoor environment.",
                "recommendation": "Hire an HVAC professional to install a dehumidification system in the basement and crawlspace areas.",
                "cost_estimate_low": 1500,
                "cost_estimate_high": 3000
            }

4. ask question about this

curl --location 'http://localhost:8000/internal/chat/321321/new_message/' \
--header 'Referer: http://localhost:8000' \
--header 'Content-Type: application/json' \
--header 'Authorization: ••••••' \
--data '{
    "chat": 321321,
    "message_type": "HUMAN", 
    "content": "What is recommended for this house?",
    "issue": "Dehumidification"
}'

curl --location 'http://localhost:8000/internal/chat/321321/get_response/93e7458d-d2e3-4040-864b-256f5136d712/' \
--header 'Referer: http://localhost:8000' \
--header 'Authorization: ••••••' \
--header 'Cookie: csrftoken=FArsNS2OuI1f8CHFkKSDLKFHlwMH8q1O; sessionid=xh3m1v4xj2i46xc7b3e1xj9nielolxqv' \
--data ''

The report recommends installing a properly sized dehumidification system in the basement and crawlspace areas of the house. This recommendation arises from the need to maintain a normal fungal ecology (condition 1) and prevent mold growth, as confirmed by the laboratory analysis of air and surface samples.


5. ask a not relevent question

curl --location 'http://localhost:8000/internal/chat/321321/new_message/' \
--header 'Referer: http://localhost:8000' \
--header 'Content-Type: application/json' \
--header 'Authorization: ••••••' \
--header 'Cookie: csrftoken=FArsNS2OuI1f8CHFkKSDLKFHlwMH8q1O; sessionid=xh3m1v4xj2i46xc7b3e1xj9nielolxqv' \
--data '{
    "chat": 321321,
    "message_type": "HUMAN", 
    "content": "What'\''s wrong with the roof?",
    "issue": "Dehumidification"
}'

This document does not provide information specifically about the condition of the roof. The provided excerpts mostly discuss topics related to humidity levels, mold growth, and other environmental conditions inside the house.


#This use case no longer revelevent - chatting with HTML

1. upload report with reportid 1234567
curl --location 'http://localhost:8000/internal/reports/upload' \
--header 'Cookie: csrftoken=RY19kAWVitChxsV8yi1k06vwcCZScxaJ; sessionid=f4any6v3y1hgwsu55ziyq2twc7mu3fim; csrftoken=iJjx2zDcXO5Tf5lhaKOyZSiwKwvvkUQP; sessionid=itnl9jvgm54gglju47im28cquzn4e6ly' \
--header 'reportid: 1234567' \
--form 'file=@"/Users/<USER>/code/homescore/homescore/apps/checker/2view-source_https___propertychecker.com_srep_675a0d76873eca752312d8f7_84-wellesley-st--weston--ma-02493_.html"'

2. Use reportid 1234567 to create a new chat 
curl --location 'http://localhost:8000/internal/chat/1234567/new_message/' \
--header 'Referer: http://localhost:8000' \
--header 'Content-Type: application/json' \
--header 'Cookie: csrftoken=iJjx2zDcXO5Tf5lhaKOyZSiwKwvvkUQP; sessionid=itnl9jvgm54gglju47im28cquzn4e6ly' \
--data '{
    "chat": 1234567,
    "message_type": "HUMAN", 
    "content": "What's the homes age?",
    "issue": "checker"
}'

3. Get async LLM response
curl --location 'http://localhost:8000/internal/chat/1234567/get_response/179a120c-1350-4fdc-99f8-7c2d7655ce6a/' \
--header 'Referer: http://localhost:8000' \
--header 'Cookie: csrftoken=iJjx2zDcXO5Tf5lhaKOyZSiwKwvvkUQP; sessionid=itnl9jvgm54gglju47im28cquzn4e6ly' \
--data ''