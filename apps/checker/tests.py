import os
import sys
import json
import time
import django
import requests
import logging
from pathlib import Path
from typing import Dict, List
from django.conf import settings
from django.test import TestCase, override_settings
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from rest_framework.permissions import AllowAny

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
logger = logging.getLogger(__name__)



# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'homescore.settings')
django.setup()

# Now we can import our models
from apps.checker.models import InspectionUploadStatus
from apps.checker.views import InspectionReportView


class InspectionReportTest(TestCase):
    def setUp(self):
        # Disable token authentication for tests
        InspectionReportView.authentication_classes = []
        InspectionReportView.permission_classes = [AllowAny]
        # Log the token
        self.start_checker_id = 1
        self.test_files_dir = Path(__file__).parent / "test_files"
        self.upload_url = reverse("internal:checker-report")  # Matches the URL pattern

    def test_inspection_uploads(self):
        test_files = list(self.test_files_dir.glob("*.pdf"))
        if not test_files:
            self.skipTest(f"No test files found in {self.test_files_dir}")
        results = {"successes": [], "failures": []}

        for idx, file_path in enumerate(test_files):
            checker_id = self.start_checker_id + idx
            with open(file_path, "rb") as f:
                upload_file = SimpleUploadedFile(name=file_path.name, content=f.read(), content_type="application/pdf")
            # Send POST request with file and checker_id
            response = self.client.post(
                self.upload_url,
                data={"file": upload_file, "checker_id": checker_id},
                HTTP_AUTHORIZATION=f"Bearer {self.token}",
                
            )
            
            # Log the view handling the request
            logger.info(f"Uploaded {file_path.name} with checker_id {checker_id} - status code: {response.status_code}")
            # If upload fails, log failure and continue to next file
            if response.status_code != 200:
                results["failures"].append({
                    "file_name": file_path.name,
                    "checker_id": checker_id,
                    "status_code": response.status_code,
                    "error": response.content.decode()
                })
                continue

            # Allow processing time (if asynchronous, consider using mocks or waiting)
            success = self.monitor_processing(checker_id)
            result = {
                "file_name": file_path.name,
                "checker_id": checker_id,
                "status_code": response.status_code,
                "processing_success": success
            }
            if success:
                results["successes"].append(result)
            else:
                results["failures"].append(result)

            # Wait between uploads
            time.sleep(1)
        # Simple assertion - you could assert based on expected numbers
        self.assertGreater(len(results["successes"]), 0, "No uploads processed successfully")
        # Optionally, print the summary for debugging
        print(json.dumps(results, indent=2))

    def monitor_processing(self, checker_id: int, max_retries: int = 10, wait_seconds: int = 2) -> bool:
        for _ in range(max_retries):
            status_obj = InspectionUploadStatus.objects.filter(checker_id=checker_id).first()
            if status_obj:
                if status_obj.upload_status == "Completed":
                    return True
                elif status_obj.upload_status == "Failed":
                    return False
            time.sleep(wait_seconds)
        return False
