from django.db import models
from enum import Enum
from enumfields import EnumField
from apps.utils.models import BaseModel, BaseImage, BaseImageFeature, BaseRoomSummary, BaseHomeQuality, BaseStatusModel
from apps.reports.models import RoomType, FeatureType
from django.utils.functional import cached_property
import time
from django.utils import timezone

__all__ = ['RiskCategory', 'RiskType', 'Severity', 'PropertyRisk', 'PermitAnalysis', 'PropertyQuestion', 'PropertyOpportunity', 'PropertyIssues']


# Create your models here.
# Risk-related enums
class RiskCategory(str, Enum):
    ASBESTOS = 'asbestos'
    LEAD = 'lead'
    WIRING = 'wiring'
    PLUMBING = 'plumbing'
    SEWER = 'sewer'
    WINDOWS = 'windows'
    FOUNDATION = 'foundation'
    ROOF = 'roof'
    HVAC = 'hvac'
    ELECTRICAL = 'electrical'
    WATER_HEATER = 'water_heater'
    APPLIANCES = 'appliances'
    SAFETY = 'safety'
    OTHER = 'other'

    @classmethod
    def choices(cls):
        return [(item.value, item.name.title().replace('_', ' ')) for item in cls]

class RiskType(str, Enum):
    STRUCTURAL = 'structural'
    SYSTEM = 'system'
    SAFETY = 'safety'
    MAINTENANCE = 'maintenance'
    FLOODING = 'flooding'

    @classmethod
    def choices(cls):
        return [(item.value, item.name.title()) for item in cls]

class Severity(str, Enum):
    LOW = 'low'
    MEDIUM = 'medium'
    HIGH = 'high'

    @classmethod
    def choices(cls):
        return [(item.value, item.name.title()) for item in cls]

# Image Models
class CheckerImage(BaseImage):
    """Model for storing property images from checker."""
    checker_id = models.CharField(max_length=255, default="")
    room_type = EnumField(RoomType, max_length=50, default=RoomType.LIVING_ROOM)

    class Meta:
        unique_together = ['checker_id', 'image_url']
        indexes = [
            models.Index(fields=['checker_id', 'room_type']),
        ]

# Image Feature Models
class CheckerImageFeature(BaseImageFeature):
    """Model for storing features of checker images."""
    image = models.ForeignKey(CheckerImage, on_delete=models.CASCADE)
    feature_type = EnumField(FeatureType, max_length=50, default=FeatureType.SIZE)

    class Meta:
        unique_together = ['image', 'feature_type', 'choice']
        indexes = [
            models.Index(fields=['image', 'feature_type']),
        ]

# Room Summary Models
class CheckerRoomSummary(BaseRoomSummary):
    """Model for storing room summary of checker images."""
    checker_id = models.CharField(max_length=255, default="")
    feature_type = EnumField(FeatureType, max_length=50, default=FeatureType.SIZE)

    class Meta:
        unique_together = ['checker_id', 'room_type', 'feature_type']
        indexes = [
            models.Index(fields=['checker_id', 'room_type']),
        ]

class CheckerHomeQuality(BaseHomeQuality):
    """Model for storing home quality analysis of checker images."""
    checker_id = models.CharField(max_length=255, default="")

    class Meta:
        unique_together = ['checker_id', 'condition']
        indexes = [
            models.Index(fields=['checker_id']),
        ]

class PropertyRisk(models.Model):
    """Centralized model for storing all types of risks"""
    SECTION_CHOICES = [
        ('age', 'Age Section'),
        ('permit', 'Permit Section'),
        ('structural', 'Structural Section'),
    ]

    checker_id = models.CharField(max_length=255)
    section = models.CharField(max_length=20, choices=SECTION_CHOICES, default='age')
    category = models.CharField(
        max_length=20,
        choices=RiskCategory.choices(),
        default=RiskCategory.PLUMBING
    )
    risk_type = models.CharField(
        max_length=20,
        choices=RiskType.choices(),
        default=RiskType.MAINTENANCE
    )
    severity = models.CharField(
        max_length=10,
        choices=Severity.choices(),
        default=Severity.MEDIUM
    )
    description = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['checker_id', 'section', 'category']

class PermitAnalysis(models.Model):
    """Model for storing permit information only"""
    checker_id = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)

    # Permit Details
    permit_date = models.DateField(null=True)
    permit_type = models.CharField(max_length=100)
    description = models.TextField()
    value = models.CharField(max_length=50, null=True)
    importance = models.TextField()

    class Meta:
        unique_together = ['checker_id', 'permit_date', 'permit_type']

class PropertyQuestion(models.Model):
    checker_id = models.CharField(max_length=100)
    category = models.CharField(max_length=50)  # e.g., 'maintenance', 'safety', 'structural'
    question = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('checker_id', 'question')


class PropertyOpportunity(models.Model):
    """Model for storing improvement opportunities"""
    checker_id = models.CharField(max_length=255)
    category = models.CharField(max_length=50)  # Easy Repairs, Renovation Potential, Energy Efficiency Updates
    title = models.CharField(max_length=200)
    description = models.TextField()
    estimated_cost = models.CharField(max_length=100)
    potential_value_add = models.CharField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['checker_id', 'category', 'title']

class PropertyIssues(models.Model):
    """Model for storing issues found in the property"""
    checker_id = models.CharField(max_length=255)
    issue = models.CharField(max_length=256, default="")
    category = models.CharField(max_length=64, default="")
    source = models.CharField(max_length=64, default="")
    urgency = models.CharField(max_length=64, default="Low")
    context = models.TextField(max_length=1024, default="")
    recommendation = models.TextField(max_length=1024, default="")
    cost_estimate_low = models.IntegerField(default=0)
    cost_estimate_high = models.IntegerField(default=0)

    class Meta:
        unique_together = ("checker_id", "issue", "category", "source")

class CheckerIDModelMixin(models.Model):
    """Mixin to add checker_id to models."""
    checker_id = models.CharField(max_length=255, unique=True)

    class Meta:
        abstract = True

class ReportUploadStatus(BaseStatusModel, CheckerIDModelMixin):
    """Tracks the status of report uploads and processing."""
    html_url = models.URLField(max_length=500, null=True, blank=True)
    property_analysis_complete = models.BooleanField(default=False)
    context_analysis_complete = models.BooleanField(default=False)
    room_summary_complete = models.BooleanField(default=False)

    class Meta:
        verbose_name = "Report Upload Status"
        verbose_name_plural = "Report Upload Statuses"

    def __str__(self):
        return f"Report for {self.checker_id}: {self.upload_status}"

class InspectionUploadStatus(BaseStatusModel, CheckerIDModelMixin):
    """Model for tracking inspection report upload and processing status."""

    def __str__(self):
        return f"{self.checker_id} - {self.upload_status}"

class ImageAnalysisStatus(BaseStatusModel, CheckerIDModelMixin):
    """Tracks the status of image analysis."""

    class Meta:
        verbose_name = "Image Analysis Status"
        verbose_name_plural = "Image Analysis Statuses"

    def __str__(self):
        return f"Image Analysis for {self.checker_id}: {self.upload_status}"

class RoomSummaryStatus(BaseStatusModel, CheckerIDModelMixin):
    """Tracks the status of room summary generation."""

    class Meta:
        verbose_name = "Room Summary Status"
        verbose_name_plural = "Room Summary Statuses"

    def __str__(self):
        return f"Room Summary for {self.checker_id}: {self.upload_status}"
