from datetime import datetime
import hashlib
import logging
import os
import json

from allauth.account.signals import email_confirmed, user_signed_up
from django.conf import settings
from django.core.files.storage import default_storage
from django.core.mail import mail_admins
from django.db.models.signals import pre_save
from django.dispatch import receiver

from apps.reports import utils
from apps.reports.models import Report
from apps.reports.tasks import summary, get_home_details, create_chat, get_preferences
from apps.users.mailing_list import subscribe_to_mailing_list
from apps.users.models import CustomUser, Section, Question, SectionResponse, QuestionResponse

logger = logging.Logger(__file__, logging.INFO)
storage: utils.Supabase | None = utils.storage
UUID = "e2a5d79cce384b3d816c46341a12f260"


@receiver(user_signed_up)
def handle_sign_up(request, user, **kwargs):
    # customize this function to do custom logic on sign up, e.g. send a welcome email
    # or subscribe them to your mailing list.
    # This example notifies the admins, in case you want to keep track of sign ups
    _notify_admins_of_signup(user)
    # and subscribes them to a mailchimp mailing list
    subscribe_to_mailing_list(user.email)
    _create_sample_report(user)
    


@receiver(email_confirmed)
def update_user_email(sender, request, email_address, **kwargs):
    """
    When an email address is confirmed make it the primary email.
    """
    # This also sets user.email to the new email address.
    # hat tip: https://stackoverflow.com/a/29661871/8207
    email_address.set_as_primary()


def _notify_admins_of_signup(user):
    mail_admins(
        f"Yowsers, someone signed up for {settings.PROJECT_METADATA['NAME']}!",
        "Email: {}".format(user.email),
        fail_silently=True,
    )


def _create_sample_report(user):
    time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    filename = hashlib.md5(f"{UUID}-{user.email}-{time}".encode("utf-8")).hexdigest()
    logger.info("Creating %s", filename)
    logger.info("Working directory", os.getcwd())

    # Bootstrap sample report
    logger.info("Creating sample report")
    dir_path = os.path.dirname(os.path.realpath(__file__))
    with open(os.path.join(dir_path, "SampleReport.pdf"), "rb") as fin:
        storage.put_file(settings.REPORT_BUCKET, filename, fin)

    report = Report.objects.create(
        user=user,
        name="Sample Report",
        file_name=filename,
        summary_status="Processing...",
        chat_status="Not ready",
        address=settings.DEFAULT_ADDRESS,
    )
    report.save()
    get_preferences.delay(filename, user.id)
    create_chat.delay(filename, user.id)
    summary.delay(filename, user.id)
    

@receiver(pre_save, sender=CustomUser)
def remove_old_profile_picture_on_change(sender, instance, **kwargs):
    if not instance.pk:
        return False

    try:
        old_file = sender.objects.get(pk=instance.pk).avatar
    except sender.DoesNotExist:
        return False

    if old_file and old_file.name != instance.avatar.name:
        if default_storage.exists(old_file.name):
            default_storage.delete(old_file.name)
