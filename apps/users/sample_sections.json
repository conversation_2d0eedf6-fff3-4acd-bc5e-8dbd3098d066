{"sections": [{"title": "Household Composition", "description": "Understand the composition of your household", "image_file": "image/household.png", "progress": 0.0, "cues": ["What’s the reason you’re looking to buy now?", "Who will be living in the home with you?", "What type of property are you considering?", "Do you envision this to be your forever home?"], "questions": [{"id": "primary_purpose", "prompt": "Who is the primary user of the property?", "purpose": "Prospective buyers looking to accommodate kids at any point would likely prioritize amenities like a backyard and likely want to make sure there are sufficient bedrooms. Users with an elderly person likely will prioritize amenities focused on accessibility", "options": ["Me", "Partner/Spouse", "Elderly parents", "Kids", "Future kids"], "min_max": [1, 1], "satisfied": "False", "response": "None", "updated": "False", "input_type": "RADIO"}, {"id": "children", "prompt": "How many children do you have?", "purpose": "Understand the number of children in the household", "options": ["0", "1", "2", "3", "4", "5+"], "min_max": [1, 1], "satisfied": "False", "response": "None", "updated": "False", "input_type": "RADIO"}, {"id": "work_from_home", "prompt": "Do you ever work from home?", "purpose": "Determining home office needs based on work-from-home frequency.", "options": ["Every day", "Sometimes", "Rarely"], "min_max": [1, 1], "satisfied": "False", "response": "None", "updated": "False", "input_type": "RADIO"}, {"id": "home_type", "prompt": "What type of home are you interested in?", "purpose": "Matching home types with buyer's lifestyle and preferences.", "options": ["Townhouse", "<PERSON><PERSON>", "Single Family Home", "Multi-family Home"], "min_max": [1, 4], "satisfied": "False", "response": "None", "updated": "False", "input_type": "CHECKBOX"}, {"id": "pets", "prompt": "What furry friends will live in the home?", "purpose": "Accommodating pets in the housing selection.", "options": ["No pets", "Dogs", "Cats", "Not now but maybe later"], "min_max": [1, 4], "satisfied": "False", "response": "None", "updated": "False", "input_type": "CHECKBOX"}, {"id": "move_timeline", "prompt": "What is your preferred timeline for moving?", "purpose": "Scheduling viewings and recommendations based on urgency.", "options": ["Immediately", "Within 3 months", "3-6 months", "6-12 months", "More than a year"], "min_max": [1, 1], "satisfied": "False", "response": "None", "updated": "False", "input_type": "RADIO"}], "section_response": "Not filled"}, {"title": "Location & Convenience", "description": "Explore the buyer's preferences for location and convenience to find the ideal property.", "image_file": "image/location.png", "progress": 0.0, "cues": ["What do you want nearby?", "Are good schools important?", "Describe your ideal type of neighborhood?", "What factors are important to you in a location?", "Do you prefer walking or driving to amenities?"], "questions": [{"id": "location_factors", "prompt": "What factors are important to you in a location?", "purpose": "Gathering detailed preferences on location to guide property recommendations.", "options": [], "min_max": [1, 1], "satisfied": "False", "response": "None", "updated": "False", "input_type": "TEXT"}, {"id": "environment_type", "prompt": "What kind of environment do you see yourself in?", "purpose": "Identifying preferred living environments.", "options": ["Bustling city center", "City outskirts", "Suburban", "Rural area with lots of nature"], "min_max": [1, 1], "satisfied": "False", "response": "None", "updated": "False", "input_type": "RADIO"}, {"id": "favorite_towns", "prompt": "Name a few towns you really love and why", "purpose": "Analyzing location appeal based on personal preferences.", "options": [], "min_max": [1, 1], "satisfied": "False", "response": "None", "updated": "False", "input_type": "TEXT"}, {"id": "noise_tolerance", "prompt": "How much noise are you comfortable with in your neighborhood?", "purpose": "Matching location to noise level preferences.", "options": ["Very quiet", "Moderately quiet", "Some noise is okay"], "min_max": [1, 1], "satisfied": "False", "response": "None", "updated": "False", "input_type": "RADIO"}, {"id": "school_importance", "prompt": "How important are good schools?", "purpose": "Assessing the priority of school quality for families.", "options": [], "min_max": [1, 10], "satisfied": "False", "response": "None", "updated": "False", "input_type": "SLIDER"}, {"id": "home_features_ranking", "prompt": "How would you rank these features in your new home?", "purpose": "Prioritizing home features based on buyer preferences.", "options": ["Safe area", "Quiet neighborhood", "Green space", "Transit accessibility", "Things to do nearby"], "min_max": [1, 5], "satisfied": "False", "response": "None", "updated": "False", "input_type": "RANK"}, {"id": "proximity_preferences", "prompt": "In an ideal world, how close would you like to be to various amenities?", "purpose": "Determining preferred proximity to key amenities.", "options": ["Grocery store", "Workplace", "Kids' school", "Restaurants/Nightlife/Cafes", "Public transportation", "Greenspaces/Outdoor activities", "Urgent Care/Hospitals"], "min_max": [1, 7], "satisfied": "False", "response": "None", "updated": "False", "input_type": "RANK"}], "section_response": "Not filled"}, {"title": "Home Details", "description": "Explore the buyer's preferences for specific home features and how they imagine their ideal living space.", "image_file": "image/details.png", "progress": 0.0, "cues": ["Do you want a bathroom double vanity?", "Are hardwood floors a must?", "Need a high quality chef’s kitchen?", "What type of outdoor space do you like?"], "questions": [{"id": "home_priorities", "prompt": "What features and attributes do you prioritize in a home?", "purpose": "Identifying essential home features prioritized by the buyer.", "options": [], "min_max": [1, 1], "satisfied": "False", "response": "None", "updated": "False", "input_type": "TEXT"}, {"id": "exterior_non_negotiable", "prompt": "What exterior features are non-negotiable for you?", "purpose": "Identifying critical exterior features for the buyer.", "options": [], "min_max": [1, 1], "satisfied": "False", "response": "None", "updated": "False", "input_type": "TEXT"}, {"id": "exterior_features_rank", "prompt": "Rank your top 7 exterior features.", "purpose": "Prioritizing exterior features according to buyer preferences.", "options": ["Outdoor space/yard", "Patio/Deck", "Property Siding", "Swimming Pool", "Roof Condition", "Neighbor Privacy", "Garage/Parking"], "min_max": [1, 7], "satisfied": "False", "response": "None", "updated": "False", "input_type": "RANK"}, {"id": "interior_non_negotiable", "prompt": "What interior features are non-negotiable for you?", "purpose": "Identifying critical interior features for the buyer.", "options": [], "min_max": [1, 1], "satisfied": "False", "response": "None", "updated": "False", "input_type": "TEXT"}, {"id": "interior_features_importance", "prompt": "How important are the following interior features in your home?", "purpose": "Evaluating the importance of various interior features.", "options": ["Hardwood floors", "Open Concept", "Modern Kitchen", "Central Air Conditioning", "Closet Space", "Finished Basement/Attic", "Fireplace"], "min_max": [1, 7], "satisfied": "False", "response": "None", "updated": "False", "input_type": "RANK"}], "section_response": "Not filled"}, {"title": "Open to Doing Work", "description": "Assessing the buyer's willingness to undertake renovations or repairs to match their ideal property specifications.", "image_file": "image/work.png", "progress": 0.0, "cues": ["What if the kitchen is dated?", "What rooms can be less than perfect", "Are you okay if mold is found?", "Is it fine if a roof needs replacement?", "Do you want the property move-in ready now?"], "questions": [{"id": "home_condition_preference", "prompt": "Do you prefer a ready home or one needing work to get ideal features? What fixes are you okay with?", "purpose": "Determining the buyer's trade-off between property condition and ideal features.", "options": [], "min_max": [1, 1], "satisfied": "False", "response": "None", "updated": "False", "input_type": "TEXT"}, {"id": "move_in_condition", "prompt": "When I get the keys to my new home, I will be ok if it's:", "purpose": "Identifying acceptable levels of required initial repairs or renovations.", "options": ["Move-in ready", "Only Needs Minor Repairs", "Key Systems Need Repair", "In Need of Major Renovations"], "min_max": [1, 4], "satisfied": "False", "response": "None", "updated": "False", "input_type": "CHECKBOX"}, {"id": "trade_offs", "prompt": "What are a few things you’d sacrifice about your future home so you could get more of what you want?", "purpose": "Understanding priorities and trade-offs in property features.", "options": [], "min_max": [1, 1], "satisfied": "False", "response": "None", "updated": "False", "input_type": "TEXT"}, {"id": "compromises", "prompt": "I'd be ok if ____ in my home wasn't exactly what I wanted:", "purpose": "Ranking the acceptability of less-than-ideal features.", "options": ["Home square footage", "Kitchen", "<PERSON><PERSON>", "Flooring", "Dining room", "Bedrooms", "Bathrooms", "Outdoor space", "Heating/AC", "Appliances", "Neighborhood"], "min_max": [1, 11], "satisfied": "False", "response": "None", "updated": "False", "input_type": "RANK"}, {"id": "home_improvements", "prompt": "What changes do you plan to make to your home over time?", "purpose": "Identifying long-term home improvement goals.", "options": ["Improve living quality", "Increase value over time", "Make the home more energy-efficient", "Modernize outdated features", "Accommodate changing family needs", "Enhance aesthetic appeal", "Reduce maintenance costs"], "min_max": [1, 7], "satisfied": "False", "response": "None", "updated": "False", "input_type": "CHECKBOX"}, {"id": "energy_efficiency_importance", "prompt": "Energy efficiency is...", "purpose": "Assessing the importance of energy efficiency in their new home.", "options": ["Not important", "Helps save money", "Reduces carbon footprint"], "min_max": [1, 1], "satisfied": "False", "response": "None", "updated": "False", "input_type": "RADIO"}], "section_response": "Not filled"}]}