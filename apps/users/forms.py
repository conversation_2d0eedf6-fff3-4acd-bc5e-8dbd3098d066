from allauth.account.forms import SignupForm
from django import forms
from django.contrib.auth.forms import UserChangeForm
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.utils.translation import gettext

from .helpers import validate_profile_picture
from .models import CustomUser


class CustomUserChangeForm(UserChangeForm):
    email = forms.EmailField(label=gettext("Email"), required=True)

    class Meta:
        model = CustomUser
        fields = ("email", "first_name", "last_name")


class UploadAvatarForm(forms.Form):
    avatar = forms.FileField(validators=[validate_profile_picture])


class TermsSignupForm(SignupForm):
    """Custom signup form to add a checkbox for accepting the terms."""

    terms_agreement = forms.BooleanField(initial=True, required=True, widget=forms.CheckboxInput)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        link = '<a href="{}" target="_blank">{}</a>'.format(
            reverse("web:terms"),
            gettext("Terms and Conditions"),
        )
        self.fields["terms_agreement"].label = mark_safe(gettext("I agree to the {terms_link}").format(terms_link=link))
