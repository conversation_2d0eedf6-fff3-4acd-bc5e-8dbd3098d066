import logging
import os
import random
import string
from allauth.account.utils import send_email_confirmation
from allauth_2fa.utils import user_has_valid_totp_device
from django.contrib import messages
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib.sessions.models import Session
from django.http import HttpResponse, JsonResponse
from django.middleware.csrf import get_token
from django.shortcuts import get_object_or_404, render
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_POST
from apps.checker.authentication import BearerTokenAuthentication
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.authentication import BasicAuthentication, SessionAuthentication
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from apps.users.tasks import in_take_quiz, send_preference_email

from .forms import CustomUserChangeForm, UploadAvatarForm
from .helpers import require_email_confirmation, user_has_confirmed_email_address
from .models import CustomUser, UserPreferences, Section, Question, SectionResponse, QuestionResponse
from .serializers import UserPreferencesSerializer, SectionSerializer, QuestionSerializer, SectionResponseSerializer, QuestionResponseSerializer
from . import tasks
import json

logger = logging.getLogger(__file__)


@api_view(["GET"])
@permission_classes([AllowAny])
def get_csrf(request):
    csrf_token = get_token(request)
    return Response(data={"detail": "CSRF cookie set", "X-CSRFToken": csrf_token}, headers={"X-CSRFToken": csrf_token})


class LoginAPIView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        logger.info("Data: %s", request.data)
        username = request.data.get("username", None)
        password = request.data.get("password", None)
        user = authenticate(request, username=username, password=password)
        logger.info(f"User: {user}")
        if user is not None:
            login(request, user)
            return JsonResponse({"detail": "Login successful"}, status=status.HTTP_200_OK)
        else:
            return JsonResponse({"detail": "Invalid credentials"}, status=status.HTTP_400_BAD_REQUEST)


class LogoutView(APIView):  # logout_view(request):
    authentication_classes = [SessionAuthentication, BasicAuthentication]
    permission_classes = [IsAuthenticated]

    @staticmethod
    def get(request, format=None):
        logout(request)
        return JsonResponse({"detail": "Successfully logged out."})


class SessionView(APIView):
    authentication_classes = [SessionAuthentication, BasicAuthentication]
    permission_classes = [IsAuthenticated]

    @staticmethod
    def get(request, format=None):
        return JsonResponse({"isAuthenticated": True})


class WhoAmIView(APIView):
    authentication_classes = [SessionAuthentication, BasicAuthentication, BearerTokenAuthentication]
    permission_classes = [IsAuthenticated]

    @staticmethod
    def get(request, format=None):
        id = request.GET.get("id")
        print("params:", request.GET)
        if id:
            current_user = get_object_or_404(CustomUser, id=id)
        else:
            current_user = get_object_or_404(CustomUser, username=request.user.username)
        return JsonResponse(
            {
                "id": current_user.id,
                "username": current_user.username,
                "first_name": current_user.first_name,
                "last_name": current_user.last_name,
                "is_superuser": current_user.is_superuser,
                "is_testuser": current_user.is_testuser,
                "email": current_user.email,
            }
        )


@login_required
def profile(request):
    if request.method == "POST":
        form = CustomUserChangeForm(request.POST, instance=request.user)
        if form.is_valid():
            user = form.save(commit=False)
            user_before_update = CustomUser.objects.get(pk=user.pk)
            need_to_confirm_email = (
                user_before_update.email != user.email
                and require_email_confirmation()
                and not user_has_confirmed_email_address(user, user.email)
            )
            if need_to_confirm_email:
                # don't change it but instead send a confirmation email
                # email will be changed by signal when confirmed
                new_email = user.email
                send_email_confirmation(request, user, signup=False, email=new_email)
                user.email = user_before_update.email
                # recreate the form to avoid populating the previous email in the returned page
                form = CustomUserChangeForm(instance=user)
            user.save()
            messages.success(request, _("Profile successfully saved."))
    else:
        form = CustomUserChangeForm(instance=request.user)
    return render(
        request,
        "account/profile.html",
        {
            "form": form,
            "active_tab": "profile",
            "page_title": _("Profile"),
            "user_has_valid_totp_device": user_has_valid_totp_device(request.user),
        },
    )


@login_required
@require_POST
def upload_profile_image(request):
    user = request.user
    form = UploadAvatarForm(request.POST, request.FILES)
    if form.is_valid():
        user.avatar = request.FILES["avatar"]
        user.save()
        return HttpResponse(_("Success!"))
    else:
        readable_errors = ", ".join(str(error) for key, errors in form.errors.items() for error in errors)
        return JsonResponse(status=403, data={"errors": readable_errors})


class UserPreferencesAPIView(APIView):
    authentication_classes = [SessionAuthentication, BasicAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        queryset = UserPreferences.objects.filter(user=user)

        if not queryset.exists():
            return Response([], status=status.HTTP_200_OK)

        serializer = UserPreferencesSerializer(queryset, many=True)
        return Response(serializer.data)

    # ...

    @swagger_auto_schema(request_body=UserPreferencesSerializer)
    def post(self, request):
        user = request.user
        preference_data = request.data  # Ensure request.data is of type UserPreference
        print(preference_data)
        if "user" not in preference_data:
            preference_data["user"] = user.id
        preference_queryset = UserPreferences.objects.filter(
            user=user, preference=preference_data.get("preference", "")
        )

        if preference_queryset.exists():
            preference_instance = preference_queryset.first()
            serializer = UserPreferencesSerializer(instance=preference_instance, data=preference_data)
        else:
            serializer = UserPreferencesSerializer(data=preference_data)

        if serializer.is_valid():
            serializer.save(user=user)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)



def get_or_create_anonymous_user(session):
    if not session.session_key:
        session.create()
    else:
        try:
            s = Session.objects.get(session_key=session.session_key)
            if s.expire_date < timezone.now():
                session.flush()  # Clear session if expired
                session.create()
        except Session.DoesNotExist:
            session.create()

    session_key = session.session_key
    username = f"anon_{session_key}"
    email = f"{username}@example.com"

    user, created = CustomUser.objects.get_or_create(
        username=username,
        defaults={"email": email, "first_name": "Anonymous"}
    )

    return user, username

class GetSections(APIView):
    permission_classes = [AllowAny]
    
    def get(self, request):
        sections = Section.objects.all().order_by('id')
        serializer = SectionSerializer(sections, many=True)
        return Response(serializer.data)

class SectionResponseAPIView(APIView):
    permission_classes = [AllowAny]
    
    def get(self, request):
        if request.user.is_authenticated:
            user = request.user
        else:
            user, _ = get_or_create_anonymous_user(request.session)

        queryset = SectionResponse.objects.filter(user=user)
        
        if not queryset.exists():
            return Response([], status=status.HTTP_200_OK)
        serializer = SectionResponseSerializer(queryset, many=True)
        
        return Response(serializer.data)
    
    def post(self, request):
        data = request.data
        section_id = data.get("section_id")
        response = data.get("response")
        
        if section_id is None:
            return Response({"error": "Section ID is required"}, status=status.HTTP_400_BAD_REQUEST)
        
        section = get_object_or_404(Section, id=section_id)
        
        if request.user.is_authenticated:
            user = request.user
        else:
            user, _ = get_or_create_anonymous_user(request.session)

        section_response, created = SectionResponse.objects.update_or_create(
            user=user, section=section,
            defaults={"response": response}
        )
        
        serializer = SectionResponseSerializer(section_response)
        
        return Response(serializer.data, status=status.HTTP_201_CREATED)

class QuestionResponseAPIView(APIView):
    permission_classes = [AllowAny]
    
    def get(self, request):
        if request.user.is_authenticated:
            user = request.user
        else:
            user, _ = get_or_create_anonymous_user(request.session)

        queryset = QuestionResponse.objects.filter(user=user)
        
        if not queryset.exists():
            return Response([], status=status.HTTP_200_OK)
        serializer = QuestionResponseSerializer(queryset, many=True)
        
        return Response(serializer.data)
    
    def post(self, request):
        data = request.data
        question_id = data.get("question_id")
        section_response_id = data.get("section_response_id")
        response = data.get("response")
        
        if question_id is None:
            return Response({"error": "Question ID is required"}, status=status.HTTP_400_BAD_REQUEST)
        
        question = get_object_or_404(Question, id=question_id)
        section_response = get_object_or_404(SectionResponse, id=section_response_id)
        
        if request.user.is_authenticated:
            user = request.user
        else:
            user, _ = get_or_create_anonymous_user(request.session)

        question_response, created = QuestionResponse.objects.update_or_create(
            user=user, question=question, section_response=section_response,
            defaults={"response": response}
        )
        
        serializer = QuestionResponseSerializer(question_response)
        
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    
    

class DeleteSectionResponse(APIView):
    permission_classes = [AllowAny]
    
    def post(self, request):
        if request.user.is_authenticated:
            user = request.user
        else:
            user, _ = get_or_create_anonymous_user(request.session)
        
        if user is None:
            return Response({"error": "User not found"}, status=status.HTTP_400_BAD_REQUEST)
        
        SectionResponse.objects.filter(user=user).delete()
        
        return Response({"success": "Section response deleted"}, status=status.HTTP_200_OK)

class DeleteQuestionResponse(APIView):
    permission_classes = [AllowAny]
    
    def post(self, request):
        if request.user.is_authenticated:
            user = request.user
        else:
            user, _ = get_or_create_anonymous_user(request.session)
        
        if user is None:
            return Response({"error": "User not found"}, status=status.HTTP_400_BAD_REQUEST)
        
        QuestionResponse.objects.filter(user=user).delete()
        
        return Response({"success": "Question response deleted"}, status=status.HTTP_200_OK)

class ProcessSection(APIView):
    permission_classes = [AllowAny]
    
    def post(self, request):
        try:
            data = request.data
            section_index = data.get("section_index")
            section_response = data.get("section_response")
            
            if section_index is None or section_index < 0 or section_index >= Section.objects.count():
                return Response({"error": "Invalid section index"}, status=status.HTTP_400_BAD_REQUEST)
            
            if request.user.is_authenticated:
                user = request.user
                username = user.username
            else:
                user, username = get_or_create_anonymous_user(request.session)
            
            if user is None:
                return Response({"error": "User not found"}, status=status.HTTP_400_BAD_REQUEST)
            
            output = in_take_quiz(section_index=section_index, section_response=section_response, user=user, username=username)
            
            return Response(output, status=status.HTTP_200_OK)
        
        except json.JSONDecodeError:
            return Response({"error": "Invalid JSON input"}, status=status.HTTP_400_BAD_REQUEST)
        
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class QuizSummary(APIView):
    permission_classes = [AllowAny]
    
    def post(self, request):
        try:
            # data = request.data
            # sections = data.get("sections")
            if request.user.is_authenticated:
                user = request.user
            else:
                user, _ = get_or_create_anonymous_user(request.session)
                
            
            output = tasks.process_quiz_summary(user=user)
            
            return Response(output, status=status.HTTP_200_OK)
        except Exception as e:
            print("Error response:", e)
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
class ShareSummary(APIView):
    permission_classes = [AllowAny]
    
    def post(self, request):
        email = []
        try:
            data = request.data
            user_email = data.get("user_email")
            realtor_email = data.get("realtor_email")
            
            
            # Validate user_email
            if user_email is None:
                return Response({"error": "Primary Email is required"}, status=status.HTTP_400_BAD_REQUEST)

            email.append(user_email)
            
            # Add realtor_email to the email list if provided
            if realtor_email:
                email.append(realtor_email)
            
            if request.user.is_authenticated:
                # If the user is authenticated, update or create the user object
                user = request.user
                user_object, created = CustomUser.objects.get_or_create(email=user_email)
                user_object.realtor_email = realtor_email
                user_object.save()
            else:
                # Check if a user with the provided email already exists
                existing_user = CustomUser.objects.filter(email=user_email).first()
                if existing_user:
                    # Transfer section and question responses from anonymous user to existing user
                    anonymous_user, _ = get_or_create_anonymous_user(request.session)
                    if anonymous_user is None:
                        return Response({"error": "Anonymous user not found"}, status=status.HTTP_400_BAD_REQUEST)
                    
                    # Assuming you have models SectionResponse and QuestionResponse
                    SectionResponse.objects.filter(user=anonymous_user).update(user=existing_user)
                    QuestionResponse.objects.filter(user=anonymous_user).update(user=existing_user)
                    
                    # Delete the anonymous user
                    anonymous_user.delete()
                    
                    user = existing_user
                else:
                    # Create or get the anonymous user
                    user, _ = get_or_create_anonymous_user(request.session)
                    if user is None:
                        return Response({"error": "User not found"}, status=status.HTTP_400_BAD_REQUEST)
                    
                    # Update the anonymous user with the provided email and realtor email
                    user.email = user_email
                    user.realtor_email = realtor_email
                    user.save()
                    
            quiz_summary = tasks.process_quiz_summary(user=user)

            # Send the email to the realtor with the PDF attachment
            send_preference_email(email=email, summary=quiz_summary)
            
            
            response_data = {"email": email}
            return Response(response_data, status=status.HTTP_200_OK)
        
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)