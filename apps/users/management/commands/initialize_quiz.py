import json
import logging
from django.core.management.base import BaseCommand
from apps.users.models import Section, Question

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

class Command(BaseCommand):
    help = "Initialize or update sections and questions from a JSON file"

    def handle(self, *args, **kwargs):
        try:
            # Get the data from the JSON file
            with open("apps/users/sample_sections.json", "r") as f:
                data = json.load(f)
            
            data = data["sections"]
            
            print("Creating sections and questions")
            for section_data in data:
                # Assuming Section model has a method "create_or_update" to handle both creation and updates
                section, _ = Section.objects.update_or_create(
                    title=section_data["title"],
                    defaults={
                        "description": section_data["description"],
                        "image_file": section_data.get("image_file", None),  # Assuming image_file is optional
                        "progress": section_data.get("progress", 0),  # Default progress to 0 if not provided
                        "cues": section_data.get("cues", [])
                    }
                )
                section.save()
                for question_data in section_data["questions"]:
                    # Assuming Question model has a method "create_or_update" to handle both creation and updates
                    question, _ = Question.objects.update_or_create(
                        name=question_data["id"],
                        defaults={
                            "section": section,
                            "prompt": question_data["prompt"],
                            "purpose": question_data.get("purpose", ""),
                            "options": question_data.get("options", []),
                            "satisfied": question_data.get("satisfied", False),
                            "response": question_data.get("response", ""),
                            "min_max": question_data.get("min_max", []),
                            "input_type": question_data["input_type"],
                            "updated": question_data.get("updated", False)
                        }
                    )
            self.stdout.write(self.style.SUCCESS("Successfully initialized or updated sections and questions"))
        except Exception as e:
            logger.error("Error initializing sections and questions: %s", str(e))
            logger.debug(traceback.format_exc())
            self.stdout.write(self.style.ERROR("Error initializing sections and questions"))