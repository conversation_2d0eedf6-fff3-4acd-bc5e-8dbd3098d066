from django.core.management.base import BaseCommand, CommandError

from apps.users.models import CustomUser


class Command(BaseCommand):
    help = "Sets to the given user testuser flag to True."

    def add_arguments(self, parser):
        parser.add_argument("username", type=str)

    def handle(self, username, **options):
        try:
            user = CustomUser.objects.get(username=username)
        except CustomUser.DoesNotExist:
            raise CommandError(f"No user with username/email {username} found!")

        user.is_testuser = True
        user.save()
        print(f"{username} successfully set to testuser")
