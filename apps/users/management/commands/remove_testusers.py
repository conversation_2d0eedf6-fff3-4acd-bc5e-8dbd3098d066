from datetime import timedelta

from django.core.management.base import BaseCommand
from django.utils import timezone

from apps.users.models import CustomUser


class Command(BaseCommand):
    help = "Deletes users with flag testuser in certain duration"

    def add_arguments(self, parser):
        parser.add_argument("duration", type=int, help="Duration in H for filtering users", nargs="?", default=24)

    def handle(self, duration, **options):
        time_threshold = timezone.now() - timedelta(hours=duration)
        recent_entries = CustomUser.objects.filter(last_login__lt=time_threshold, is_testuser=True)

        count = recent_entries.count()

        if count == 0:
            self.stdout.write(self.style.SUCCESS(f"Nothing changed"))
            return

        recent_entries.delete()
        self.stdout.write(self.style.SUCCESS(f"Successfully deleted {count} test users in range of {duration}H"))
