# Generated by Django 4.2.13 on 2024-07-21 22:13

from django.conf import settings
import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0004_customuser_is_testuser"),
    ]

    operations = [
        migrations.CreateModel(
            name="Question",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("name", models.Char<PERSON>ield(max_length=255, unique=True)),
                ("prompt", models.Char<PERSON>ield(max_length=1024)),
                ("purpose", models.CharField(blank=True, max_length=1024, null=True)),
                (
                    "options",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=100), blank=True, null=True, size=None
                    ),
                ),
                ("satisfied", models.BooleanField(default=False, null=True)),
                ("response", models.CharField(blank=True, max_length=1024, null=True)),
                (
                    "min_max",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=100), blank=True, null=True, size=None
                    ),
                ),
                ("input_type", models.CharField(max_length=50)),
                ("updated", models.BooleanField(default=False, null=True)),
            ],
        ),
        migrations.CreateModel(
            name="Section",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("title", models.CharField(max_length=255)),
                ("description", models.TextField()),
                ("image_file", models.CharField(max_length=255)),
                ("progress", models.FloatField()),
                (
                    "cues",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=100), blank=True, null=True, size=None
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="SectionResponse",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("response", models.TextField(blank=True, null=True)),
                ("section", models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="users.section")),
                ("user", models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name="QuestionResponse",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("response", models.TextField(blank=True, null=True)),
                ("question", models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="users.question")),
                (
                    "section_response",
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="users.sectionresponse"),
                ),
                ("user", models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name="question",
            name="section",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, related_name="questions", to="users.section"
            ),
        ),
    ]
