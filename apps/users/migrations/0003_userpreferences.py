# Generated by Django 4.2.1 on 2024-02-07 18:04

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0002_alter_customuser_options_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserPreferences",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("preference", models.CharField(default="", max_length=256)),
                ("score", models.IntegerField(default=5)),
                ("user", models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
