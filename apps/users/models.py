import hashlib
import logging

import stripe
from django.contrib.auth.models import AbstractUser
from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.db import models
from django.utils import timezone

from apps.subscriptions.models import SubscriptionModelBase
from apps.users.helpers import validate_profile_picture

log = logging.getLogger("homescore.subscription")


class CustomUser(SubscriptionModelBase, AbstractUser):
    """
    Add additional fields to the user model here.
    """

    is_testuser = models.BooleanField(default=False)
    avatar = models.FileField(upload_to="profile-pictures/", blank=True, validators=[validate_profile_picture])
    realtor_email = models.EmailField(max_length=254,blank=True, null=True)
    
    def __str__(self):
        return f"{self.get_full_name()} <{self.email or self.username}>"

    def get_display_name(self) -> str:
        if self.get_full_name().strip():
            return self.get_full_name()
        return self.email or self.username

    def remove_subscription(self):
        if self.subscription:
            # Optionally, cancel the Stripe subscription
            try:
                stripe.Subscription.delete(self.subscription.id)
            except stripe.error.StripeError as e:
                log.error(f"Stripe error: {e}")
                # Consider notifying the user or retrying the operation
            except Exception as e:
                log.error(f"Unexpected error: {e}")
                # Additional handling for unexpected errors

            self.subscription.delete()
            self.subscription = None
            self.customer = None
            self.billing_details_last_changed = timezone.now()
            self.last_synced_with_stripe = timezone.now()
            self.save()

            log.info(f"Subscription removed for user {self.email}")

    @property
    def avatar_url(self) -> str:
        if self.avatar:
            return self.avatar.url
        else:
            return "https://www.gravatar.com/avatar/{}?s=128&d=identicon".format(self.gravatar_id)

    @property
    def gravatar_id(self) -> str:
        # https://en.gravatar.com/site/implement/hash/
        return hashlib.md5(self.email.lower().strip().encode("utf-8")).hexdigest()


class UserPreferences(models.Model):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    preference = models.CharField(max_length=256, default="")
    score = models.IntegerField(default=5)


class Section(models.Model):
    title = models.CharField(max_length=255)
    description = models.TextField()
    image_file = models.CharField(max_length=255)
    progress = models.FloatField()
    cues = ArrayField(models.CharField(max_length=100), blank=True, null=True)  # Assuming cues is a list of strings


class Question(models.Model):
    name = models.CharField(max_length=255, unique=True)
    section = models.ForeignKey(Section, related_name="questions", on_delete=models.CASCADE)
    prompt = models.CharField(max_length=1024)
    purpose = models.CharField(max_length=1024, blank=True, null=True)  # Optional based on your JSON structure
    options = ArrayField(
        models.CharField(max_length=100), blank=True, null=True
    )  # Assuming options is a list of strings or integers
    satisfied = models.BooleanField(default=False, null=True)  # Assuming this is a boolean
    response = models.CharField(max_length=1024, blank=True, null=True)  # Assuming this is a string
    min_max = ArrayField(
        models.CharField(max_length=100), blank=True, null=True
    )  # Assuming this is a range, stored as a list of two floats
    input_type = models.CharField(max_length=50)  # Assuming you have a predefined set of input types
    updated = models.BooleanField(default=False, null=True)  # Assuming this is a boolean


class SectionResponse(models.Model):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    section = models.ForeignKey(Section, on_delete=models.CASCADE)
    response = models.TextField(blank=True, null=True)


class QuestionResponse(models.Model):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    question = models.ForeignKey(Question, on_delete=models.CASCADE)
    response = models.TextField(blank=True, null=True)
    section_response = models.ForeignKey(SectionResponse, on_delete=models.CASCADE)
