from celery import shared_task
from django.core.management import call_command
from django.shortcuts import get_object_or_404
from openai import BadRequestError, OpenAI, AzureOpenAI
import json
import os
import re
import yaml
import requests
import base64
from django.conf import settings
from django.core.mail import send_mail, EmailMessage
from django.utils.html import strip_tags
from collections import OrderedDict
from .models import Section, Question, SectionResponse, QuestionResponse
from .serializers import SectionSerializer, QuestionSerializer, SectionResponseSerializer, QuestionResponseSerializer

@shared_task
def remove_testusers_task(hours=24):
    call_command("remove_testusers", duration=hours)

client = AzureOpenAI(
    api_key=settings.AZURE_OPENAI_API_KEY,  
    api_version=settings.AZURE_OPENAI_API_VERSION,
    azure_endpoint = settings.AZURE_OPENAI_API_ENDPOINT
    )

@shared_task
def in_take_quiz(section_index, section_response, user=None, username=None):
    # Fetch the user if not provided
    if user is None and username is not None:
        user = CustomUser.objects.get(username=username)
    
    # Fetch the section
    section = Section.objects.all().order_by("id")[section_index]

    # Serialize the section
    serializer_data = SectionSerializer(section).data
    
    # Prepare system instruction
    system_instruction = f"""
    You are an expert survey taker. You are helping a real estate agent understand the needs of a prospective buyer. Use the following instructions to process the user's input and provide a structured output using function calling.

        1. User Input Processing:
        - Take the user's input provided via messages to the API.
        - Use the provided sections and questions as a reference for processing the input.

        2. Question Evaluation:
        - Determine if the user has answered all the questions in each section.
        - For each question, strictly identify it by its name.
        - Determine if the user's response satisfies the question's requirement based on the input.
        
        3. Children Question Handling:
        - If the user explicitly mentions children in the HouseHold Composition section, consider their response.
        - If the user does not mention children, fill the response as 0 for the children question in the HouseHold Composition section.

        4. Structured Output:
        - Provide a structured output indicating which questions have been answered and if the responses meet the requirements.
        - Use the function calling feature to return the structured data.

        5. Category and Question Data:
        - Refer to the following YAML data for each section and corresponding questions:
            {yaml.dump(serializer_data, default_flow_style=False, sort_keys=False)}
    Note: The user response so far is as follows:
    """

    tools = [
        {
            "type": "function",
            "function": {
                "name": "response_status",
                "description": "List of questions with their satisfaction status based on the user responses.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "response_status": {
                            "descriptions": "Individual questions with their satisfaction status.",
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "id": {
                                        "type": "string",
                                        "description": "Number of the question mention in the serializer data.",
                                    },
                                    "satisfied": {
                                        "type": "boolean",
                                        "description": "If user's response strictly satisfies the question's requirement.",
                                    },
                                    "response": {
                                        "type": ["string", "null"],
                                        "description": "The detailed response provided by the user, capturing their input or answer, which indicates satisfaction of the question's requirements.",
                                    },
                                },
                            },
                        }
                    },
                },
            },
        }
    ]
    
    # Prepare the messages to send to the LLM
    messages = [
        {"role": "system", "content": system_instruction},
        {"role": "user", "content": section_response},
    ]
    # Call the LLM function to get updated responses
    llm_responses = client.chat.completions.create(
        model=settings.OPENAI_QUIZ_MODEL,
        messages=messages,
        tools=tools,
        tool_choice="required",
        temperature=0.0,
    )
    
    output_dump = llm_responses.model_dump()
    response_json = ""
    choices = output_dump.get("choices", [])
    if choices:
        message = choices[0].get("message", {})
        tool_calls = message.get("tool_calls", [])
        if tool_calls:
            function = tool_calls[0].get("function", {})
            arguments = function.get("arguments", {})
            if arguments:
                response_json = arguments
    try:
        responses = json.loads(response_json).get("response_status", [])
    except json.JSONDecodeError as e:
        print(f"JSON decode error: {e}")
        responses = []
    
    print("Response:\n",responses, "\n")
    
    # Update the section with the new responses
    section_response, _ = SectionResponse.objects.update_or_create(
        user=user,
        section=section,
        defaults={"response": section_response}
    )
    
    question_map = {question["id"]: question for question in serializer_data["questions"]}
    for response in responses:
        question_id = int(response["id"])
        if question_id in question_map and response["satisfied"]:
            # Retrieve the Question instance
            question_instance = Question.objects.get(id=question_id)
            # Retrieve or create a QuestionResponse instance
            question_response, created = QuestionResponse.objects.get_or_create(
                user=user,
                question=question_instance,
                defaults={
                    "response": response["response"],
                    "section_response_id": section_response.id
                }
            )
            # If the QuestionResponse already existed, update the response
            if not created:
                question_response.response = response["response"]
                question_response.save()


    # Fetch all questions in the section 
    all_questions = Question.objects.filter(section=(section_index+1))
    # Fetch all user's responses for questions in the section
    answered_questions_ids = QuestionResponse.objects.filter(
    user=user
    ).values_list('question', flat=True)
    
    print("Answered Questions IDs:\n",list(answered_questions_ids), "\n")
    unanswered_questions = all_questions.exclude(id__in=list(answered_questions_ids))
    unanswered_questions_serialized = QuestionSerializer(unanswered_questions, many=True).data
    
    response_data = {
        "not_satisfied_questions": unanswered_questions_serialized
    }
    print("Response Data:\n",response_data, "\n")
    return response_data

def combine_responses_with_sections(sections, user):
    combined_sections = []
    
    for section in sections:
        section_instance = get_object_or_404(Section, id=section["id"])
        section_data = {
            "title": section_instance.title,
            "description": section_instance.description,
            "image_file": section_instance.image_file,
            "progress": section_instance.progress,
            "questions": []
        }
        
        for question in section["questions"]:
            question_instance = get_object_or_404(Question, id=question["id"])
            response_instance = QuestionResponse.objects.filter(question=question_instance, user=user).first()
            
            question_data = {
                "id": question_instance.id,
                "prompt": question_instance.prompt,
                "purpose": question_instance.purpose,
                "input_type": question_instance.input_type,
                "min_max": question_instance.min_max,
                "options": question_instance.options,
                "response": response_instance.response if response_instance else None,
            }
            
            section_data["questions"].append(question_data)
        
        combined_sections.append(section_data)
    
    return combined_sections

@shared_task
def process_quiz_summary(user):
    """
    A task to summarize the quiz section based on the user responses.
    """
    master_prompt = """
                You are a residential realtor and want to better understand prospective clients' situations to determine what they value most in buying a home.
                Given the buyer’s household composition, you will analyze how they value location & convenience, how they prioritize key property details, and their openness to accepting imperfections.
                Based on how the buyer answers the survey organize your output into the following fields

                    * Summary of Household Composition:
                        Create a two-column table with the first column titled “Demographics” and list out the demographic attribute titles for family structure and the second column should be titled Details and should state the demographic information of the prospective buyer.
                        Next in an outline summarize the home buyer's family structure, lifestyle, and specific needs related to their situation
                    * Location & Convenience:
                        Create a one-column table titled 'Key Points' and list the top 4 key insights regarding how the buyer values location and convenience.
                        Next, provide a summary that includes the following sections: Definition of Convenience, Key Aspects of a Good Location, and Undesirable Location Considerations.

                    Ensure the summary concisely details the buyer's definition of convenience, the key aspects that make up a good location for them, and any location considerations the buyer notes as not important or categorizes as a bad location.

                    * Property Priorities:
                        Objective:
                            Summarize the key features and rooms prioritized by the prospective client based on their responses in the 'Home Details' section and other relevant survey sections.
                            Provide detailed reasoning for why these areas are important to the client.

                    Instructions:
                        1. Identify Key Priorities:
                            * Review the open-ended responses and recorded answers to extract the most mentioned features and rooms.
                            * Pay attention to the specific details provided about each feature and room.
                        2. Analyze Context:
                            * Consider the client's lifestyle and preferences described in their responses.
                            * Highlight any patterns or recurring themes in their answers.
                        3. Summarize Priorities:
                            * List the top features and rooms the client prioritizes, such as the kitchen, living room, yard, etc.
                            * Explain why each feature or room is important based on the client's descriptions (e.g., love for cooking, need for a large family space, preference for outdoor activities).
                        4. Organize Insights in a Table:
                            * Use a table to clearly present the key features/rooms and the reasons behind their importance

                    Risk Tolerance:
                        Analyze the buyer's openness to accepting certain imperfections or issues with various properties based on the "Open to Doing Work" section and other insights from the survey. Identify the following:
                        1.“Will Accept” and “Wants to Avoid”:
                            * Which rooms, projects, or issues is the buyer okay with tolerating?
                            * Which specific areas or types of work does the buyer prefer to avoid?
                        2.Openness to Tradeoffs:
                            * Determine the buyer's willingness to make tradeoffs to find a property that meets their key needs. What are they willing to sacrifice to achieve their priorities?
                        3.Preferences and Lifestyle Considerations:
                            * Leverage insights, preferences, and lifestyle considerations provided throughout the survey to understand the buyer's overall risk tolerance and flexibility.How do these factors influence their openness to renovations or repairs?

                Summarize these findings in an organized outline and provide a brief understanding of the buyer's risk tolerance, helping to match them with the most suitable properties.
                """

    tools= [
        {
        "type": "function",
        "function": {
            "name": "generate_quiz_summary",
            "description": "Generate a detailed summary for a real estate quiz based on user inputs.",
            "parameters": {
            "type": "object",
            "properties": {
                "SummaryofHouseholdComposition": {
                "type": "object",
                "properties": {
                    "Table": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                        "Demographics": {
                            "type": "string"
                        },
                        "Details": {
                            "type": "string"
                        }
                        },
                        "required": ["Demographics", "Details"]
                    }
                    },
                    "Additional": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "description": "Includes summary of Family Structure, Lifestyle, and Specific Needs."
                    }
                },
                "required": ["Table", "Additional"]
                },
                "LocationAndConvenience": {
                "type": "object",
                "properties": {
                    "Table": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                    },
                    "Additional": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "description": "Includes Definition of Convenience, Key Aspects of a Good Location, and Undesirable Location Considerations."
                    }
                },
                "required": ["Table", "Additional"]
                },
                "PropertyPriorities": {
                "type": "object",
                "properties": {
                    "Table": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                    },
                    "Additional": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "description": "Includes Objective, Key Features/Rooms, and Reasoning for their importance."
                    }
                },
                "required": ["Table", "Additional"]
                },
                "RiskTolerance": {
                "type": "object",
                "properties": {
                    "WillAccept": {
                    "type": "string"
                    },
                    "WantsToAvoid": {
                    "type": "string"
                    },
                    "OpennessToTradeoffs": {
                    "type": "string"
                    },
                    "PreferencesAndLifestyleConsiderations": {
                    "type": "string"
                    }
                },
                "required": ["WillAccept", "WantsToAvoid", "OpennessToTradeoffs", "PreferencesAndLifestyleConsiderations"]
                }
            },
            "required": [
                "SummaryofHouseholdComposition",
                "LocationAndConvenience",
                "PropertyPriorities",
                "RiskTolerance"
            ],
            }
        }
        }
    ]

    sections = Section.objects.all().order_by("id")
    sections_json = [{"id": section.id, "questions": [{"id": question.id} for question in section.questions.all()]} for section in sections]
    
    combined_data = combine_responses_with_sections(sections_json, user)

    messages = [
            {"role": "system", "content": master_prompt},
            {"role": "user", "content": f"Summarize the home buyer's preferences based on the following information:\n\n{combined_data}"},
            ]
    
    try:
        # Generate summary using OpenAI API
        response = client.chat.completions.create(
            model=settings.OPENAI_QUIZ_MODEL,
            messages=messages,
            tools=tools,
            tool_choice="required",
            temperature=0.0,
        )
        summary = ""
        response_dump = response.model_dump()
        choices = response_dump.get("choices", [])
        if choices:
            message = choices[0].get("message", {})
            tool_calls = message.get("tool_calls", [])
            if tool_calls:
                function = tool_calls[0].get("function", {})
                print("tool calls:", tool_calls, "\n")
                arguments = function.get("arguments", {})
                print("Arguments:",arguments, "\n")
                if arguments:
                    response_json = arguments
        try:
            summary = json.loads(response_json)
        except json.JSONDecodeError as e:
            print(f"JSON decode error: {e}")
            summary = {}
        return summary
    except Exception as e:
        print(f"Error generating summary: {e}")

def format_quiz_summary_for_email(data):
    # HTML Template for the email
    html_content = f"""
    <html>
    <body>
        <div>
            <table width="100%" cellpadding="0" cellspacing="0" border="0" style="background: black !important; padding: 1rem; text-align: center;">
                <tr>
                    <td align="center">
                        <table cellpadding="0" cellspacing="0" border="0" style="background: black !important; padding: 1rem;">
                            <tr>
                                <td>
                                    <img src="cid:logo" alt="logo" style="height: 5rem;"/>
                                </td>
                            </tr>
                            <tr>
                                <td style="text-align: center;">
                                    <h3 style="color: white !important;"><strong>Your Buyer Preferences</strong></h3>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
            <div style="background: linear-gradient(#FFF1EB, #EEE5E0); padding: 1rem;">
                <table border="1" cellpadding="5" cellspacing="0" style="border: transparent !important; background-color: transparent; margin-bottom: 10px">
                    <tr>
                        <th><img src="cid:oasis" alt="oasis" style="height: 5rem;" /></th>
                        <th><h2 style="color: #D26145; margin-bottom: 0px; margin-top: auto">My Household</h2></th>
                    </tr>
                </table>
                <table border="1" cellpadding="5" cellspacing="0" style="border: white !important; background-color: #D26145;">
                    <tr>
                        <th style="color: white !important">Demographics</th>
                        <th style="color: white !important">Details</th>
                    </tr>
                    {"".join([f'<tr><td style="color: white !important">{item["Demographics"]}</td><td style="color: white">{item["Details"]}</td></tr>' for item in data['SummaryofHouseholdComposition']['Table']])}
                </table>
                <h3 style="color: #D26145;"><strong>Summary:</strong></h3>
                <p style="color: black !important">
                    {" ".join([f"{item}" for item in data['SummaryofHouseholdComposition']['Additional']])}
                </p>
            </div>
            <div style="background: linear-gradient(#c68e8e, #ee5656); padding: 1rem;">
                <table border="1" cellpadding="5" cellspacing="0" style="border: transparent !important; background-color: transparent; margin-bottom: 10px">
                    <tr>
                        <th><img src="cid:house" alt="house" style="height: 5rem;" /></th>
                        <th><h2 style="color: black !important; margin-bottom: 0px; margin-top: auto">Location & Convenience</h2></th>
                    </tr>
                </table>
                <table border="1" cellpadding="5" cellspacing="0" style="border: white !important; background-color: rgb(117, 34, 34);">
                    <tr>
                        <th style="color: white !important">Key Points</th>
                    </tr>
                    {"".join([f'<tr><td style="color: white !important">{item}</td></tr>' for item in data['LocationAndConvenience']['Table']])}
                </table>
                <h3 style="color: black !important"><strong>Additional Information:</strong></h3>
                <div>
                    {"".join([f'<p style="color: black !important">{item}</p>' for item in data['LocationAndConvenience']['Additional']])}
                </div>
            </div>
            <div style="background: linear-gradient(#E3C297, #D1C9BC); padding: 1rem;">
                <table border="1" cellpadding="5" cellspacing="0" style="border: transparent !important; background-color: transparent; margin-bottom: 10px">
                    <tr>
                        <th><img src="cid:details" alt="details" style="height: 5rem;" /></th>
                        <th><h2 style="color: rgb(95, 79, 45); margin-bottom: 0px; margin-top: auto">Property Priorities</h2></th>
                    </tr>
                </table>
                <table border="1" cellpadding="5" cellspacing="0" style="border: white !important; background-color: rgb(95, 79, 45);">
                    <tr>
                        <th style="color: white !important">Feature/Room</th>
                    </tr>
                    {"".join([f'<tr><td style="color: white !important">{item}</td></tr>' for item in data['PropertyPriorities']['Table']])}
                </table>
                <h3 style="color: rgb(95, 79, 45);"><strong>Additional Information:</strong></h3>
                <div>
                    {"".join([f'<p style="color: black !important">{item}</p>' for item in data['PropertyPriorities']['Additional']])}
                </div>
            </div>
            <div style="background: linear-gradient(#83B5C7, #89BBC3); padding: 1rem;">
                <table border="1" cellpadding="5" cellspacing="0" style="border: transparent !important; background-color: transparent; margin-bottom: 10px">
                    <tr>
                        <th><img src="cid:fix" alt="fix" style="height: 5rem;" /></th>
                        <th><h2 style="color: black; margin-bottom: 0px; margin-top: auto">Risk Tolerance</h2></th>
                    </tr>
                </table>
                <p style="color: black !important"><strong>Will Accept:</strong> {data['RiskTolerance']['WillAccept']}</p>
                <p style="color: black !important"><strong>Wants to Avoid:</strong> {data['RiskTolerance']['WantsToAvoid']}</p>
                <p style="color: black !important"><strong>Openness to Tradeoffs:</strong> {data['RiskTolerance']['OpennessToTradeoffs']}</p>
                <p style="color: black !important"><strong>Preferences and Lifestyle Considerations:</strong> {data['RiskTolerance']['PreferencesAndLifestyleConsiderations']}</p>
            </div
        </div>
    </body>
    </html>
    """

    return html_content

from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import Mail, Attachment, FileContent, FileName, FileType, Disposition, ContentId


def send_preference_email(email, summary):
    email_content = format_quiz_summary_for_email(summary)
    
    # Ensure email is a list of strings
    if isinstance(email, list):
        email = [str(e) for e in email]
    else:
        email = [str(email)]
    
    # Create the email message
    message = Mail(
        from_email=settings.DEFAULT_FROM_EMAIL,
        to_emails=email,
        subject="Client's Home Buying Preferences Summary",
        plain_text_content=strip_tags(email_content),
        html_content=email_content
    )
    print("Message:", message)
    
    # Function to encode and attach images
    def attach_image(message, image_path, cid, filename, filetype):
        with open(image_path, 'rb') as f:
            data = f.read()
            encoded = base64.b64encode(data).decode()
        attachment = Attachment()
        attachment.file_content = FileContent(encoded)
        attachment.file_type = FileType(filetype)
        attachment.file_name = FileName(filename)
        attachment.disposition = Disposition('inline')
        attachment.content_id = ContentId(cid)
        message.add_attachment(attachment)

    current_dir = os.path.dirname(os.path.abspath(__file__))
    attach_image(message, os.path.join(current_dir, '../../assets/images/inverseLogo.png'), 'logo', 'logo.jpg', 'image/jpeg')
    attach_image(message, os.path.join(current_dir, '../../assets/images/redhouse.png'), 'house', 'house.jpg', 'image/jpeg')
    attach_image(message, os.path.join(current_dir, '../../assets/images/fancyRoom.png'), 'details', 'details.jpg', 'image/jpeg')
    attach_image(message, os.path.join(current_dir, '../../assets/images/fix.png'), 'fix', 'fix.jpg', 'image/jpeg')
    attach_image(message, os.path.join(current_dir, '../../assets/images/oasis.png'), 'oasis', 'oasis.jpg', 'image/jpeg')

    # Send the email using SendGridAPIClient
    try:
        api_key = os.getenv("ANYMAIL_SENDGRID_API_KEY")
        sg = SendGridAPIClient(api_key)
        response = sg.send(message)
        print("Response Status Code",response.status_code)
        print("\n")
        print("Response Body",response.body)
        print("\n")
        print("Response Headers",response.headers)
        print("\n")
    except Exception as e:
        print(str(e))