from rest_framework import serializers

from .models import CustomUser, UserPreferences, Section, Question, SectionResponse, QuestionResponse


class CustomUserSerializer(serializers.ModelSerializer):
    """
    Basic serializer to pass CustomUser details to the front end.
    Extend with any fields your app needs.
    """

    class Meta:
        model = CustomUser
        fields = ("id", "first_name", "last_name", "email")


# Serializer for UserPreferences class
class UserPreferencesSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserPreferences
        fields = ("id", "user", "preference", "score")


#Serializer for Question class
class QuestionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Question
        fields = ("id", "name", "section", "prompt", "purpose", "options", "satisfied", "response", "min_max", "input_type", "updated")


#Serializer for Section class
class SectionSerializer(serializers.ModelSerializer):
    #This line adds a questions field to the serializer that will return a list of all questions in the section
    questions = QuestionSerializer(many=True, read_only=True)
    
    class Meta:
        model = Section
        fields = ("id", "title", "description", "image_file", "progress", "cues", "questions")

# Serializer for SectionResponse class
class SectionResponseSerializer(serializers.ModelSerializer):
    class Meta:
        model = SectionResponse
        fields = ("id", "user", "section", "response")

# Serializer for QuestionResponse class
class QuestionResponseSerializer(serializers.ModelSerializer):
    class Meta:
        model = QuestionResponse
        fields = ("id", "user", "question", "response", "section_response")
