from django.urls import path

from . import views

app_name = "users"
urlpatterns = [
    path("profile/", views.profile, name="user_profile"),
    path("profile/upload-image/", views.upload_profile_image, name="upload_profile_image"),
    path("csrf/", views.get_csrf, name="api-csrf"),
    path("login/", views.LoginAPIView.as_view(), name="user-login"),
    path("logout/", views.LogoutView.as_view(), name="user-logout"),
    path("session/", views.SessionView.as_view(), name="user-session"),
    path("whoami/", views.WhoAmIView.as_view(), name="user-whoami"),
    # path to get and update user preferences
    path("user-preferences/", views.UserPreferencesAPIView.as_view(), name="user-preferences"),
    path("quiz/", views.ProcessSection.as_view(), name="quiz"),
    path("summary/", views.QuizSummary.as_view(), name="quiz-summary"),
    path("getSections/", views.GetSections.as_view(), name="get-sections"),
    path("section_response/", views.SectionResponseAPIView.as_view(), name="section_response"),
    path("question_response/", views.QuestionResponseAPIView.as_view(), name="question_response"),
    path("delete_section/", views.DeleteSectionResponse.as_view(), name="delete_section"),
    path("delete_question/", views.DeleteQuestionResponse.as_view(), name="delete_question"),
    path("send_email/", views.ShareSummary.as_view(), name="send_email"),
]
