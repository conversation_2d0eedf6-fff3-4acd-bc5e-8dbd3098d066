from typing import Optional
from celery.result import AsyncResult
from celery_progress.backend import Progress
from django.contrib.auth.decorators import login_required
from django.http import HttpResponseRedirect
from django.shortcuts import get_object_or_404
from django.template.response import TemplateResponse
from django.urls import reverse
from django.utils.decorators import method_decorator
from django.utils.translation import gettext as _
from django.views.decorators.http import require_POST
from drf_spectacular.utils import extend_schema
from rest_framework import generics, mixins
from rest_framework.exceptions import ValidationError
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.decorators import api_view

from apps.chat.api_url_helpers import get_chat_api_url_templates
from apps.chat.models import Chat, ChatMessage
from apps.reports.models import Report
from apps.chat.serializers import ChatMessageSerializer, ChatSerializer
from apps.chat.tasks import get_chatgpt_response
from django.db.models import Q
from rest_framework.pagination import PageNumberPagination


@login_required
def chat_home(request):
    chats = request.user.chats.exclude(name="")
    return TemplateResponse(
        request,
        "chat/chat_home.html",
        {
            "active_tab": "openai",
            "chats": chats,
        },
    )


@require_POST
@login_required
def start_chat(request):
    chat = Chat.objects.create(
        user=request.user,
    )

    return HttpResponseRedirect(reverse("chat:single_chat", args=[chat.id]))


@login_required
@api_view(["POST"])
def create_chat(request):
    data = request.data
    report = get_object_or_404(Report, id=data.get("report"))
    issue = data.get("issue")
    if not report:
        return Response({"error": "Valid report ID is required"}, status=400)
    chat = Chat.objects.create(user=request.user, report=report, file_name=report.file_name, name=issue)
    return Response(ChatSerializer(chat).data, content_type="application/json")


@login_required
def single_chat_react(request, chat_id: int):
    chat = get_object_or_404(Chat, user=request.user, id=chat_id)
    serialized_chat = ChatSerializer(chat).data
    return TemplateResponse(
        request,
        "chat/single_chat_react.html",
        {
            "active_tab": "openai",
            "chat": chat,
            "serialized_chat": serialized_chat,
            "api_urls": get_chat_api_url_templates(),
        },
    )


@extend_schema(tags=["chat"], exclude=True)
class ChatAPI(generics.ListAPIView):
    serializer_class = ChatSerializer
    pagination_class = None  # Remove pagination

    def get_queryset(self):
        queryset = Chat.objects.filter(user=self.request.user)
        id = self.request.query_params.get("id")
        report_id = self.request.query_params.get("report")
        issue = self.request.query_params.get("issue")
        if id:
            queryset = Chat.objects.filter(id=id)
        else:
            report = get_object_or_404(Report, id=report_id)
            queryset = Chat.objects.filter(report=report, name=issue)
        return queryset


@extend_schema(tags=["chat"], exclude=True)
class NewChatMessageAPI(mixins.CreateModelMixin, generics.GenericAPIView):
    serializer_class = ChatMessageSerializer
    pagination_class = None  # Remove pagination

    def get_queryset(self):
        return ChatMessage.objects.filter(chat__user=self.request.user)

    @method_decorator(login_required)
    def post(self, request, chat_id, *args, **kwargs):
        # ensure user can access chat
        self.chat = get_object_or_404(Chat, id=chat_id)
        # set some values we'll need later
        self.chat_id = chat_id
        self.is_first_message = not self.chat.messages.exists()
        response = self.create(request, *args, **kwargs)
        response.data["task_id"] = self.task_id  # add task_id to the response so it can be queried
        return response

    def perform_create(self, serializer):
        data = self.request.data
        if serializer.validated_data["chat"] != self.chat:
            raise ValidationError(_("Invalid Chat ID."))
        # save model
        instance = serializer.save(user=self.request.user, issue=data.get("issue"))
        # process message
        result = get_chatgpt_response.delay(
            chat_id=self.chat_id, message=instance.content, user=self.request.user.id, issue=data.get("issue")
        )
        self.task_id = result.task_id
        # if self.is_first_message:
        #    set_chat_name.delay(self.chat_id, instance.content)


@extend_schema(tags=["chat"], exclude=True)
class GetMessageResponseAPI(APIView):
    serializer_class = ChatMessageSerializer
    pagination_class = None  # Remove pagination

    def get(self, request, chat_id, task_id):
        # TODO: this get_object_or_404 get be used to get Infopay chats by non-Infopay users
        chat = get_object_or_404(Chat, id=chat_id)
        progress = Progress(AsyncResult(task_id))
        return Response(progress.get_info())
