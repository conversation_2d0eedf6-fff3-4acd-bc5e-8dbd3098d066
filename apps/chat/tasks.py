import logging
import os

from pinecone import <PERSON>con<PERSON>
from celery import shared_task
from django.conf import settings
from langchain_community.vectorstores import Pinecone as PineconeCommunity
from langchain_openai import OpenAIEmbeddings, AzureOpenAIEmbeddings
from openai import OpenAI, AzureOpenAI

from apps.chat.models import Chat, ChatMessage, MessageTypes
from apps.chat.serializers import ChatMessageSerializer
from apps.reports.models import Report
from apps.users.models import CustomUser

# Fix this, if OpenAI key is not provided
client = AzureOpenAI(
    api_key=settings.AZURE_OPENAI_API_KEY,  
    api_version=settings.AZURE_OPENAI_API_VERSION,
    azure_endpoint = settings.AZURE_OPENAI_API_ENDPOINT
    )


SYSTEM = """Your are HomeScoreAI, a home inspection and home care expert.
    Your goal is to help prospective home buyers be more informed about their homes.
    You help them answer questions about the issues with their house they are buying.
    For their questions, provide some useful context for first time home buyer.
    Provide recommendations to address the issue and cost estimates, urgency of the addressing the issue.
    Keep your answers very brief and under 200 words. Refuse to answer any questions that are not related
    to home buying, home inspection or home care.
   """
logger = logging.getLogger(__file__)


@shared_task(bind=True)
def get_chatgpt_response(self, chat_id: int, message: str, user: str, issue: str) -> str:
    chat = Chat.objects.get(id=chat_id)
    logger.info("Interacting with chat:%s", chat)
    report = Report.objects.filter(id=chat.report.id).first()
    user = CustomUser.objects.get(id=user)
    if not report:
        report = Report.objects.filter(file_name=chat.file_name).first()
    messages = chat.get_openai_messages()
    messages.insert(0, {"role": "system", "content": SYSTEM})

    pc = Pinecone(api_key=settings.PINECONE_API_KEY, environment=settings.PINECONE_ENV)
    embeddings = AzureOpenAIEmbeddings(model=settings.AZURE_OPENAI_EMBEDDINGS_MODEL)
    doc_search = PineconeCommunity.from_existing_index(settings.PINECONE_INDEX, embeddings)
    similar_docs = []
    if report:
        similar_docs = doc_search.similarity_search_with_score(message, k=settings.TOPK, filter={"filename": report.file_name})
        print(similar_docs)
    if similar_docs:
        context = "Use additional context below to answer the question: \n"
        context += report.details
        context += "\n".join(
            [doc.metadata.get("source") + doc.page_content for doc, score in similar_docs if score > settings.SIMILARITY_THRESHOLD]
        )
        messages.append({"role": "user", "content": context})
    messages.append({"role": "user", "content": message})
    if issue:
        messages.append(
            {
                "role": "user",
                "content": f"The message is discussing issue: {issue}. Your answers should be related to {issue}",
            }
        )

    openai_response = client.chat.completions.create(model=settings.OPENAI_MODEL, messages=messages)
    response_body = openai_response.choices[0].message.content.strip()
    message = ChatMessage.objects.create(
        chat_id=chat_id,
        user=user,
        issue=issue,
        message_type=MessageTypes.AI,
        content=response_body,
    )
    return ChatMessageSerializer(message).data

@shared_task(bind=True)
def get_chatgpt_response_checker(self, chat_id: str, message: str, user: str, issue: str) -> str:
    chat = Chat.objects.get(id=chat_id)
    logger.info("Interacting with chat:%s", chat)
    #For now not using reports for checker
    # report = Report.objects.filter(id=chat.report.id).first()
    report=None

    user = CustomUser.objects.get(id=user)
    if not report:
        report = Report.objects.filter(file_name=chat.file_name).first()

    #How to handle existing messages for checker?
    # messages = chat.get_openai_messages()
    messages = []
    messages.insert(0, {"role": "system", "content": SYSTEM})

    if issue:
        messages.append(
            {
                "role": "user",
                "content": f"The message is discussing issue: {issue}. Your answers should be related to {issue}",
            }
        )
    else:
        logger.error("No issue provided")
        return

    pc = Pinecone(api_key=settings.PINECONE_INFOPAY_API_KEY)
    embeddings = AzureOpenAIEmbeddings(model=settings.AZURE_OPENAI_EMBEDDINGS_MODEL)
    doc_search = PineconeCommunity.from_existing_index(settings.PINECONE_INFOPAY_INDEX, embeddings)

    similar_docs = doc_search.similarity_search_with_score(message, k=settings.TOPK,filter={"checker_id": chat_id})

    context = "\n".join(
        [doc.metadata.get("source") + doc.page_content for doc, score in similar_docs if score > settings.SIMILARITY_THRESHOLD]
    )
    
    logger.info("Context: %s", context)
    if context:
        context = "Use additional context below to answer the question: \n"+context
        messages.append({"role": "user", "content": context})
    else:
        #Assume it's a general question and let the model answer it
        logger.info("No context found")

    messages.append({"role": "user", "content": message})
    openai_response = client.chat.completions.create(model=settings.OPENAI_MODEL, messages=messages)
    response_body = openai_response.choices[0].message.content.strip()

    message = ChatMessage.objects.create(
        chat_id=chat_id,
        user=user,
        issue=issue,
        message_type=MessageTypes.AI,
        content=response_body,
    )
    return ChatMessageSerializer(message).data

@shared_task
def set_chat_name(chat_id: int, message: str):
    chat = Chat.objects.get(id=chat_id)
    if not message:
        return
    elif len(message) < 20:
        # for short messages, just use them as the chat name. the summary won't help
        chat.name = message
        chat.save()
    else:
        # set the name with openAI
        system_naming_prompt = """
    You are SummaryBot. When I give you an input, your job is to summarize the intent of that input.
    Provide only the summary of the input and nothing else.
    Summaries should be less than 100 characters long.
    """
        openai_response = client.chat.completions.create(
            model=settings.OPENAI_MODEL,
            messages=[
                {
                    "role": "system",
                    "content": system_naming_prompt,
                },
                {
                    "role": "user",
                    "content": f"Summarize the following text: '{message}'",
                },
            ],
        )
        response_body = openai_response.choices[0].message.content.strip()
        chat.name = response_body[:100]
        chat.save()
