# Generated by Django 4.2.1 on 2024-03-26 10:58

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("reports", "0012_alter_collaborators_collaborator"),
        ("chat", "0003_alter_chat_options"),
    ]

    operations = [
        migrations.AddField(
            model_name="chat",
            name="report",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="report_chats",
                to="reports.report",
            ),
        ),
        migrations.AddField(
            model_name="chatmessage",
            name="issue",
            field=models.TextField(default=""),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="chatmessage",
            name="user",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="chats",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="chat",
            name="user",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="user_chats",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
