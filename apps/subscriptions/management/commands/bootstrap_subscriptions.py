import os

from django.core.management import call_command
from django.core.management.base import BaseCommand
from djstripe.models import APIKey, Product
from djstripe.settings import djstripe_settings
from stripe.error import AuthenticationError

from apps.subscriptions.metadata import ProductMetadata
from apps.utils.billing import create_stripe_api_keys_if_necessary


class Command(BaseCommand):
    help = "Bootstraps your Stripe subscriptions"

    def handle(self, **options):
        print("Syncing products and plans from Stripe")
        try:
            if create_stripe_api_keys_if_necessary():
                print("Added Stripe secret key to the database...")
            call_command("djstripe_sync_models", "price")
        except AuthenticationError:
            print(
                "\n======== ERROR ==========\n"
                "Failed to authenticate with Stripe! Check your Stripe key settings.\n"
                "More info: https://docs.saaspegasus.com/subscriptions.html#getting-started"
            )
        else:
            print("Done! Creating default product configuration")
            _create_default_product_config()


def _create_api_keys_if_necessary():
    key, created = APIKey.objects.get_or_create_by_api_key(djstripe_settings.STRIPE_SECRET_KEY)
    if created:
        print("Added Stripe secret key to the database...")


def _generate_products_file(product_metas):
    # Define the file path for the generated code
    file_path = os.path.join("apps", "subscriptions", "__products__.py")
    active_products_code = "ACTIVE_PRODUCTS = [\n\t" + ",\n\t".join(str(meta) for meta in product_metas) + "\n]"

    # Write the ACTIVE_PRODUCTS code to the file
    with open(file_path, "w") as file:
        file.write("# Automatically generated by bootstrap_subscriptions\n")
        file.write("from apps.subscriptions.metadata import ProductMetadata\n\n")
        file.write(active_products_code)
        file.write("\n")


def _create_default_product_config():
    # make the first product the default
    default = True
    product_metas = []
    for product in Product.objects.filter(active=True):
        product_meta = ProductMetadata.from_stripe_product(
            product,
            description=f"The {product.name} plan",
            is_default=default,
            features=[
                "{} Feature 1".format(product.name),
                "{} Feature 2".format(product.name),
                "{} Feature 3".format(product.name),
            ],
        )
        default = False
        product_metas.append(product_meta)

    _generate_products_file(product_metas)
