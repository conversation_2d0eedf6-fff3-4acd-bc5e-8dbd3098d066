import logging

from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse, HttpResponseRedirect, JsonResponse
from django.shortcuts import get_object_or_404, render
from django.urls import reverse
from django.utils import timezone
from django.db.models import Prefetch
from django.core.exceptions import ObjectDoesNotExist
from django.utils.translation import gettext_lazy as _
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods, require_GET
from djstripe.enums import SubscriptionStatus
from djstripe.models import Subscription, Product, Price
from djstripe.settings import djstripe_settings
from stripe.error import InvalidRequestError, StripeError

from apps.users.models import CustomUser
from apps.utils.billing import get_stripe_module

from ..decorators import active_subscription_required, redirect_subscription_errors
from ..forms import UsageRecordForm
from ..helpers import get_subscription_urls, subscription_is_active, subscription_is_trialing
from ..models import SubscriptionModelBase, UserUsage
from ..wrappers import InvoiceFacade, SubscriptionWrapper
from ..helpers import get_subscription_details

log = logging.getLogger("homescore.subscription")

logger = logging.getLogger(__file__)

stripe = get_stripe_module()


@redirect_subscription_errors
@login_required
def subscription(request):
    subscription_holder = request.user
    if subscription_holder.has_active_subscription():
        return _view_subscription(request, subscription_holder)
    else:
        return _upgrade_subscription(request, subscription_holder)

@login_required
@require_GET
def subscriptions_json(request):
    logger.debug("Starting subscriptions_json for user: %s", request.user)
    subscription_holder = request.user
    customer = subscription_holder.customer
    subscription = Subscription.objects.filter(customer=request.user.customer, status='active').first()

    # Define rate limits for different plan levels
    rate_limits = {
        "no_plan": 2,     # 1 sample + 1 free
        "5-properties": 7,  # 7 properties per subscription
        "Launch Offer": None  # No limit
    }
    
    # Get the user's plan
    user_plan = get_user_plan(subscription)
    logger.debug("User plan: %s", user_plan)

    # Get or create the user's usage record
    usage, created = UserUsage.objects.get_or_create(
        user=subscription_holder,
        defaults={"homes_added": 1}
    )
    logger.debug("UserUsage record: %s, created: %s", usage, created)

    if customer and not usage.customer:
        usage.customer = customer
        usage.save()
        logger.debug("Updated UserUsage record with customer: %s", usage.customer)

    # Get the rate limit for the user's plan
    rate_limit = rate_limits.get(user_plan)
    logger.debug("Rate limit: %s", rate_limit)

    # Calculate remaining uploads
    if rate_limit is None:
        remaining = "Unlimited"
    else:
        remaining = max(0, rate_limit - usage.homes_added)
    logger.debug("Remaining uploads: %s", remaining)

    subscription_data = get_subscription_details(subscription) if subscription else None

    data = {
        "has_active_subscription": subscription is not None,
        "subscription": subscription_data,
        "client_reference_id": subscription_holder.id,
        "usage": {
            "plan": user_plan,
            "homes_added": usage.homes_added,
            "limit": "Unlimited" if rate_limit is None else rate_limit,
            "remaining_uploads": remaining,
            "additional_homes_allowed": rate_limit - 2 if rate_limit else "Unlimited",
        }
    }

    logger.debug("Response data: %s", data)
    return JsonResponse(data)

def get_user_plan(subscription):
    if not subscription:
        return "no_plan"
    try:
        # Assuming the product ID represents the plan name
        product_id = subscription.plan.product.id
        plan_mapping = {
            "prod_R1FIELEJZmnayD": "5-properties",
            "prod_R1FHot1Rm1SRP6": "Launch Offer",
            # Add other product ID to plan slug mappings here
        }
        return plan_mapping.get(product_id, "unknown_plan")
    except AttributeError:
        # Handle cases where the subscription might not have a plan or product
        return "unknown_plan"

def _view_subscription(request, subscription_holder: SubscriptionModelBase):
    """
    Show user's active subscription
    """
    assert subscription_holder.has_active_subscription()
    subscription = subscription_holder.active_stripe_subscription
    next_invoice = None
    if subscription_is_trialing(subscription) and not subscription.default_payment_method:
        # trialing subscriptions with no payment method set don't have invoices so we can skip that check
        pass
    elif not subscription.cancel_at_period_end:
        try:
            next_invoice = stripe.Invoice.upcoming(
                subscription=subscription.id,
            )
        except InvalidRequestError:
            # this error is raised if you try to get an invoice but the subscription is canceled or deleted
            # check if this happened and redirect to the upgrade page if so
            subscription_is_invalid = False
            try:
                stripe_subscription = stripe.Subscription.retrieve(subscription.id)
            except InvalidRequestError:
                log.error(
                    "The subscription could not be retrieved from Stripe. "
                    "If you are running in test mode, it may have been deleted."
                )
                stripe_subscription = None
                subscription_holder.subscription = None
                subscription_holder.save()
                subscription_is_invalid = True
            if stripe_subscription and (
                stripe_subscription.status != SubscriptionStatus.active or stripe_subscription.cancel_at_period_end
            ):
                log.warning(
                    "A canceled subscription was not synced to your app DB. "
                    "Your webhooks may not be set up properly. "
                    "See: https://docs.saaspegasus.com/subscriptions.html#webhooks"
                )
                # update the subscription in the database and clear from the subscription_holder
                subscription.sync_from_stripe_data(stripe_subscription)
                subscription_is_invalid = True
            elif stripe_subscription:
                # failed for some other unexpected reason.
                raise

            if subscription_is_invalid:
                subscription_holder.refresh_from_db()
                subscription_holder.clear_cached_subscription()

                if not subscription_is_active(subscription):
                    return _upgrade_subscription(request, subscription_holder)

    wrapped_subscription = SubscriptionWrapper(subscription_holder.active_stripe_subscription)
    return render(
        request,
        "subscriptions/view_subscription.html",
        {
            "active_tab": "subscription",
            "page_title": _("Subscription"),
            "subscription": wrapped_subscription,
            "next_invoice": InvoiceFacade(next_invoice) if next_invoice else None,
            "subscription_urls": get_subscription_urls(subscription_holder),
        },
    )

def _view_subscription_json(request, subscription_holder):
    subscription = subscription_holder.active_stripe_subscription
    subscription_data = get_subscription_details(subscription) if subscription else None

    data = {
        'has_active_subscription': subscription is not None,
        'subscription': subscription_data,
        'client_reference_id': subscription_holder.id,
    }

    return JsonResponse(data)

@require_http_methods(["GET"])
def pricing_table_json(request):
    logger.info("Fetching pricing table JSON")
    try:
        products = Product.objects.filter(active=True).prefetch_related(
            Prefetch('prices', queryset=Price.objects.filter(active=True))
        )
        # Fetch all active products and their active prices
        products = Product.objects.filter(active=True)
        
        data = {
            'stripe_public_key': djstripe_settings.STRIPE_PUBLIC_KEY,
            'stripe_pricing_table_id': settings.STRIPE_PRICING_TABLE_ID,
            'products': []
        }

        for product in products:
            prices = product.prices.all()
            
            product_data = {
                'id': product.id,
                'name': product.name,
                'description': product.description,
                'prices': [
                    {
                        'id': price.id,
                        'interval': price.recurring.get('interval') if price.recurring else 'one_time',
                        'interval_count': price.recurring.get('interval_count') if price.recurring else None,
                        'amount': price.unit_amount,
                        'currency': price.currency,
                    }
                    for price in prices
                ]
            }
            if product_data['prices']:  # Only include products with active prices
                data['products'].append(product_data)

        return JsonResponse(data)
    except ObjectDoesNotExist as e:
        logger.error(f"Database object not found: {str(e)}")
        return JsonResponse({'error': 'Requested pricing information is not available.'}, status=404)
    except StripeError as e:
        logger.error(f"Stripe API error: {str(e)}")
        return JsonResponse({'error': 'Unable to retrieve pricing information. Please try again later.'}, status=503)

def _upgrade_subscription(request, subscription_holder: SubscriptionModelBase):
    return render(
        request,
        "subscriptions/upgrade_subscription.html",
        {
            "active_tab": "subscription",
            "stripe_public_key": djstripe_settings.STRIPE_PUBLIC_KEY,
            "stripe_pricing_table_id": settings.STRIPE_PRICING_TABLE_ID,
            "client_reference_id": subscription_holder.id,
        },
    )

@login_required
def subscription_demo(request):
    subscription_holder = request.user
    subscription = subscription_holder.active_stripe_subscription
    wrapped_subscription = SubscriptionWrapper(subscription) if subscription else None
    return render(
        request,
        "subscriptions/demo.html",
        {
            "active_tab": "subscription_demo",
            "subscription": wrapped_subscription,
            "subscription_urls": get_subscription_urls(subscription_holder),
            "page_title": _("Subscription Demo"),
        },
    )


@login_required
@active_subscription_required
def subscription_gated_page(request):
    return render(request, "subscriptions/subscription_gated_page.html")


@login_required
@active_subscription_required
def metered_billing_demo(request):
    subscription_holder = request.user
    if request.method == "POST":
        form = UsageRecordForm(subscription_holder, request.POST)
        if form.is_valid():
            usage_data = form.save()
            messages.info(request, _("Successfully recorded {} units for metered billing.").format(usage_data.quantity))
            return HttpResponseRedirect(reverse("subscriptions:subscription_demo"))
    else:
        form = UsageRecordForm(subscription_holder)

    if not form.is_usable():
        messages.info(
            request,
            _(
                "It looks like you don't have any metered subscriptions set up. "
                "Sign up for a subscription with metered usage to use this UI."
            ),
        )
    return render(
        request,
        "subscriptions/metered_billing_demo.html",
        {
            "subscription": subscription_holder.active_stripe_subscription,
            "form": form,
        },
    )


@csrf_exempt
def stripe_webhook(request):
    payload = request.body
    sig_header = request.META["HTTP_STRIPE_SIGNATURE"]
    endpoint_secret = settings.DJSTRIPE_WEBHOOK_SECRET

    try:
        event = stripe.Webhook.construct_event(payload, sig_header, endpoint_secret)
    except ValueError as e:
        log.error(f"Webhook error: {e}")
        return HttpResponse(status=400)
    except stripe.error.SignatureVerificationError as e:
        log.error(f"Webhook signature verification error: {e}")
        return HttpResponse(status=400)

    # Check if this is a subscription event
    if event["type"] == "checkout.session.completed":
        session = event["data"]["object"]
        # Only handle subscription checkouts
        if session.get("mode") == "subscription":
            try:
                handle_checkout_session(session)
            except Exception as e:
                log.error(f"Error handling checkout session: {e}")
                return HttpResponse(status=500)
        else:
            # Use the correct namespace:name pattern for the redirect
            return HttpResponseRedirect(reverse('ecommerce:ecommerce_webhook'))
    else:
        log.warning(f"Unhandled event type: {event['type']}")

    return JsonResponse({"status": "success"})


def handle_checkout_session(session):
    customer_email = session["customer_details"]["email"]
    try:
        user = CustomUser.objects.get(email=customer_email)
    except CustomUser.DoesNotExist:
        # Handle the case where the user does not exist
        log.error(f"User with email {customer_email} does not exist.")
        return

    stripe_subscription_id = session["subscription"]

    # Sync the subscription from Stripe to your local database if it does not exist
    try:
        subscription = Subscription.objects.get(id=stripe_subscription_id)
    except Subscription.DoesNotExist:
        log.info(f"Subscription with ID {stripe_subscription_id} does not exist locally. Syncing with Stripe...")
        try:
            stripe_subscription = stripe.Subscription.retrieve(stripe_subscription_id)
            subscription = Subscription.sync_from_stripe_data(stripe_subscription)
        except stripe.error.StripeError as e:
            log.error(f"Failed to sync subscription from Stripe: {e}")
            return
        log.info(f"Subscription '{subscription}' retrieved successfully")

    # Update the user's subscription details
    user.subscription = subscription
    user.customer = subscription.customer
    user.billing_details_last_changed = timezone.now()
    user.last_synced_with_stripe = timezone.now()
    user.save()

    log.info(f"Updated subscription for user {user.email}")


@csrf_exempt
@login_required
def remove_user_subscription(
    request, admin_condition: bool = True, additional_condition: callable = lambda a, b: (False, None)
):
    if request.method == "POST" and admin_condition:
        email = request.POST.get("email")
        user = get_object_or_404(CustomUser, email=email)
        if not user:
            log.error(f"User with email '{email}' not found")
            return JsonResponse({"status": "error", "message": f"User with email '{email}' not found"}, status=400)

        is_return, what_return = additional_condition(request, user)
        if is_return:
            return what_return

        if user.subscription or request.POST.get("clear"):
            user.remove_subscription()
            log.info(f"Subscription from user '{email}' removed successfully")
            return JsonResponse(
                {"status": "success", "message": f"Subscription from user '{email}' removed successfully"}, status=200
            )
        else:
            log.error(f"No active subscription found for user '{email}'")
            return JsonResponse(
                {"status": "error", "message": f"No active subscription found for user '{email}'"}, status=400
            )

    if not admin_condition:
        user_title = request.user.email
        email = request.POST.get("email")
        log.error(f"User '{user_title}' tried to remove subscription from user '{email}'")
        return JsonResponse(
            {
                "status": "error",
                "message": f"User '{user_title}' have no rights to remove subscription for user '{email}'",
            },
            status=400,
        )

    return JsonResponse({"status": "error", "message": f"Invalid request method: {request.method}"}, status=405)


@csrf_exempt
@login_required
def unsubscribe(request):

    def _additional_condition(request, user) -> tuple[bool, object]:
        if user != request.user:
            log.error("Impossible to remove custom user's subscription without it's approvement. Login to this user.")
            return True, JsonResponse(
                {"status": "error", "message": "Can't remove subscription without logging into this user"}, status=400
            )
        return False, None

    return remove_user_subscription(request, True, _additional_condition)


@csrf_exempt
@login_required
def unsubscribe_admin(request):
    return remove_user_subscription(request, request.user.is_superuser)
