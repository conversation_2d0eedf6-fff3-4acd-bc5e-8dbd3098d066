import logging
import dataclasses
from typing import List, Optional
from django.utils.translation import gettext as _
from django.utils import timezone
from datetime import timedelta
from apps.subscriptions.exceptions import FeatureGateError, NoSubscriptionFoundError, PlanNotSupportedError
from apps.subscriptions.metadata import get_product_with_metadata
from apps.subscriptions.models import SubscriptionModelBase, UserUsage
from apps.users.models import CustomUser


logger = logging.getLogger(__file__)
logger.setLevel(logging.DEBUG)
@dataclasses.dataclass
class FeatureGateCheckResult:
    passed: bool
    message: Optional[str] = None

def get_feature_gate_check(
    subscription_holder: SubscriptionModelBase, 
    limit_to_plans: Optional[List[str]] = None,
    rate_limit: int = None
) -> FeatureGateCheckResult:
    try:
        proceed = feature_gate_check(subscription_holder, limit_to_plans, rate_limit)
        return FeatureGateCheckResult(passed=proceed)
    except FeatureGateError as e:
        return FeatureGateCheckResult(passed=False, message=str(e))

def feature_gate_check(user, limit_to_plans: Optional[List[str]] = None, rate_limit: int = None) -> bool:
    logger.error("Starting feature_gate_check for user: %s", user)
    
    # Get or create the user's usage record
    usage, created = UserUsage.objects.get_or_create(
        user=user,
        defaults={'homes_added': 1}  # Start with 1 home (the sample)
    )
    
    # If the usage record was created with a null customer, update it with the user's customer
    if created:
        logger.error("UserUsage record created with user: %s and customer: %s", usage.user, usage.customer)
    else:
        logger.error("UserUsage record already exists with user: %s and customer: %s", usage.user, usage.customer)
    
    if user.customer and not usage.customer:
        usage.customer = user.customer
        usage.save()
        logger.error("Updated UserUsage record with customer: %s", usage.customer)

    logger.error("UserUsage record: %s, created: %s", usage, created)

    # If no rate limit is specified, allow the action
    if rate_limit is None:
        logger.error("No rate limit specified, allowing action.")
        usage.increment_homes()
        return True

    # Check if the user has reached the rate limit
    if usage.homes_added >= rate_limit:
        logger.error("User has reached the rate limit: %d", rate_limit)
        raise FeatureGateError(_("You've reached your usage limit."))

    # If there's an active subscription, check the plan
    if user.has_active_subscription():
        subscription = user.active_stripe_subscription
        logger.error("Active subscription: %s", subscription)
        if subscription and limit_to_plans:
            plan_check_passed = False
            for item in subscription.items.select_related("price__product"):
                product_metadata = get_product_with_metadata(item.price.product).metadata
                logger.error("Checking plan: %s against limit_to_plans: %s", product_metadata.slug, limit_to_plans)
                if product_metadata.slug in limit_to_plans:
                    plan_check_passed = True
                    break
            if not plan_check_passed:
                logger.error("Plan not supported. limit_to_plans: %s, subscription items: %s", limit_to_plans, [item.price.product.id for item in subscription.items.select_related("price__product")])
                raise PlanNotSupportedError(_("Your current plan does not support that."))

    # If all checks pass, increment the count and return True
    logger.error("All checks passed, incrementing homes_added.")
    usage.increment_homes()
    return True
