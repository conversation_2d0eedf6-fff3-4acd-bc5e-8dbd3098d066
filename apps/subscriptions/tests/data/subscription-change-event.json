{"object": {"application_fee_percent": null, "billing_cycle_anchor": 1594726406, "billing_thresholds": null, "cancel_at": null, "cancel_at_period_end": false, "canceled_at": null, "collection_method": "charge_automatically", "created": **********, "current_period_end": 1597404806, "current_period_start": 1594726406, "customer": "cus_HYmt7k4zw34Q5n", "days_until_due": null, "default_payment_method": null, "default_source": null, "default_tax_rates": [], "discount": null, "ended_at": null, "id": "sub_HYmtU2lb7NXDFZ", "items": {"data": [{"billing_thresholds": null, "created": **********, "id": "si_HYmten0CrQAe10", "metadata": {}, "object": "subscription_item", "plan": {"active": true, "aggregate_usage": null, "amount": 900, "amount_decimal": "900", "billing_scheme": "per_unit", "created": **********, "currency": "usd", "id": "plan_GqvTUw8QwIbChu", "interval": "month", "interval_count": 1, "livemode": false, "metadata": {}, "nickname": "Monthly", "object": "plan", "product": "prod_GqvS3fqDhG6SmG", "tiers": null, "tiers_mode": null, "transform_usage": null, "trial_period_days": null, "usage_type": "licensed"}, "price": {"active": true, "billing_scheme": "per_unit", "created": **********, "currency": "usd", "id": "plan_GqvTUw8QwIbChu", "livemode": false, "lookup_key": null, "metadata": {}, "nickname": "Monthly", "object": "price", "product": "prod_GqvS3fqDhG6SmG", "recurring": {"aggregate_usage": null, "interval": "month", "interval_count": 1, "trial_period_days": null, "usage_type": "licensed"}, "tiers_mode": null, "transform_quantity": null, "type": "recurring", "unit_amount": 900, "unit_amount_decimal": "900"}, "quantity": 1, "subscription": "sub_HYmtU2lb7NXDFZ", "tax_rates": []}], "has_more": false, "object": "list", "total_count": 1, "url": "/v1/subscription_items?subscription=sub_HYmtU2lb7NXDFZ"}, "latest_invoice": "in_1H4mO2ACsyNNXfUPjXiJQIPj", "livemode": false, "metadata": {}, "next_pending_invoice_item_invoice": null, "object": "subscription", "pause_collection": null, "pending_invoice_item_interval": null, "pending_setup_intent": null, "pending_update": null, "plan": {"active": true, "aggregate_usage": null, "amount": 900, "amount_decimal": "900", "billing_scheme": "per_unit", "created": **********, "currency": "usd", "id": "plan_GqvTUw8QwIbChu", "interval": "month", "interval_count": 1, "livemode": false, "metadata": {}, "nickname": "Monthly", "object": "plan", "product": "prod_GqvS3fqDhG6SmG", "tiers": null, "tiers_mode": null, "transform_usage": null, "trial_period_days": null, "usage_type": "licensed"}, "quantity": 1, "schedule": null, "start_date": **********, "status": "active", "tax_percent": null, "transfer_data": null, "trial_end": null, "trial_start": null}, "previous_attributes": {"items": {"data": [{"billing_thresholds": null, "created": **********, "id": "si_HYmten0CrQAe10", "metadata": {}, "object": "subscription_item", "plan": {"active": true, "aggregate_usage": null, "amount": 3000, "amount_decimal": "3000", "billing_scheme": "per_unit", "created": **********, "currency": "usd", "id": "plan_GqvV4aKw0sh0Za", "interval": "month", "interval_count": 1, "livemode": false, "metadata": {}, "nickname": "Monthly", "object": "plan", "product": "prod_GqvUIo03rqNM4C", "tiers": null, "tiers_mode": null, "transform_usage": null, "trial_period_days": null, "usage_type": "licensed"}, "price": {"active": true, "billing_scheme": "per_unit", "created": **********, "currency": "usd", "id": "plan_GqvV4aKw0sh0Za", "livemode": false, "lookup_key": null, "metadata": {}, "nickname": "Monthly", "object": "price", "product": "prod_GqvUIo03rqNM4C", "recurring": {"aggregate_usage": null, "interval": "month", "interval_count": 1, "trial_period_days": null, "usage_type": "licensed"}, "tiers_mode": null, "transform_quantity": null, "type": "recurring", "unit_amount": 3000, "unit_amount_decimal": "3000"}, "quantity": 1, "subscription": "sub_HYmtU2lb7NXDFZ", "tax_rates": []}]}, "plan": {"amount": 3000, "amount_decimal": "3000", "created": **********, "id": "plan_GqvV4aKw0sh0Za", "product": "prod_GqvUIo03rqNM4C"}}}