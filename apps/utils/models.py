from django.db import models
from enum import Enum
from enumfields import EnumField


class BaseModel(models.Model):
    """
    Base model that includes default created / updated timestamps.
    """
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True

class BaseImage(BaseModel):
    """Base model for all image-related models."""
    image_url = models.URLField(max_length=256, default="")
    width = models.IntegerField(null=True, blank=True)
    height = models.IntegerField(null=True, blank=True)

    class Meta:
        abstract = True

class BaseImageFeature(BaseModel):
    """Base model for all image feature models."""
    description = models.TextField(max_length=1024, default="")
    choice = models.CharField(max_length=255, default="average")

    class Meta:
        abstract = True

class BaseRoomSummary(BaseModel):
    """Base model for all room summary models."""
    room_type = models.CharField(max_length=50, default="")
    summary = models.TextField(max_length=1024, default="")
    values = models.JSONField(default=list)

    class Meta:
        abstract = True

class BaseHomeQuality(BaseModel):
    """Base model for all home quality models."""
    score = models.IntegerField(default=0, blank=True)
    condition = models.CharField(max_length=50, default="")
    description = models.TextField(max_length=1024, default="")

    class Meta:
        abstract = True

class BaseStatusModel(BaseModel):
    """Base model for status tracking."""
    upload_status = models.CharField(max_length=50, default='Pending')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


    class Meta:
        abstract = True

    def update_status(self, status):
        """Update status, ensuring it fits within the field's max_length."""
        # Get the max_length from the field definition
        max_length = self._meta.get_field('upload_status').max_length
        # Truncate the status if it's too long
        if len(status) > max_length:
            status = status[:max_length]
        self.upload_status = status
        self.save(update_fields=['upload_status', 'updated_at'])
