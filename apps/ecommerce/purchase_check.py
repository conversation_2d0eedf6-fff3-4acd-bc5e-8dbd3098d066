import logging
import dataclasses
from typing import Optional, List

from django.utils.translation import gettext as _
from apps.subscriptions.models import UserUsage
from apps.ecommerce.models import ProductPurchase

log = logging.getLogger("homescore.ecommerce.purchase_check")

class PurchaseError(Exception):
    pass

class NoPurchaseFoundError(PurchaseError):
    pass

class PurchaseLimitExceededError(PurchaseError):
    pass

@dataclasses.dataclass
class PurchaseCheckResult:
    passed: bool
    message: Optional[str] = None

def get_purchase_check(
    user,
    required_product_ids: Optional[List[str]] = None,
    rate_limit: Optional[int] = None
) -> PurchaseCheckResult:
    try:
        proceed = purchase_check(user, required_product_ids, rate_limit)
        return PurchaseCheckResult(passed=proceed)
    except PurchaseError as e:
        return PurchaseCheckResult(passed=False, message=str(e))

# Define purchase limits
PURCHASE_LIMITS = {
    "no_purchase": 2,  # 1 sample + 1 free
    "+5 properties": 7,  # Base 2 + 5 additional
    "Launch Offer": None  # Unlimited
}

def get_product_limit(product) -> Optional[int]:
    """Get the homes limit for a given product"""
    # First try to get from metadata
    if product.metadata and product.metadata.get('homes_allowed'):
        try:
            return int(product.metadata.get('homes_allowed'))
        except (ValueError, TypeError):
            log.warning("Failed to parse homes_allowed metadata for product %s", product.id)
            pass
    
    # Fall back to predefined limits based on product name
    if not product.name:
        log.warning("Product %s has no name, defaulting to base limit", product.id)
        return PURCHASE_LIMITS["no_purchase"]

    for limit_name, limit in PURCHASE_LIMITS.items():
        if limit_name.lower() in product.name.lower():
            return limit
    
    log.info("No limit found for product %s, using base limit", product.id)
    return PURCHASE_LIMITS["no_purchase"]  # Default to base allowance if no match

def _is_multiple_purchase_allowed(product):
    # Check if multiple purchases are allowed for the product
    return product.metadata.get('allow_multiple_purchases', '').lower() == 'true'

def _calculate_additional_limit(product_limit, base_limit):
    # Calculate the additional limit provided by the product over the base limit
    return max(0, product_limit - base_limit)

def purchase_check(user, required_product_ids: Optional[List[str]] = None, rate_limit: Optional[int] = None) -> bool:
    log.info("Starting purchase check for user: %s", user)
    
    # Get or create the user's usage record
    usage, created = UserUsage.objects.get_or_create(
        user=user,
        defaults={'homes_added': 1}  # Start with 1 home (the sample)
    )
    log.info("UserUsage record: %s, created: %s", usage, created)

    # Get all active purchases for the user
    active_purchases = ProductPurchase.objects.filter(
        user=user,
        is_active=True
    ).select_related('stripe_product')

    if required_product_ids and not active_purchases.filter(stripe_product__id__in=required_product_ids).exists():
        raise NoPurchaseFoundError(_("Required product purchase not found."))

    # Calculate total allowed homes
    total_allowed_homes = PURCHASE_LIMITS["no_purchase"]  # Start with base allowance
    
    for purchase in active_purchases:
        limit = get_product_limit(purchase.stripe_product)
        if limit is None:  # Unlimited access
            log.info("User has unlimited access from product: %s", purchase.stripe_product.name)
            return True
        
        if _is_multiple_purchase_allowed(purchase.stripe_product):
            # Add additional limit for products that allow multiple purchases
            total_allowed_homes += _calculate_additional_limit(limit, PURCHASE_LIMITS["no_purchase"])
        else:
            # Use the maximum limit for products that do not allow multiple purchases
            total_allowed_homes = max(total_allowed_homes, limit)

    # If rate limit is specified, use it instead
    if rate_limit is not None:
        total_allowed_homes = rate_limit

    # Check if user has exceeded their limit
    # We add 1 to homes_added to account for the new home being added
    if (usage.homes_added + 1) > total_allowed_homes:
        raise PurchaseLimitExceededError(
            _("Adding this home would exceed your limit of {} homes. Purchase additional capacity to add more homes.").format(
                total_allowed_homes
            )
        )

    # If all checks pass, return True without incrementing
    log.info("Purchase check passed. Total allowed: %d, Current usage: %d", 
                total_allowed_homes, usage.homes_added)
    return True  # All checks passed

def get_user_purchase_limits(user) -> dict:
    """Get the user's current usage limits based on their purchases"""
    active_purchases = ProductPurchase.objects.filter(
        user=user,
        is_active=True
    ).select_related('stripe_product')
    
    base_allowance = PURCHASE_LIMITS["no_purchase"]
    total_allowed = base_allowance
    
    # Get current usage
    usage = UserUsage.objects.get_or_create(user=user, defaults={'homes_added': 1})[0]
    
    # Track additional properties from multiple purchases
    additional_properties = 0
    
    # Check purchases for limits
    for purchase in active_purchases:
        limit = get_product_limit(purchase.stripe_product)
        if limit is None:  # Unlimited access
            return {
                "total_allowed": "Unlimited",
                "homes_added": usage.homes_added,
                "remaining": "Unlimited",
                "base_allowance": base_allowance,
                "additional_purchased": "Unlimited"
            }
        
        if _is_multiple_purchase_allowed(purchase.stripe_product):
            # Add additional limit for products that allow multiple purchases
            additional_properties += _calculate_additional_limit(limit, base_allowance)
        else:
            # Use the maximum limit for products that do not allow multiple purchases
            total_allowed = max(total_allowed, limit)

    # Add the additional properties from multiple purchases to the total
    total_allowed += additional_properties
    
    return {
        "total_allowed": total_allowed,
        "homes_added": usage.homes_added,
        "remaining": max(0, total_allowed - usage.homes_added),
        "base_allowance": base_allowance,
        "additional_purchased": total_allowed - base_allowance
    }