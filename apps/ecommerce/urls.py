from django.urls import path

from . import views

app_name = "ecommerce"

urlpatterns = [
    
    # Basic url paths for the displaying pricing table, customer products, and the checkout process
    path("", views.ecommerce_store, name="ecommerce_store"),
    path("webhook/", views.ecommerce_webhook, name="ecommerce_webhook"),
    path("one-time-purchase/", views.store_pricing, name="show_pricing"),
    path("purchases/", views.purchases_html, name="purchases_html"),
    path("purchases/json/", views.purchases_json, name="purchases_json"),
    # path("create-checkout-session/", views.CreateCheckoutSession.as_view(), name="create_checkout_session"),
    # path("checkout-success/", views.checkout_success, name="checkout_success"),

]
