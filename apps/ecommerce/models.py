from django.db import models, transaction
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from djstripe.models import Product, Charge

# Model for product purchases with Stripe integration
class ProductPurchase(models.Model):
    """
    Concrete model for product purchases with Stripe integration.
    """
    user = models.ForeignKey(
        "users.CustomUser",
        on_delete=models.CASCADE,
        related_name="product_purchases"
    )
    stripe_product = models.ForeignKey(
        Product,
        on_delete=models.PROTECT,
        help_text=_("Reference to Stripe product")
    )
    charge = models.ForeignKey(
        Charge,
        on_delete=models.SET_NULL,
        null=True,
        help_text=_("The associated Stripe Charge object")
    )
    
    # Purchase specific fields
    purchased_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        app_label = "ecommerce"
        db_table = "product_purchases"
        ordering = ["-purchased_at"]
        # Removed unique_together constraint to allow multiple purchases

    def __str__(self):
        return f"Purchase by {self.user} - {self.stripe_product}"

    # Get product details
    def get_product_details(self):
        return {
            "name": self.stripe_product.name,
            "description": self.stripe_product.description,
            "metadata": self.stripe_product.metadata
        }

    # Override save method to update user's billing details
    def save(self, *args, **kwargs):
        with transaction.atomic():
            self.user.billing_details_last_changed = timezone.now()
            self.user.save(update_fields=["billing_details_last_changed"])
            super().save(*args, **kwargs)

    # Property to get the amount of the charge
    @property
    def amount(self):
        return self.charge.amount if self.charge else None

    # Property to check if the charge is paid
    @property
    def is_paid(self):
        return bool(self.charge and self.charge.paid)
