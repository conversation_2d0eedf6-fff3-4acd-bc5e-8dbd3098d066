# Generated by Django 4.2.13 on 2024-11-07 07:25

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("djstripe", "0012_2_8"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ProductPurchase",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("purchased_at", models.DateTimeField(auto_now_add=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "charge",
                    models.ForeignKey(
                        help_text="The associated Stripe Charge object",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="djstripe.charge",
                    ),
                ),
                (
                    "stripe_product",
                    models.ForeignKey(
                        help_text="Reference to Stripe product",
                        on_delete=django.db.models.deletion.PROTECT,
                        to="djstripe.product",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="product_purchases",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "product_purchases",
                "ordering": ["-purchased_at"],
                "unique_together": {("user", "stripe_product")},
            },
        ),
    ]
