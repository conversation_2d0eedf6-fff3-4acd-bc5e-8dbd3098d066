import logging
from typing import Optional

from django.conf import settings
from djstripe import webhooks as djstripe_hooks
from djstripe.models import Customer, Product
from stripe.error import StripeError

from apps.users.models import CustomUser
from apps.ecommerce.models import ProductPurchase
from apps.utils.billing import get_stripe_module

log = logging.getLogger("homescore.ecommerce")
stripe = get_stripe_module()

def create_purchase_record(session: dict, product: Product, user: CustomUser) -> Optional[ProductPurchase]:
    """Helper function to create purchase record"""
    try:
        purchase = ProductPurchase.objects.create(
            user=user,
            stripe_product=product,
            is_active=True
        )
        log.info(f"Created purchase record for user {user.email} - Product: {product.name}")
        return purchase
    except Exception as e:
        log.error(f"Failed to create purchase record: {str(e)}")
        return None

@djstripe_hooks.handler("checkout.session.completed")
def checkout_session_completed(event, **kwargs):
    """
    Handle completed checkout sessions for one-time purchases.
    """
    session = event.data["object"]
    
    if session["metadata"].get("source") != "ecommerce":
        log.debug("Ignoring non-ecommerce checkout session")
        return
        
    try:
        # Retrieve full session data with line items
        session = stripe.checkout.Session.retrieve(
            session["id"],
            expand=["line_items", "line_items.data.price.product"]
        )

        # Get user from session
        customer_email = session.customer_details.email
        try:
            user = CustomUser.objects.get(email=customer_email)
        except CustomUser.DoesNotExist:
            log.error(f"No user found for email: {customer_email}")
            return

        # Handle customer creation/retrieval
        if not user.customer:
            stripe_customer = session.customer or stripe.Customer.create(email=user.email)
            customer, _ = Customer.objects.get_or_create(id=stripe_customer.id)
            user.customer = customer
            user.save()

        # Create purchase records for all products in session
        for line_item in session.line_items.data:
            stripe_product_id = line_item.price.product.id
            product = Product.objects.filter(id=stripe_product_id).first()
            
            if product:
                create_purchase_record(session, product, user)
            else:
                log.error(f"Product not found: {stripe_product_id}")

        log.info(f"Successfully processed checkout session {session.id} for {customer_email}")

    except StripeError as e:
        log.error(f"Stripe error processing checkout: {str(e)}")
    except Exception as e:
        log.error(f"Error processing checkout session: {str(e)}")

@djstripe_hooks.handler("charge.refunded")
def handle_refund(event, **kwargs):
    """Handle refund events"""
    charge_data = event.data["object"]
    try:
        purchase = ProductPurchase.objects.get(charge__id=charge_data["id"])
        purchase.is_active = False
        purchase.save()
        log.info(f"Marked purchase {purchase.id} as inactive due to refund")
    except ProductPurchase.DoesNotExist:
        log.error(f"No purchase found for refunded charge {charge_data['id']}")