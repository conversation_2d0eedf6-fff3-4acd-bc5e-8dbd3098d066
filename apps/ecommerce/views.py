import json
import logging
from typing import Optional, Dict, Any

from django.conf import settings
from django.db import transaction
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from djstripe.settings import djstripe_settings
from django.views.decorators.http import require_GET, require_POST
from django.views.decorators.csrf import csrf_exempt
from djstripe.models import Customer, Product, Charge

from apps.users.models import CustomUser
from apps.utils.billing import get_stripe_module
from .models import ProductPurchase
from .purchase_check import get_user_purchase_limits

# Set up logging for the ecommerce module
log = logging.getLogger("homescore.ecommerce")

# Initialize the Stripe module
stripe = get_stripe_module()


# View for the ecommerce store
@login_required
def ecommerce_store(request):
    # Attempt to get the customer's Stripe customer object
    customer = request.user.customer
    if not customer:
        # If no customer exists, show the pricing page
        return store_pricing(request)
    else:
        # If customer exists, display their purchases
        return purchases_html(request)


# Extract product ID from session data
def extract_product_id_from_session(session: Dict[str, Any]) -> Optional[str]:
    # Check if product ID is in session metadata
    if session.get("metadata", {}).get("product_id"):
        return session["metadata"]["product_id"]
    
    try:
        # Try to get product ID from line items
        return session["line_items"]["data"][0]["price"]["product"]
    except (KeyError, IndexError):
        # Return None if product ID is not found
        return None

# Extract customer email from session or charge data
def extract_customer_email(data: Dict[str, Any]) -> Optional[str]:
    if "customer_details" in data:
        # Get email from customer_details in session data
        return data["customer_details"].get("email")
    elif "billing_details" in data:
        # Get email from billing_details in charge data
        return data["billing_details"].get("email")
    return None

# Handle checkout session completed event
def handle_checkout_completed(event: Dict[str, Any]) -> HttpResponse:
    # Get the session object from the event data
    session = event["data"]["object"]
    try:
        # Retrieve the session with expanded fields
        session = stripe.checkout.Session.retrieve(
            session["id"],
            expand=["line_items", "line_items.data.price.product", "payment_intent"]
        )
        
        try:
            # Get the user based on the email from customer details
            user = CustomUser.objects.get(email=session.customer_details.email)
        except CustomUser.DoesNotExist:
            # Log error if user is not found
            log.error(f"No user found with email: {session.customer_details.email}")
            return HttpResponse(status=400)
    
        # Create or retrieve the Stripe customer
        if not user.customer:
            if session.customer:
                # Use existing Stripe customer ID from session
                customer, _ = Customer.objects.get_or_create(id=session.customer)
            else:
                # Create a new Stripe customer
                stripe_customer = stripe.Customer.create(email=user.email)
                customer, _ = Customer.objects.get_or_create(id=stripe_customer.id)
            
            # Associate the customer with the user
            with transaction.atomic():
                user.customer = customer
                user.save()
        log.info(f"The Current User: {user} has customer {user.customer.id}")
            
        # Get the product ID from the session line items
        stripe_product_id = session.line_items.data[0].price.product.id
        log.info(f"Looking for Stripe product ID: {stripe_product_id}")
        
        # Retrieve and sync the product from Stripe
        stripe_product = stripe.Product.retrieve(stripe_product_id)
        product = Product.sync_from_stripe_data(stripe_product)
        log.info(f"Synced product from Stripe: {product.id}, metadata: {product.metadata}")
        
        # Check if multiple purchases are allowed for this product
        allow_multiple = product.metadata.get('allow_multiple_purchases') == 'true'
        log.info(f"Allow Multiple Purchases: {allow_multiple}")
        
        # Check if the user already has an active purchase of this product
        with transaction.atomic(): 
            # Lock the rows to prevent race conditions 
            existing_purchase = ProductPurchase.objects.select_for_update().filter( 
                user=user, 
            stripe_product=product, 
            is_active=True 
            ).first() 

            if existing_purchase and not allow_multiple: 
                # If multiple purchases are not allowed, log and return 
                log.info(f"Product {product.id} already purchased and doesn't allow multiple purchases") 
                return HttpResponse(status=200) # Silently succeed to avoid Stripe retries
        
        # Create a new product purchase for the user
        with transaction.atomic():
            purchase = ProductPurchase.objects.create(
                user=user,
                stripe_product=product
            )
        log.info(f"Created new purchase {purchase.id} for product {product.id}")

        return HttpResponse(status=200)
        
    except Exception as e:
        # Log errors during processing
        log.error(f"Error processing checkout: {str(e)}", exc_info=True)
        return HttpResponse(status=400)

# Handle charge succeeded event
def handle_charge_succeeded(event: Dict[str, Any]) -> HttpResponse:
    # Get the charge object from the event data
    charge = event["data"]["object"]
    # Extract the customer email from the charge data
    customer_email = extract_customer_email(charge)
    
    if not customer_email:
        # Log error if customer email is missing
        log.error("Missing customer email in charge data")
        return HttpResponse(status=400)
        
    try:
        # Log the successful charge
        log.info(f"Successful charge for {customer_email}")
        return HttpResponse(status=200)
    except Exception as e:
        # Log error if processing fails
        log.error(f"Error processing charge: {str(e)}")
        return HttpResponse(status=400)

# Handle charge refunded event
def handle_charge_refunded(event: Dict[str, Any]) -> HttpResponse:
    # Get the charge object from the event data
    charge = event["data"]["object"]
    # Extract the customer email from the charge data
    customer_email = extract_customer_email(charge)
    
    if not customer_email:
        # Log error if customer email is missing
        log.error("Missing customer email in refund data")
        return HttpResponse(status=400)
        
    try:
        # Log the processed refund
        log.info(f"Processed refund for {customer_email}")
        return HttpResponse(status=200)
    except Exception as e:
        # Log error if processing fails
        log.error(f"Error processing refund: {str(e)}")
        return HttpResponse(status=400)

# Mapping of event types to handler functions
EVENT_HANDLERS = {
    "checkout.session.completed": handle_checkout_completed,
    "charge.succeeded": handle_charge_succeeded,
    "charge.refunded": handle_charge_refunded,
}

# Webhook endpoint to handle Stripe events
@csrf_exempt
@require_POST
def ecommerce_webhook(request):
    # Retrieve the payload and signature header from the request
    payload = request.body
    sig_header = request.META.get("HTTP_STRIPE_SIGNATURE")
    
    try:
        # Check if the webhook secret is configured
        if not settings.DJSTRIPE_WEBHOOK_SECRET:
            log.error("DJSTRIPE_WEBHOOK_SECRET is not configured!")
            return HttpResponse(status=500)
            
        # Construct the event using Stripe's webhook utility
        event = stripe.Webhook.construct_event(
            payload,
            sig_header,
            settings.DJSTRIPE_WEBHOOK_SECRET
        )
        
        log.info("Webhook event constructed successfully: %s", event.type)
        
        # Get the appropriate handler for the event type
        handler = EVENT_HANDLERS.get(event["type"])
        if handler:
            # Call the handler function
            return handler(event)
        
        # Log warning if event type is unhandled
        log.warning(f"Unhandled event type: {event['type']}")
        return HttpResponse(status=200)
        
    except stripe.error.SignatureVerificationError as e:
        # Log signature verification errors
        log.error("⚠️ Webhook signature verification failed: %s", str(e))
        return HttpResponse(status=400)
    except json.JSONDecodeError as e:
        # Log JSON decode errors
        log.error("⚠️ Invalid payload: %s", str(e))
        return HttpResponse(status=400)
    except Exception as e:
        # Log any other exceptions
        log.error("⚠️ Webhook error: %s", str(e), exc_info=True)
        return HttpResponse(status=500)

# View to show one-time purchase pricing table
@login_required
def store_pricing(request):
    # Prepare the context data for rendering the pricing template
    context = {
        "active_tab": "store pricing",
        "stripe_public_key": djstripe_settings.STRIPE_PUBLIC_KEY,
        "one_time_pricing_table_id": settings.STRIPE_ONE_TIME_PRICING_TABLE_ID,
        "client_reference_id": request.user.id,
    }
    
    # Log the context data excluding the request object
    log.debug("Rendering one_time_purchase with context: %s", {
        k: v for k, v in context.items() if k != "request"
    })
    
    # Render the pricing template with the context data
    return render(request, "ecommerce/pricing.html", context)

# View to show products purchased by the user in HTML format
@login_required
def purchases_html(request):
    # Prepare the context data with the user's purchases
    context = {
        "active_tab": "My Purchases",
        "purchases": ProductPurchase.objects.filter(user=request.user).select_related("stripe_product"),
    }
    # Render the purchases template with the context data
    return render(request, "ecommerce/one_time_purchases.html", context)

# View to return user purchases as JSON
@login_required
@require_GET
def purchases_json(request):
    # Retrieve the user's purchases with related Stripe product and charge data
    purchases = ProductPurchase.objects.filter(user=request.user).select_related("stripe_product", "charge")
    
    # Prepare the purchases data for the JSON response
    purchases_data = [{
        "product_name": purchase.stripe_product.name,
        "product_description": purchase.stripe_product.description,
        "purchased_at": purchase.purchased_at.isoformat(),
        "amount": str(purchase.charge.amount) if purchase.charge else None,
        "is_active": purchase.is_active,
        "metadata": purchase.stripe_product.metadata
    } for purchase in purchases]
    
    # Get the user's purchase limits
    purchase_limits = get_user_purchase_limits(request.user)
    
    # Return the purchases and limits in a JSON response
    return JsonResponse({
        "status": "success",
        "purchases": purchases_data,
        "limits": purchase_limits
    })