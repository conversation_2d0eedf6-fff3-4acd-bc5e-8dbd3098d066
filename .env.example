SECRET_KEY='wjRDdCsrhCiUIwftWokApWkjDZnxmGtxIvmjjGvH'

DATABASE_URL='postgres://postgres:postgres@localhost:5432/homescore'  # change host to "db" for docker
REDIS_URL='redis://localhost:6379'  # change host to "redis" for docker

GOOGLE_ANALYTICS_ID=''
SENTRY_DSN=''
SENTRY_ENV='staging'

# put your stripe keys here
STRIPE_LIVE_PUBLIC_KEY="pk_live_***"
STRIPE_LIVE_SECRET_KEY="sk_live_***"
STRIPE_TEST_PUBLIC_KEY="pk_test_***"
STRIPE_TEST_SECRET_KEY="sk_test_***"
DJSTRIPE_WEBHOOK_SECRET="whsec_***"

ENABLE_DEBUG_TOOLBAR=True

OPENAI_API_KEY="sk-***"
OPENAI_MODEL="gpt-4o"

GOOGLE_ANALYTICS_ID=''
GOOGLE_API_KEY="***"
GOOGLE_OAUTH_CLIENT_ID="***.apps.googleusercontent.com"
GOOGLE_OAUTH_SECRET="***"

REACT_APP_API_URL="http://localhost:8000"