# Merging `dev` (DigitalOcean Migration) into `main` - Checklist & Findings

This document outlines the steps and critical checks required to safely merge the `dev` branch, containing the DigitalOcean (DO) migration setup, into the `main` branch. The primary goal is to integrate the DO configuration without disrupting the current production deployment on Render.

**Phase 1: Pre-Merge Analysis & Verification**

**1.1. Fetch Latest Changes:**
   - Ensure local `main` and `dev` branches are synchronized with the remote repository.
   - **Status:** Done (Ran `git fetch origin main dev`)

**1.2. Analyze Differences (`git diff origin/main..origin/dev`):**
   - Identify all files changed between `dev` and `main`.
   - **Summary of Changed Files:** Done (See diff output below)
   - **Detailed Diff Analysis (Key Files):**
     - **Added:** `.do/production.yaml`, `.do/staging.yaml`, `.github/workflows/ci.yml`, `.github/workflows/container-cleanup.yml`, `.github/workflows/deploy-do.yml`, `Dockerfile.celery`, `deploy/celery_startup_do.sh`, `deploy/debug_deployment.sh`, `deploy/docker_startup_do.sh`
     - **Modified:** `.gitignore`, `Dockerfile.dev`, `Makefile`, `homescore/settings.py`, `apps/reports/tasks.py`, `apps/reports/views.py`, `apps/web/tests/test_basic_views.py`, `tests/base_test.py`
     - **Deleted:** `.github/workflows/docker-test.yml`, `.vscode/settings.json`
     - **Full Diff Output (`--name-status`):**
       ```
       A       .do/production.yaml
       A       .do/staging.yaml
       A       .github/workflows/ci.yml
       A       .github/workflows/container-cleanup.yml
       A       .github/workflows/deploy-do.yml
       D       .github/workflows/docker-test.yml
       M       .gitignore
       D       .vscode/settings.json
       A       Dockerfile.celery
       M       Dockerfile.dev
       M       Makefile
       M       apps/reports/tasks.py
       M       apps/reports/views.py
       M       apps/web/tests/test_basic_views.py
       A       deploy/celery_startup_do.sh
       A       deploy/debug_deployment.sh
       A       deploy/docker_startup_do.sh
       M       homescore/settings.py
       M       tests/base_test.py
       ```

**1.3. Anticipate Merge Conflicts:**
   - Based on the diff, list files likely to have merge conflicts.
   - **Potential Conflict Files:**
     - `.gitignore`
     - `Makefile`
     - Potentially other Python files if modified concurrently in `main` (less likely if `main` is stable prod).
   - **High-Priority Potential Conflict Files (Risk to Render Prod):**
     - `Dockerfile.dev`: Modified in `dev` for DO (permissions, packages, optimization). Direct merge risks breaking Render's build process which relies on this file.
     - `homescore/settings.py`: Modified in `dev` with DO-specific DB options (`sslmode`) and Redis SSL logic. Direct merge risks causing misconfigurations or connection failures for the Render production environment (DB/Redis).

**1.4. Verify Workflow Triggers (`main` branch):**
   - **Goal:** Prevent accidental DO deployments from `main` post-merge.
   - **Check `.github/workflows/deploy-do.yml` (from `dev` branch):**
     - Trigger for `main` branch: **AUTOMATIC** via `workflow_run` after CI completes on `main`.
     - **ACTION REQUIRED:** Must modify `deploy-do.yml` on `dev` branch *before* merge to remove `"main"` from `workflow_run.branches`.
     - Snippet (Current Trigger in `dev`):
       ```yaml
       on:
         workflow_run:
           workflows: ["Homescore Backend CI"]
           types: [completed]
           branches: ["dev", "main"]
         workflow_dispatch:
           inputs:
             environment:
               description: "Environment to deploy to"
               type: choice
               options: [dev, prod]
               default: "dev"
       ```
   - **Check Render Deployment Workflow (if applicable):**
     - Trigger for `main` branch: No Render deployment workflow file found in `main`'s `.github/workflows/` directory. Current Render deployment likely handled via platform integration or a previously deleted file.
     - Snippet: N/A

**1.5. Verify Image Tagging (`ci.yml`):**
   - **Goal:** Confirm `main` branch pushes tag images correctly (e.g., `:main`).
   - **Check `.github/workflows/ci.yml` (from `dev` branch):**
     - Image tagging logic for `main`: Correct. Sets `tag_name=main` and tags image as `ghcr.io/homescore-ai/homescore-web:main`.
     - Relevant Snippet (Verified):
       ```yaml
       # Verified section in the current ci.yml from dev branch
       - name: Resolve environment name and tag suffix
         id: env
         run: |
           echo "sanitized_branch=$(echo ${GITHUB_REF_NAME} | sed 's/\//-/g')" >> $GITHUB_OUTPUT
           if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
             echo "env_name=prod" >> $GITHUB_OUTPUT
             echo "tag_name=main" >> $GITHUB_OUTPUT
           elif [[ "${{ github.ref }}" == "refs/heads/dev" ]]; then
             echo "env_name=dev" >> $GITHUB_OUTPUT
             echo "tag_name=dev" >> $GITHUB_OUTPUT
           # ... other branches ...
       # ...
       - name: Build and push Docker image Webapp
         uses: docker/build-push-action@v5
         with:
           # ... context, file, target ...
           push: ${{ github.event_name == 'push' && (github.ref == 'refs/heads/dev' || github.ref == 'refs/heads/main' || contains(github.ref, 'do-prod')) }}
           tags: |
             ghcr.io/homescore-ai/homescore-web:${{ steps.env.outputs.tag_name }}
             ghcr.io/homescore-ai/homescore-web:${{ github.sha }}
           # ... cache settings ...
       ```
     - **Verification Status:** Done. Logic is correct for `main` branch tagging.
     - **Note:** Minor typo found in `elif/else` blocks for feature branches (`steps.vars...` should be `steps.env...`). Build target/file (`Dockerfile.dev`, `web-release`) usage for `main` should be confirmed separately if needed.

**1.6. Verify DO Production Configuration (`.do/production.yaml`):**
   - Confirm the file exists in `dev` and contains the intended production setup.
   - **Status:** Done. File exists in `dev` and structure/references appear correct for production (uses `homescore-production` name, `homescore-prod-db` cluster, `${TAG_SUFFIX}` for image tag, relies on workflow for env vars).

**1.7. Verify GitHub Secrets & Variables (`prod` Environment):**
   - **Goal:** Ensure all necessary secrets/variables for the *DO production deployment* are defined in GitHub Actions under the `prod` environment.
   - **Required Variables/Secrets (Check against `.do/production.yaml` and `deploy-do.yml`):**
     - **Secrets:** `MY_DO_ACCESS_TOKEN`, `GHCR_USERNAME`, `GHCR_PAT`, `DATABASE_URL` (or `POSTGRES_URL`), `AWS_STORAGE_BUCKET_NAME`, `AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY`, `AWS_S3_REGION_NAME`, `AZURE_OPENAI_API_KEY`, `DJSTRIPE_WEBHOOK_SECRET`, `FR_KEY`, `GOOGLE_API_KEY`, `GOOGLE_OAUTH_CLIENT_ID`, `GOOGLE_OAUTH_SECRET`, `PINECONE_API_KEY`, `PINECONE_INFOPAY_API_KEY`, `REDIS_URL`, `SENTRY_DSN`, `STRIPE_LIVE_SECRET_KEY`, `STRIPE_TEST_SECRET_KEY`, `SUPABASE_KEY`, `POSTGRES_URL`, `CELERY_BROKER_URL`, `LOGTAIL_TOKEN`.
     - **Variables:** `PROD_POSTGRES_CLUSTER_NAME`, `PROD_REDIS_CLUSTER_NAME`, `DO_APP_ID` (Needs actual Production App ID once created/deployed). Other app-specific vars like `SENTRY_ENV=prod`, etc.
   - **Verification:** Manual check required in GitHub UI (`Settings` -> `Secrets and variables` -> `Actions` -> `Environments` -> `prod`).
   - **Status:** Done (Manual check performed by user).
   - **Notes (Diff `dev.env.list` vs `prod.env.list`):**
     ```diff
     diff dev.env.list prod.env.list
     7a8
     > GOOGLE_ANALYTICS_ID
     24a26
     > DO_APP_ID
     26a29
     > FRONT_END_URL
     33a37,38
     > PROD_POSTGRES_CLUSTER_NAME
     > PROD_REDIS_CLUSTER_NAME
     38,39d42
     < STAGING_POSTGRES_CLUSTER_NAME
     < STAGING_REDIS_CLUSTER_NAME
     ```

**1.8. Render Configuration (`render.yaml`):**
   - Status in `dev` vs `main`: Present and identical in both branches.
   - Plan: Keep for now. Merging `dev` will not affect it. Can be removed post-DO-migration completion.

**Phase 2: Branching Strategy & Safe Integration**

*   **Goal:** Safely integrate `dev` changes into `main` without disrupting the live Render production environment, primarily by isolating changes to `Dockerfile.dev` and `settings.py`.
*   **Strategy:** Use intermediate feature branches off `dev` to manage the merge and resolve critical conflicts before merging the final result into `main`. **Do not merge these feature branches back into `dev` during this process.**
*   **Monitoring:** Keep an eye on GitHub Actions CI runs after pushing commits to ensure workflows behave as expected on these branches.

*   **Step 2.1: Modify `deploy-do.yml` Trigger on `dev`:**
    *   **Goal:** Prevent accidental DO deployments from `main` post-merge.
    *   **Action:** `git checkout dev && git pull origin dev`. Edit `.github/workflows/deploy-do.yml` to remove `"main"` from `workflow_run.branches`. Commit (`git commit -am "ci: Prevent deploy-do trigger on main branch"`) and push (`git push origin dev`).
*   **Step 2.2: Create Base Merge Branch:**
    *   **Goal:** Create an isolated branch containing all `dev` changes (including the fixed workflow trigger).
    *   **Action:** Ensure you are on the updated `dev` branch. `git checkout -b feat/do-prod-merge-dev dev`.
*   **Step 2.3: Merge `main` into Base Branch:**
    *   **Goal:** Integrate `main`'s history into the feature branch.
    *   **Action:** `git merge main`.
    *   **Outcome:** The merge completed without conflicts. Verification (`git diff <merge-base>..main -- <files>`) showed no changes to `Dockerfile.dev` or `homescore/settings.py` on `main` since divergence. The branch now contains the `dev` versions of these files.
    *   **Note:** Steps 2.4 and 2.5 will address the necessary modifications to `Dockerfile.dev` and `homescore/settings.py` respectively to ensure Render compatibility.
    *   **Push:** `git push origin feat/do-prod-merge-dev`.
*   **Step 2.4: Create `Dockerfile.web` and Update Configurations:**
    *   **Goal:** Introduce the DO-specific Dockerfile and configure CI/DO specs to use it, segregating Render/DO builds.
    *   **Action:** Branch off `feat/do-prod-merge-dev`: `git checkout -b feat/integrate-dockerfile-web feat/do-prod-merge-dev`.
    *   **Sub-steps:**
        *   Rename `Dockerfile.dev` -> `Dockerfile.web` (`git mv Dockerfile.dev Dockerfile.web`). This preserves the DO-specific content.
        *   Restore `Dockerfile.dev` from `main` (`git checkout origin/main -- Dockerfile.dev`). This preserves the Render-compatible content.
        *   Modify `.github/workflows/ci.yml`: Update the build step for the `web` image to use `file: ./Dockerfile.web` instead of `Dockerfile.dev`. The existing tagging logic (`ghcr.io/homescore-ai/homescore-web:<tag>`) remains correct.
        *   **Note:** No changes were needed in `.do/*.yaml` specs. DO pulls the image built by CI based on the `image:` tag, not the `dockerfile_path` when an image is specified.
        *   Commit changes related to `Dockerfile.web` integration.
        *   Push: `git push origin feat/integrate-dockerfile-web`.
        *   Create PR: `feat/integrate-dockerfile-web` -> `feat/do-prod-merge-dev`.
        *   **Status:** Completed & Merged into `feat/do-prod-merge-dev`.
*   **Step 2.5: Resolve `settings.py` Conflicts (Conditional Logic):**
    *   **Goal:** Apply DO-specific settings (DB/Redis SSL) conditionally, ensuring Render compatibility.
    *   **Action:** Branch off `feat/do-prod-merge-dev`: `git checkout -b feat/resolve-settings feat/do-prod-merge-dev`.
    *   **Sub-steps:**
        *   Edit `homescore/settings.py`, resolving conflict markers.
        *   Implement conditional logic (e.g., checking `os.environ.get('DIGITALOCEAN_APP_ID')`, `DJANGO_SETTINGS_MODULE`, or another reliable indicator) to apply DO-specific DB (`sslmode`) and Redis (`ssl_cert_reqs=None`) settings. Ensure Render/local settings remain the default.
        *   Thoroughly review validation logic (in settings and startup scripts) for robustness.
        *   Commit the resolved `settings.py`.
        *   Push: `git push origin feat/resolve-settings`.
        *   Create PR: `feat/resolve-settings` -> `feat/do-prod-merge-dev`.
        *   **Status:** Completed & Merged into `feat/do-prod-merge-dev`.
*   **Note on Debugging Path:** During the integration process on `feat/do-prod-merge-dev`, several commits involving complex Redis connection settings were added in an attempt to resolve stability issues. These were later found to be unnecessary after diagnosing the root cause in the DO app spec (see section "DO App Spec Configuration Fix" below) and were subsequently reverted or excluded from the final merge branch.
*   **Step 2.6: Test Integrated Branch (`feat/do-prod-merge-dev`):**
    *   **Goal:** Verify that the combined changes work correctly on both Render and DigitalOcean staging environments before merging to `main`.
    *   **Branch:** Ensure your local `feat/do-prod-merge-dev` is up-to-date (`git checkout feat/do-prod-merge-dev && git pull origin feat/do-prod-merge-dev`).
    *   **Sub-steps:**
        *   **Confirm CI Pass & Image Push:** Verified. CI passed successfully, and images for `web` and `celery` tagged with `feat-do-prod-merge-dev` were pushed to GHCR.
        *   **Test on Render Preview (Optional but Recommended):**
            *   Deploy the `feat/do-prod-merge-dev` branch to a Render Preview Environment.
            *   **Result:** Deployed successfully via PR `[render preview] Feat: Merge DO Migration Changes for Testing`. Application started and basic functionality worked. **Note:** Preview environment connected to the **production** database and Redis instance due to default environment variable inheritance. No issues observed despite this.
        *   **Test on DigitalOcean Staging:**
            *   **Prerequisite:** Ensure the GitHub Environment Variable `IS_DIGITALOCEAN_DEPLOYMENT` is set to `true` for the `staging` environment in repository/organization settings.
            *   Trigger a deployment of the `feat/do-prod-merge-dev` branch to the DO **staging** app (e.g., manually via `workflow_dispatch` if the `deploy-do.yml` doesn't automatically trigger for this branch pattern, or by temporarily adding the branch pattern to the trigger).
            *   **Result:** Deployed successfully via `workflow_dispatch`. Application started, logged in, and obtained reports successfully. Verified console logs showed usage of `Dockerfile.web`. Confirmed DB and Redis connections were functional, implicitly testing `settings.py` logic for `IS_DIGITALOCEAN=True`.
*   **Step 2.7: Merge to `main`:**
    *   **Goal:** Integrate the fully tested changes into the `main` branch.
    *   **Action:** Create PR: `feat/do-prod-merge-dev` -> `main`.

**Phase 3: Final Integration & Deployment Prep**

*   **Step 3.1: Consolidate Final Changes:** Create a final integration branch (e.g., `release/do-prod-migration-final`) branching off a stable point in `feat/do-prod-merge-dev` *before* the unnecessary Redis commits, or by reverting those commits. Cherry-pick or merge the necessary changes (like Dockerfile split, settings resolution) into this final branch. This branch represents the clean, intended state for merging into `main`.
*   **Step 3.2: Final Test `release/do-prod-migration-final`:** Perform final checks and deploy the `release/do-prod-migration-final` branch to the DO *staging* environment for verification. Ensure all functionality works as expected and the previous stability issues (now understood to be app spec related) are resolved. **Status: Completed (Verified in DO Staging, DO Prod, and Render Preview).**
*   **Step 3.3: Merge to `main`:**
    *   Create PR: `release/do-prod-migration-final` -> `main`.
    *   Review the PR thoroughly, ensuring it only contains the intended DO migration changes and fixes.
    *   Merge the PR into `main` (using `--no-ff` if merging locally: `git checkout main && git pull origin main && git merge --no-ff release/do-prod-migration-final`).
    *   Push `main`: `git push origin main`.
*   **Step 3.4: Monitor:** Monitor CI runs on `main`. Confirm the DO deployment workflow does *not* trigger automatically. Confirm the Render deployment still succeeds.

**Phase 4: DO Production Cutover (Separate Process)**

*   This involves manually triggering the DO deployment for `main` via `workflow_dispatch`, performing the database migration (using methods from Memory `9a143274`), DNS changes, and eventually decommissioning Render.

**DO App Spec Configuration Fix**

### Problem Description
The application experienced issues with Celery worker configuration in DigitalOcean's App Platform, specifically:
- Workers were not properly picking up environment variables
- Tasks were not being routed to the correct queues
- Connection stability issues were observed

### Initial Misdiagnosis and Path
Initially, suspecting Redis configuration issues, several commits with complex connection stability settings were added directly to the `feat/do-prod-merge-dev` branch:

1. Added extensive connection stability settings in `settings.py`:
   - Socket keepalives
   - Connection retries
   - Transport options
   - Health check modifications

2. Modified `celery_startup_do.sh` with additional flags:
   - `--without-heartbeat`
   - `--without-gossip`
   - `--without-mingle`

However, these Redis-focused changes were later reverted or excluded during the creation of the `release/do-prod-migration-final` branch, as the root cause was identified in the DO app spec configuration (`envs` vs `env_vars`).

### Root Cause
The issue was in the DO app spec configuration files (`.do/staging.yaml` and `.do/production.yaml`). The environment variables were incorrectly declared under an `env_vars` block, which is not recognized by DigitalOcean's App Platform. The correct key is `envs`.

### Solution Implemented
The fix involved two key changes in both `.do/staging.yaml` and `.do/production.yaml`:

```yaml
# Before (Incorrect)
workers:
  - name: celery-default
    run_command: bash /code/deploy/celery_startup_do.sh
    env_vars:
      - key: QUEUE_NAME
        value: default
    envs: ${envs}  # Redundant and problematic

# After (Correct)
workers:
  - name: celery-default
    run_command: bash /code/deploy/celery_startup_do.sh
    envs:
      - key: QUEUE_NAME
        value: default
```

Key changes:
1. Changed `env_vars` to `envs` to match DigitalOcean's expected configuration
2. Removed redundant `envs: ${envs}` inheritance line that was causing variable scoping issues

### Observations and Learnings

1. **Unexpected Benefits**
   - Fixing the app spec configuration not only resolved the queue routing issues but also significantly reduced the frequency of Redis connection errors
   - This suggests that proper worker configuration and task distribution can improve overall connection stability

2. **Connection Stability Insights**
   - Previous Redis connection settings (keepalives, retries, etc.) were not strictly necessary once the app spec was correct
   - DigitalOcean's managed Redis has more aggressive connection timeouts than self-hosted instances
   - Proper task distribution across workers can reduce load and improve connection stability

3. **Configuration Management**
   - Always use `envs` instead of `env_vars` for environment variables in DO app spec files
   - Avoid redundant inheritance lines (`envs: ${envs}`)
   - Proper variable scoping is crucial for worker-specific configurations

### Future Considerations

1. **Configuration Management**
   - Document all environment-specific configurations
   - Maintain consistent configuration patterns
   - Regularly review and simplify configurations

2. **Deployment Process**
   - Keep rollback procedures documented
   - Maintain clear branch naming conventions
   - Document all environment-specific changes


**Relevant Memory Notes:**

*   **DO App Spec Env Vars (Memory `570e5114-6128-4139-98d0-ae57adc9f0c8`):** Env Vars are managed in GitHub Actions, not defaulted in YAML. Validation is in startup scripts (`docker_startup_do.sh`, `celery_startup_do.sh`).
*   **DO Image Reference Format (Memory `32217739-c03b-438a-a150-82801355506c`):** Image spec requires separate `registry` and `repository` fields. e.g., `registry: ghcr.io`, `repository: homescore-ai/homescore-web`. Tag comes from `TAG_SUFFIX`.
*   **DO Staging Env (Memory `7b169f2c-686f-43a8-9387-705b742cc290`):** Staging setup mirrors production (`.do/staging.yaml`) and uses specific staging resources.
*   **Database Restore (Memory `9a143274-8ded-4a6e-bcbd-d6c2b673cf60`):** Crucial to ensure the *application user* owns DB objects. Use `pg_restore --no-owner --role=<app_user>` for `.dump` or schema prep + `psql -U <app_user>` for `.sql`.
*   **DO App ID Handling (Memory `5b6b068e-6cb6-467e-9fb2-857aabf62bf4`):** `DO_APP_ID` controls create vs. update logic. Ensure the production `DO_APP_ID` is set correctly in GitHub Variables before the *first* DO production deployment.

---
*This document will be updated iteratively as checks are completed.*

---

## Render Preview Environment Findings

*   **Trigger Mechanism:** Render Preview Environments for this project appear to be configured for **manual generation** by default (`render.yaml: previews.generation: manual`). However, specific triggers seem to be enabled via Render's dashboard settings or integrations, overriding this default for certain conditions.
*   **Base Branch Requirement:** Based on observations with PR #345, a key trigger condition is that the pull request's **base branch must be `main`**. Previews may not start automatically if the PR initially targets a different branch (e.g., `dev`).
*   **Potential Re-trigger:** Changing the PR title *after* the base branch has been set to `main` might re-evaluate the trigger conditions and initiate the preview deployment if it hadn't started previously.
*   **Label Trigger:** PR #344's description mentioned `(Triggered by this PR/label)`, suggesting a specific label (e.g., `render-preview`) might also be used as a manual trigger via Render's settings, overriding the default for specific PRs. This label was missing from PR #345 initially, which likely contributed to the delay.
