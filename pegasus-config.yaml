default_context:
  _pegasus_version: 2023.5.1
  author_name: <PERSON><PERSON><PERSON>
  ci: github_actions
  css_framework: bootstrap
  css_theme: default
  database: postgres
  deploy_platform: render
  description: Unlimited answers about your home.
  domain_name: homescore.co
  email: <EMAIL>
  enable_google_login: n
  enable_twitter_login: n
  enable_google_login: n
  enable_twitter_login: n
  front_end_framework: react
  post_process: y
  project_name: Home Score
  project_slug: homescore
  stripe_payment_mode: checkout
  subscription_billing_model: per_unit
  subscription_billing_model: per_unit
  use_2fa: y
  use_api_keys: n
  use_api_keys: n
  use_cloud_media_storage: n
  use_debug_toolbar: y
  use_docker: y
  use_examples: n
  use_impersonation: y
  use_openai: n
  use_sentry: n
  use_subscriptions: y
  use_teams: n
  use_teams_example: n
  use_translations: n
  use_wagtail: n
  use_teams: n
  use_teams_example: n
  use_translations: n
  use_wagtail: n
