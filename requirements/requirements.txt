#
# This file is autogenerated by pip-compile with Python 3.11
# by the following command:
#
#    pip-compile requirements/requirements.in
#
aiohttp==3.9.5
    # via
    #   langchain
    #   langchain-community
aiosignal==1.3.1
    # via aiohttp
amqp==5.2.0
    # via kombu
annotated-types==0.7.0
    # via pydantic
anyio==4.4.0
    # via
    #   httpx
    #   openai
apify-client==1.7.0
    # via -r requirements/requirements.in
apify-shared==1.1.1
    # via apify-client
asgiref==3.8.1
    # via
    #   django
    #   django-cors-headers
attrs==23.2.0
    # via
    #   aiohttp
    #   jsonschema
    #   referencing
azure-ai-formrecognizer==3.3.3
    # via -r requirements/requirements.in
azure-common==1.1.28
    # via azure-ai-formrecognizer
azure-core==1.30.2
    # via
    #   azure-ai-formrecognizer
    #   msrest
beautifulsoup4==4.12.3
    # via django-bootstrap-v5
billiard==4.2.0
    # via celery
brotli==1.1.0
    # via whitenoise
cachetools==5.3.3
    # via google-auth
celery[redis]==5.4.0
    # via
    #   -r requirements/requirements.in
    #   django-celery-beat
    #   flower
celery-progress==0.3
    # via -r requirements/requirements.in
certifi==2024.6.2
    # via
    #   httpcore
    #   httpx
    #   msrest
    #   pinecone-client
    #   requests
    #   sentry-sdk
cffi==1.16.0
    # via cryptography
charset-normalizer==3.3.2
    # via requests
click==8.1.7
    # via
    #   celery
    #   click-didyoumean
    #   click-plugins
    #   click-repl
click-didyoumean==0.3.1
    # via celery
click-plugins==1.1.1
    # via celery
click-repl==0.3.0
    # via celery
cron-descriptor==1.4.3
    # via django-celery-beat
cryptography==42.0.7
    # via pyjwt
dataclasses-json==0.6.7
    # via langchain-community
decorator==5.1.1
    # via retry
deprecation==2.1.0
    # via postgrest
distro==1.9.0
    # via openai
dj-rest-auth==6.0.0
    # via -r requirements/requirements.in
dj-stripe==2.8.4
    # via -r requirements/requirements.in
django==4.2.13
    # via
    #   -r requirements/requirements.in
    #   dj-rest-auth
    #   dj-stripe
    #   django-allauth
    #   django-allauth-2fa
    #   django-anymail
    #   django-bootstrap-v5
    #   django-celery-beat
    #   django-cors-headers
    #   django-extensions
    #   django-filter
    #   django-hijack
    #   django-otp
    #   django-timezone-field
    #   django-waffle
    #   djangorestframework
    #   djangorestframework-simplejwt
    #   drf-spectacular
    #   drf-yasg
    #   sentry-sdk
django-allauth[mfa,socialaccount]==0.63.3
    # via
    #   -r requirements/requirements.in
    #   django-allauth-2fa
django-allauth-2fa==0.11.1
    # via -r requirements/requirements.in
django-anymail[sendgrid]==10.3
    # via -r requirements/requirements.in
django-bootstrap-v5==1.0.11
    # via -r requirements/requirements.in
django-celery-beat==2.6.0
    # via -r requirements/requirements.in
django-cors-headers==4.3.1
    # via -r requirements/requirements.in
django-enumfields==2.1.1
    # via -r requirements/requirements.in
django-environ==0.11.2
    # via -r requirements/requirements.in
django-extensions==3.2.3
    # via -r requirements/requirements.in
django-filter==24.2
    # via -r requirements/requirements.in
django-hijack==3.4.5
    # via -r requirements/requirements.in
django-otp==1.5.0
    # via
    #   -r requirements/requirements.in
    #   django-allauth-2fa
django-timezone-field==6.1.0
    # via django-celery-beat
django-waffle==4.1.0
    # via -r requirements/requirements.in
djangorestframework==3.15.1
    # via
    #   -r requirements/requirements.in
    #   dj-rest-auth
    #   djangorestframework-simplejwt
    #   drf-spectacular
    #   drf-yasg
djangorestframework-simplejwt==5.3.1
    # via -r requirements/requirements.in
drf-spectacular==0.27.2
    # via -r requirements/requirements.in
drf-yasg==1.21.7
    # via -r requirements/requirements.in
flower==2.0.1
    # via -r requirements/requirements.in
frozenlist==1.4.1
    # via
    #   aiohttp
    #   aiosignal
google-ai-generativelanguage==0.6.6
    # via google-generativeai
google-api-core[grpc]==2.19.1
    # via
    #   google-ai-generativelanguage
    #   google-api-python-client
    #   google-generativeai
google-api-python-client==2.136.0
    # via google-generativeai
google-auth==2.31.0
    # via
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-api-python-client
    #   google-auth-httplib2
    #   google-generativeai
google-auth-httplib2==0.2.0
    # via google-api-python-client
google-generativeai==0.7.1
    # via -r requirements/requirements.in
googleapis-common-protos==1.63.2
    # via
    #   google-api-core
    #   grpcio-status
gotrue==2.5.4
    # via supabase
greenlet==3.0.3
    # via sqlalchemy
grpcio==1.64.1
    # via
    #   google-api-core
    #   grpcio-status
grpcio-status==1.62.2
    # via google-api-core
h11==0.14.0
    # via httpcore
httpcore==1.0.5
    # via httpx
httplib2==0.22.0
    # via
    #   google-api-python-client
    #   google-auth-httplib2
httpx==0.27.0
    # via
    #   apify-client
    #   gotrue
    #   openai
    #   postgrest
    #   storage3
    #   supabase
    #   supafunc
humanize==4.9.0
    # via flower
idna==3.7
    # via
    #   anyio
    #   httpx
    #   requests
    #   yarl
inflection==0.5.1
    # via
    #   drf-spectacular
    #   drf-yasg
isodate==0.6.1
    # via msrest
jsonpatch==1.33
    # via langchain-core
jsonpointer==3.0.0
    # via jsonpatch
jsonschema==4.22.0
    # via drf-spectacular
jsonschema-specifications==2023.12.1
    # via jsonschema
kombu==5.3.7
    # via celery
langchain==0.2.6
    # via
    #   -r requirements/requirements.in
    #   langchain-community
langchain-community==0.2.6
    # via -r requirements/requirements.in
langchain-core==0.2.11
    # via
    #   langchain
    #   langchain-community
    #   langchain-openai
    #   langchain-text-splitters
langchain-openai==0.1.10
    # via -r requirements/requirements.in
langchain-text-splitters==0.2.2
    # via langchain
langsmith==0.1.83
    # via
    #   langchain
    #   langchain-community
    #   langchain-core
mailchimp3==3.0.21
    # via -r requirements/requirements.in
markdown==3.6
    # via -r requirements/requirements.in
marshmallow==3.21.3
    # via dataclasses-json
msrest==0.7.1
    # via azure-ai-formrecognizer
multidict==6.0.5
    # via
    #   aiohttp
    #   yarl
mypy-extensions==1.0.0
    # via typing-inspect
numpy==1.26.4
    # via
    #   langchain
    #   langchain-community
oauthlib==3.2.2
    # via requests-oauthlib
openai==1.30.5
    # via
    #   -r requirements/requirements.in
    #   langchain-openai
orjson==3.10.6
    # via langsmith
packaging==24.1
    # via
    #   deprecation
    #   drf-yasg
    #   langchain-core
    #   marshmallow
pinecone-client==5.0.1
    # via -r requirements/requirements.in
pinecone-plugin-inference==1.1.0
    # via pinecone-client
pinecone-plugin-interface==0.0.7
    # via
    #   pinecone-client
    #   pinecone-plugin-inference
postgrest==0.16.8
    # via supabase
prometheus-client==0.20.0
    # via flower
prompt-toolkit==3.0.45
    # via click-repl
proto-plus==1.24.0
    # via
    #   google-ai-generativelanguage
    #   google-api-core
protobuf==4.25.3
    # via
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-generativeai
    #   googleapis-common-protos
    #   grpcio-status
    #   proto-plus
psycopg2-binary==2.9.9
    # via -r requirements/requirements.in
py==1.11.0
    # via retry
pyasn1==0.6.0
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.0
    # via google-auth
pycparser==2.22
    # via cffi
pydantic==2.7.2
    # via
    #   google-generativeai
    #   gotrue
    #   langchain
    #   langchain-core
    #   langsmith
    #   openai
    #   postgrest
pydantic-core==2.18.3
    # via pydantic
pyjwt[crypto]==2.8.0
    # via
    #   django-allauth
    #   djangorestframework-simplejwt
pyparsing==3.1.2
    # via httplib2
pypng==0.20220715.0
    # via qrcode
python-crontab==3.1.0
    # via django-celery-beat
python-dateutil==2.9.0.post0
    # via
    #   celery
    #   python-crontab
    #   realtime
    #   storage3
python-http-client==3.3.7
    # via sendgrid
pytz==2024.1
    # via
    #   drf-yasg
    #   flower
pyyaml==6.0.1
    # via
    #   drf-spectacular
    #   drf-yasg
    #   langchain
    #   langchain-community
    #   langchain-core
qrcode==7.4.2
    # via
    #   django-allauth
    #   django-allauth-2fa
realtime==1.0.5
    # via supabase
redis==5.0.4
    # via celery
referencing==0.35.1
    # via
    #   jsonschema
    #   jsonschema-specifications
regex==2024.5.15
    # via tiktoken
requests==2.32.3
    # via
    #   -r requirements/requirements.in
    #   azure-core
    #   django-allauth
    #   django-anymail
    #   google-api-core
    #   langchain
    #   langchain-community
    #   langsmith
    #   mailchimp3
    #   msrest
    #   requests-oauthlib
    #   stripe
    #   tiktoken
requests-oauthlib==2.0.0
    # via
    #   django-allauth
    #   msrest
retry==0.9.2
    # via -r requirements/requirements.in
rpds-py==0.18.1
    # via
    #   jsonschema
    #   referencing
rsa==4.9
    # via google-auth
sendgrid==6.11.0
    # via -r requirements/requirements.in
sentry-sdk[django]==2.3.1
    # via -r requirements/requirements.in
six==1.16.0
    # via
    #   azure-core
    #   isodate
    #   python-dateutil
sniffio==1.3.1
    # via
    #   anyio
    #   httpx
    #   openai
soupsieve==2.5
    # via beautifulsoup4
sqlalchemy==2.0.31
    # via
    #   langchain
    #   langchain-community
sqlparse==0.5.0
    # via django
starkbank-ecdsa==2.2.0
    # via sendgrid
storage3==0.7.6
    # via supabase
strenum==0.4.15
    # via postgrest
stripe==4.2.0
    # via
    #   -r requirements/requirements.in
    #   dj-stripe
supabase==2.5.1
    # via -r requirements/requirements.in
supafunc==0.4.6
    # via supabase
tenacity==8.4.2
    # via
    #   langchain
    #   langchain-community
    #   langchain-core
tiktoken==0.7.0
    # via
    #   -r requirements/requirements.in
    #   langchain-openai
tornado==6.4.1
    # via flower
tqdm==4.66.4
    # via
    #   google-generativeai
    #   openai
    #   pinecone-client
typing-extensions==4.12.1
    # via
    #   azure-ai-formrecognizer
    #   azure-core
    #   google-generativeai
    #   openai
    #   pinecone-client
    #   pydantic
    #   pydantic-core
    #   qrcode
    #   realtime
    #   sqlalchemy
    #   storage3
    #   typing-inspect
typing-inspect==0.9.0
    # via dataclasses-json
tzdata==2024.1
    # via
    #   celery
    #   django-celery-beat
uritemplate==4.1.1
    # via
    #   drf-spectacular
    #   drf-yasg
    #   google-api-python-client
urllib3==2.2.1
    # via
    #   django-anymail
    #   pinecone-client
    #   requests
    #   sentry-sdk
vine==5.1.0
    # via
    #   amqp
    #   celery
    #   kombu
wcwidth==0.2.13
    # via prompt-toolkit
websockets==12.0
    # via realtime
whitenoise[brotli]==6.6.0
    # via -r requirements/requirements.in
yarl==1.9.4
    # via aiohttp
