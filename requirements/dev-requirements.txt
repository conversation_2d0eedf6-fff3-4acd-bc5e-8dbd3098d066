#
# This file is autogenerated by pip-compile with Python 3.11
# by the following command:
#
#    pip-compile requirements/dev-requirements.in
#
asgiref==3.8.1
    # via
    #   -c requirements/requirements.txt
    #   django
black==24.4.2
    # via -r requirements/dev-requirements.in
build==1.2.1
    # via pip-tools
cfgv==3.4.0
    # via pre-commit
click==8.1.7
    # via
    #   -c requirements/requirements.txt
    #   black
    #   pip-tools
distlib==0.3.8
    # via virtualenv
django==4.2.13
    # via
    #   -c requirements/requirements.txt
    #   django-debug-toolbar
django-debug-toolbar==4.4.2
    # via -r requirements/dev-requirements.in
filelock==3.14.0
    # via virtualenv
identify==2.5.36
    # via pre-commit
isort==5.13.2
    # via -r requirements/dev-requirements.in
mypy-extensions==1.0.0
    # via
    #   -c requirements/requirements.txt
    #   black
nodeenv==1.9.0
    # via pre-commit
packaging==24.1
    # via
    #   -c requirements/requirements.txt
    #   black
    #   build
pathspec==0.12.1
    # via black
pip-tools==7.4.1
    # via -r requirements/dev-requirements.in
platformdirs==4.2.2
    # via
    #   black
    #   virtualenv
pre-commit==3.7.1
    # via -r requirements/dev-requirements.in
pyproject-hooks==1.1.0
    # via
    #   build
    #   pip-tools
pyyaml==6.0.1
    # via
    #   -c requirements/requirements.txt
    #   pre-commit
ruff==0.4.7
    # via -r requirements/dev-requirements.in
sqlparse==0.5.0
    # via
    #   -c requirements/requirements.txt
    #   django
    #   django-debug-toolbar
virtualenv==20.26.2
    # via pre-commit
wheel==0.43.0
    # via pip-tools

# The following packages are considered to be unsafe in a requirements file:
# pip
# setuptools
