import React, { useState, useEffect } from "react";
import { createRoot } from "react-dom/client";
import CSRFToken from '../csrftoken';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPaperPlane } from '@fortawesome/free-solid-svg-icons'
import ChatApplication from "../chat/ChatApplication";
import Autocomplete from "react-google-autocomplete";
import { Modal, Button } from "react-bootstrap";
import introJs from 'intro.js';

const chat = JSON.parse(document.getElementById('chat').textContent);
const apiUrls = JSON.parse(document.getElementById('api-urls').textContent);

const UploadForm = function (props) {

    const [addressValid, setAddressValid] = useState()
    const [fileValid, setFileValid] = useState()
    const [canSubmit, setCanSubmit] = useState(false)
    const [file, setFile] = useState(null);
    const [address, setAddress] = useState(null);
    const [progress, setProgress] = useState(0);
    const [error, setError] = useState(false);
    const GOOGLE_MAPS_API_KEY = document.getElementById('new-report').getAttribute('google-api-key')

    useEffect(() => {
        setCanSubmit(addressValid)
    }, [addressValid, fileValid])

    // Handle file selection
    const handleFileSelect = (event) => {
        const selectedFile = event.target.files[0];
        setFile(selectedFile);
    };

    // Handle the upload button click
    const handleUpload = () => {
        // Create a new XMLHttpRequest object
        const csrfToken = document.getElementById('csrfmiddlewaretoken').value
        const xhr = new XMLHttpRequest();
        const formData = new FormData();
        formData.append("address", address)
        formData.append("file", file);
        console.log("csrf token", csrfToken)

        // Set the progress event listener
        xhr.upload.addEventListener("progress", (event) => {
            console.log("progress")
            if (event.lengthComputable) {
                // Calculate the upload progress percentage
                const uploadProgress = Math.round(
                    (event.loaded / event.total) * 100
                );
                setProgress(uploadProgress);
            }
        });
        xhr.addEventListener("error", (req, ev) => {
            setAddress(null)
            setFile(null);
            setProgress(0);
            setError(true);
            console.error('Error with request:', xhr.statusText);
            console.log("error", req, ev)
        });
        xhr.addEventListener("load", (event) => {
            props.setShow(false);
            setAddress(null)
            setFile(null);
            setProgress(0);
            setError(false);
            // hide the form
        });
        // Open the XMLHttpRequest object and send the FormData
        xhr.open("POST", "/reports/upload/");
        xhr.setRequestHeader("X-CSRFToken", csrfToken);
        console.log("response", xhr.send(formData));
    };

    return (
        <div className="pg-content mb-5">
            <CSRFToken />
            <div className="form-control-group">
                <label for="address">Address:</label>
                <Autocomplete className="form-control" id="address" name="address"
                    apiKey={GOOGLE_MAPS_API_KEY}
                    options={{
                        types: ["address"],
                        componentRestrictions: { country: "us" },
                    }}
                    onPlaceSelected={(place) => {
                        setAddress(place.formatted_address);
                        setAddressValid(true);
                        console.log(place);
                    }}
                />
            </div>
            <div className="py-2 form-control-group">
                <label for="file">Inspection Report: [Recommended]</label>
                <input
                    className="form-control p-0"
                    type='file'
                    id="file"
                    name="file"
                    onChange={(e) => {
                        setFileValid(e.target.value.length > 0);
                        handleFileSelect(e);
                    }}
                ></input>
            </div>
            <div>
                {progress > 0 ? (
                    <div>
                        {file && <div>Uploading: <progress with="100%" value={progress} max="100" /></div>}
                        <div>Processing: <span className="spinner-border spinner-border-sm" role="status" title={progress}></span></div>
                    </div>) : (<></>)
                }
                {error ? (
                    <div className="alert alert-danger" role="alert">
                        <strong>Error!</strong> There was an error processing your request.
                    </div>
                ) : (<></>)}
            </div>
            <button onClick={handleUpload} className="btn btn-primary btn-block" disabled={!canSubmit}>Upload</button>
        </div >);
}

const ReportList = function (props) {
    return (
        <div className="report-list my-4">
            {props.reports?.map((report) => (
                <div className="card flex-row p-1 my-1 border-0 report-card" key={report.id}>
                    <div className="card-header border-0 p-1">
                        {report.details?.imgSrc ? (
                            <img className='report-img' src={report.details.imgSrc} alt="" />
                        ) : (
                            <img className='report-img' src="/static/images/logos/home01.png" alt="" />
                        )
                        }
                    </div>
                    <div className="card-block p-2">
                        <a className="h5 card-title" href={'/reports/view/' + report.file_name}>{report.name}</a>
                        <div className="card-text fz-13">
                            <div>
                                {report.chat_status !== "Done" ? (
                                    <span className="p-2 spinner-border spinner-border-sm" role="status" title={report.chat_status}></span>
                                ) : (<span><i className="p-2 fa fa-comment fa-solid" aria-hidden="true" title={report.chat_status}></i></span>)}
                                {report.summary_status !== "Done" ? (
                                    <span className="p-2 spinner-border spinner-border-sm" role="status" title={report.summary_status}></span>
                                ) : (<span><i className="p-2 fa fa-book fa-solid" aria-hidden="true" title={report.summary_status}></i></span>)}
                                {report.details_status !== "Done" ? (
                                    <span className="p-2 spinner-border spinner-border-sm" role="status" title={report.details_status}></span>
                                ) : (<span><i className="p-2 fa fa-list fa-solid" aria-hidden="true" title={report.details_status}></i></span>)}
                            </div>
                        </div>
                        {report.details ? (
                            <div className="d-sm-inline-flex">
                                <span><i className="px-2 fa fa-light fa-bed"></i>{report.details.bedrooms}</span>
                                <span><i className="px-2 fa fa-light fa-bath"></i>{report.details.bathrooms}</span>
                            </div>
                        ) : (<></>)}
                        <span className="badge badge-md badge-pill badge-info px-2 mx-2">
                            <a className="report-link" href={'/reports/view/' + report.file_name}>Details</a>
                        </span>
                    </div>
                </div>
            ))}
        </div>);
}

const NewReport = function () {
    const [reports, setReports] = useState([]);
    const GOOGLE_MAPS_API_KEY = document.getElementById('new-report').getAttribute('google-api-key')
    const username = document.getElementById('new-report').getAttribute('data-user')
    useEffect(() => {
        // refresh reports every 5 seconds
        introJs().start();
        if (!show) {
            fetchReportsData();
            console.log("#reports", reports.length)
        }
        const interval = setInterval(() => {
            if (!show) {
                fetchReportsData();
            }
        }, 5000);
        return () => {
            clearInterval(interval);
        };
    }, []);

    function fetchReportsData() {
        fetch('/reports/reports/').then(res => res.json()).then((reports) => {
            setReports(reports.map((report) => {
                var updated = Object.assign({}, report);
                try {
                    updated.details = JSON.parse(report.details);
                } catch (e) {
                    console.log("error", e)
                    updated.details = null;
                }
                return updated;
            }).sort((a, b) => { return (b.id - a.id) }));
        }).catch((error) => {
            console.log("error", error)
            return []
        });
    }

    const [show, setShow] = useState(false);
    const handleShow = () => setShow(!show);

    return (
        <section className="section row">
            <div className='col-sm-5'>
                <div className="h4 flex-row justify-content">
                    <b>Homes</b> &nbsp;
                    <span> <i
                        data-title="Add new home"
                        data-intro="You can add new homes for considerations here. Optionally, you can also add inspection report or disclosures (recommended to get the most out of homescore)"
                        className="btn btn-primary fa fa-cloud-upload" aria-hidden="true" onClick={handleShow}></i>
                    </span>
                </div>
                {show ?
                    (

                        <UploadForm setShow={setShow} />
                    ) : <></>}
                <div
                    data-title="Homes"
                    data-intro="This pane shows the list of homes you're seriously considering. You can click on individual homes to see more details."
                >
                    <ReportList reports={reports} />
                </div>
            </div>
            <div className='col-sm-7'>
                <div
                    data-title="Chat"
                    data-intro="You can ask any home buying and home repair related questions here. The questions here are not specific to any home and are general in nature."
                    className="pg-text-right report-chat report-bg"
                >
                    <ChatApplication chat={chat} apiUrls={apiUrls}
                        inputClass="chat-input form-control"
                        inputPlaceholder="Ask any question"
                        buttonClass="pg-button-primary chat-button"
                        buttonContent={<FontAwesomeIcon icon={faPaperPlane} className='nav-item-icon' />}
                    />
                </div>
            </div>
        </section>
    );
}

const domContainer = document.querySelector('#new-report');

const root = createRoot(domContainer);
root.render(
    <NewReport />
);