import React, { useState, useEffect, useRef } from "react";
import { createRoot } from "react-dom/client";
import ChatApplication from "../chat/ChatApplication";
import StarRatings from 'react-star-ratings';
import { Tab, Tabs, Tab<PERSON><PERSON>, Tab<PERSON>anel } from 'react-tabs';
import introJs from 'intro.js';

const Chat = function () {
    const chat = JSON.parse(document.getElementById('chat').textContent);
    const apiUrls = JSON.parse(document.getElementById('api-urls').textContent);
    const report = document.getElementById('issues-chat').getAttribute('data-report')
    const address = document.getElementById('issues-chat').getAttribute('data-address')
    const homescore = document.getElementById('issues-chat').getAttribute('data-homescore')
    const tooltipText = `The score indicates that the cost of fixing issues found in the report is better than ${homescore}% of the houses in our database. A score of 0 would indicate most expensive repairs (worst condition) and 100 would indicate least expensive (and best condition) compared to all the houses known to HomeScore.`
    return (
        <div className="issues-chat">
            <ChatApplication chat={chat} apiUrls={apiUrls}
                inputClass="pg-control"
                inputPlaceholder="Type your message..."
                buttonClass="pg-button-primary mx-2"
                buttonContent="Send"
            />
        </div>
    );
}

const Issue = function (props) {
    const [issue, setIssue] = useState(props.issue)
    const onClick = props.onClick
    return (
        <div className="card flex-row p-2 my-2 border-0 issue-card" key={issue.pk}>
            <div className="border-0 p-2">
                {issue.fields.urgency == "High" ?
                    (<i className="fa fa-exclamation-triangle fa-2x"></i>) :
                    (<i className="fa fa-info-circle fa-2x"></i>)}
            </div>
            <div className="card-block p-2 w-100">
                <div>
                    <p className="h5 report-link card-title">{issue.fields.issue}</p>
                </div>
                <div>
                    <div>
                        {issue.fields.urgency === "High" ? (
                            <span className="badge badge-pill badge-danger">Urgent</span>
                        ) : (issue.fields.urgency === "Medium" ? (
                            <span className="badge badge-pill badge-warning">Soon</span>
                        ) : (<span className="badge badge-pill badge-info">Later</span>))}
                        <span className="badge badge-pill badge-secondary mx-1">{issue.fields.source}</span>
                        <span className="badge badge-pill badge-secondary">${issue.fields.cost_estimate_low}-${issue.fields.cost_estimate_high}</span>
                    </div>
                </div>
                <p className="card-text fz-13">{issue.fields.context}</p>
                <div>
                    <span className="btn badge badge-secondary" onClick={onClick}><i className="fa fa-search"></i>&nbsp;Professionals</span>
                </div>
            </div>
        </div>
    )
}

const Issues = function (props) {
    const updateIssue = props.updateIssue
    const [summary, setSummary] = useState(summaryData("issues"));
    function summaryData() {
        const sumData = JSON.parse(JSON.parse(document.getElementById('issues-data').textContent));
        sumData.map((issue) => {
            if (issue.fields.urgency == "High") {
                issue.urgency_number = 1
            } else if (issue.fields.urgency == "Medium") {
                issue.urgency_number = 2
            } else {
                issue.urgency_number = 3
            }
        });
        return sumData.sort((a, b) => { return a.urgency_number - b.urgency_number });
    }
    const [inputText, setInputText] = useState("");
    const [filteredIssues, setFilteredIssues] = useState(summary)

    let inputHandler = (e) => {
        var lowerCase = e.target.value.toLowerCase();
        setInputText(lowerCase);
    };
    useEffect(() => {
        setFilteredIssues(summary.filter((issue) => {
            return inputText === "" || issue.fields.issue.toLowerCase().includes(inputText);
        }))
    }, [inputText, summary]);

    let clickHandler = (e) => {
        var tab = e.target.innerHTML.toLowerCase();
        var parent = e.target.parentElement
        parent.classList.add("focus-tab")
        var list_li = Array.prototype.slice.call(parent.parentElement.children)
        for (var li in list_li) {
            if (list_li[li] != e.target.parentElement) {
                list_li[li].classList.remove("focus-tab")
            }
        }
        setSummary(summaryData())
    };
    return (
        <>
            {
                summary.length > 0 ? (
                    <div className="issues">
                        <div className="search mx-3 mb-4 mt-3">
                            <input
                                type="text"
                                placeholder="Search"
                                onChange={inputHandler}
                                className="w-100 p-3 pl-5"
                            />
                        </div>
                        <div className="issues-list my-4">
                            <div className="issues-wrapper">
                                {filteredIssues?.map((issue) =>
                                    <Issue issue={issue} onClick={() => updateIssue(issue.fields.issue)} key={issue.fields.issue}>
                                    </Issue>
                                )}
                            </div>
                        </div>
                    </div>) : (
                    <div className="text-center my-5">No issues found (Upload report to enable)</div>
                )
            }
        </>);
};

const Place = function (props) {
    const baseUrl = JSON.parse(document.getElementById('base-url').textContent);
    const [place, setPlace] = useState(props.place)
    const [details, setDetails] = useState({})

    const placesUrl = "../places"

    async function fetchData() {
        var request = new URL("/reports/place_details", baseUrl);
        request.searchParams.append("place_id", place.place_id)
        console.log("Request: " + request.toString())
        const response = await fetch(request, {
            method: "GET",
            mode: 'no-cors'
        })
        console.log(response)
        if (!response.ok) {
            throw new Error('Data coud not be fetched!')
        } else {
            return response.json()
        }
    }

    function onClick() {
        fetchData()
            .then((response) => {
                setDetails(response.results)
                console.log(details)
            });
    }


    return (
        <div className="card my-2 mb-2 border-0 issue-card">
            <span>
                <b>{place.name}{'\u00A0'}</b><i className="fa fa-info-circle" onClick={onClick}></i></span>
            {place.formatted_address.replace(new RegExp(", United\.*"), "")}
            <div>
                {details.result?.website ? (<a href={details.result.website}><i className="fa fa-globe"></i>Website{'\u00A0'}</a>) : ("")}
                {details.result?.formatted_phone_number ? (<a href={"tel:" + details.result.formatted_phone_number}><span><i className="fa fa-phone"></i>{details.result.formatted_phone_number}{'\u00A0'}</span></a>) : ("")}
                {details.result?.rating ? (<StarRatings rating={details.result.rating} starDimension="1em" starSpacing="0.1em" starRatedColor="orange" />) : ("")}
            </div>
        </div>
    )
}
const Info = function (props) {
    const places = props.places;
    const issue = props.issue;
    return (
        <div className="issues my-3">
            <div className="title p-3">{issue}</div>
            <div className="issues-list" style={{ display: 'block', overflow: 'auto', height: '75vh' }}>
                <div className="issues-wrapper">
                    {places?.map((place) =>
                        <Place place={place} key={place.place_id}>
                        </Place>
                    )}
                </div>
            </div>
        </div>
    )
}

import { Radar } from 'react-chartjs-2';
import {
    Chart as ChartJS,
    RadialLinearScale,
    PointElement,
    LineElement,
    Filler,
    Tooltip,
    Legend,
} from 'chart.js';

ChartJS.register(
    RadialLinearScale,
    PointElement,
    LineElement,
    Filler,
    Tooltip,
    Legend
);

const Preference = function (props) {
    const iconMap = {
        // "Location": "fa-map-marker",
        "Curb Appeal": "fa-home",
        "Good Schools": "fa-book",
        "Home Size": "fa-map",
        "Noise Level": "fa-volume-up",
        "Amenities": "fa-tv",
        "Maintenance": "fa-money",
        "Energy Efficiency": "fa-thermometer",
        "Natural Lighting": "fa-lightbulb-o",
    }
    const [preference, setPreference] = useState(props.preference)
    return (
        <div className="card flex-row p-2 my-2 border-0 issue-card">
            <div className="border-0 p-2">
                <i className={"fa fa-2x " + iconMap[preference.fields.preference]}></i>
            </div>
            <div className="card-block p-2 w-100">
                <span>
                    <b>{preference.fields.preference}</b><span className="badge badge-pill badge-secondary mx-1">{preference.fields.score}/10</span>
                </span>
                <p className="card-text fz-13">{preference.fields.rationale}</p>
            </div>
        </div>
    )
}


const PreferenceList = function (props) {
    const updateIssue = props.updateIssue
    const inputRef = useRef(null);
    const [preferences, setPreferences] = useState(preferenceData());
    //const [data, setData] = useState({})
    function preferenceData() {
        const preference_list = JSON.parse(JSON.parse(document.getElementById('preferences-data').textContent));
        // console.log("parsed")
        // console.log(preferences)
        // return preferences.sort((a, b) =>{return a.fields.preference > b.fields.preference? 1: -1});
        return preference_list;
    }
    const [inputText, setInputText] = useState("");
    const [filteredPreferences, setFilteredPreferences] = useState(preferences)

    let inputHandler = (e) => {
        var lowerCase = e.target.value.toLowerCase();
        setInputText(lowerCase);
    };
    useEffect(() => {
        setFilteredPreferences(preferences.filter((preference) => {
            return inputText === "" || preference.fields.preference.toLowerCase().includes(inputText);
        }))
        console.log(preferences.map((p) => {
            return {
                name: p.fields.preference,
                score: p.fields.score
            };
        }));
    }, [inputText, preferences]);

    return (
        <div className="issues">
            <div className="search mx-3 mb-4 mt-3">
                <input
                    type="text"
                    placeholder="Search"
                    onChange={inputHandler}
                    className="w-100 p-3 pl-5"
                />
            </div>
            <div className="issues-list my-4">
                <div className="issues-wrapper">
                    {filteredPreferences?.map((p) =>
                        <Preference preference={p} key={p.fields.preference} />
                    )}
                </div>
            </div>
        </div>
    );
};

const HomeScore = function (props) {
    const [preferences, setPreferences] = useState(preferenceData());
    //const [data, setData] = useState({})
    function preferenceData() {
        const preference_list = JSON.parse(JSON.parse(document.getElementById('preferences-data').textContent));
        // console.log("parsed")
        // console.log(preferences)
        // return preferences.sort((a, b) =>{return a.fields.preference > b.fields.preference? 1: -1});
        return preference_list;
    }
    const data = {
        labels: preferences.map((p) => p.fields.preference),
        datasets: [
            {
                label: 'HomeScore',
                data: preferences.map((p) => p.fields.score),
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 1,
            },
        ],
    }
    const options = {
        scales: {
            r: {
                min: 1,  // This sets the minimum value for the radar scale
                max: 10,  // Optional: if you also want to set the max value
                ticks: {
                    stepSize: 1  // Optional: if you want to control the step size between ticks
                },
                pointLabels: {
                    font: {
                        size: 12,
                        weight: 'bold'
                    }
                }
            }
        },
        plugins: {
            title: {
                display: true,
                text: 'HomeScore',
                font: {
                    weight: 'bold'
                }
            },
            legend: {
                labels: {
                    font: {
                        size: 12,
                        weight: 'bold'
                    }
                }
            }
        }
    }
    return (
        <div>
            <Radar data={data} options={options} />
        </div>
    );
};


const IssuesChat = function () {
    const baseUrl = JSON.parse(document.getElementById('base-url').textContent);
    const report = document.getElementById('issues-chat').getAttribute('data-report')
    const address = document.getElementById('issues-chat').getAttribute('data-address')
    const url = document.getElementById('issues-chat').getAttribute('data-url')
    const [issue, setIssue] = useState("Home Repairs")
    const [places, setPlaces] = useState()

    const placesUrl = "../places"

    async function fetchData() {
        console.log("base_url:" + baseUrl)
        var request = new URL("/reports/places", baseUrl);
        request.searchParams.append("issue", issue)
        request.searchParams.append("address", address ? address : "Cambridge, MA")

        console.log("Request: " + request.toString())
        const response = await fetch(request, {
            method: "GET",
            mode: 'no-cors'
        })
        console.log(response)
        if (!response.ok) {
            throw new Error('Data coud not be fetched!')
        } else {
            return response.json()
        }
    }

    useEffect(() => {
        introJs().start();
    }, [])

    useEffect(() => {
        fetchData()
            .then((response) => {
                setPlaces(response.results)
            });
    }, [issue])

    function updateIssue(updatedIssue) {
        setIssue(updatedIssue)
        fetchData()
            .then((response) => {
                setPlaces(response.results)
            });
    }

    return (
        <section className="">
            <div className="row">
                <div
                    data-title="AI Summary"
                    data-intro="This panel shows you information derived by HomeScore AI. These include the HomeScore, Preferences and Issues found in the report."
                    className="col-12 col-md-3"
                >
                    <div className="title py-3 my-3">
                        <h5>
                            AI Summary
                        </h5>
                    </div>
                    <Tabs>
                        <TabList>
                            <Tab
                                data-title="Homescore"
                                data-intro="The homescore graphic shows you how your home measures up on specific preference dimensions. The higher the score, the better the condition of your home."
                            >
                                HomeScore
                            </Tab>
                            <Tab
                            // data-title="Preferences"
                            // data-intro="This panel shows you the preferences how your home measures up on each of them."
                            >
                                Preferences
                            </Tab>
                            <Tab
                            // data-title="Issues"
                            // data-intro="This panel shows you the issues found from your report. It shows your the issue's urgency, source, cost estimate and context."
                            >
                                Issues
                            </Tab>
                        </TabList>
                        <TabPanel
                        >
                            <HomeScore />
                        </TabPanel>
                        <TabPanel
                        >
                            <PreferenceList />
                        </TabPanel>
                        <TabPanel>
                            <Issues updateIssue={updateIssue} />
                        </TabPanel>
                    </Tabs>
                </div>
                <div
                    data-title="Chat"
                    data-intro="This is where you can chat with our AI assistant to get more information about your home. You can ask questions about the home (e.g., Which are the nearest schools? Does the home have a fireplace?) or about the report you uploaded (e.g., What are the issues found in the report? Did the report find anything about HVAC?)."
                    className="col-12 col-md-6"
                >
                    <div className="title py-3 my-3">
                        <h5>
                            {address ? address.replace(new RegExp(", U\.*$"), "") : report}
                        </h5>
                    </div>
                    <Tabs>
                        <TabList>
                            <Tab>Chat</Tab>
                            <Tab>Report</Tab>
                        </TabList>
                        <TabPanel>
                            <Chat />
                        </TabPanel>
                        <TabPanel>
                            {url == 'None' ?
                                (<div className="text-center my-5">Report not uploaded</div>) :
                                (<object data={url} width="100%" height="850" tye="application/pdf"></object>)
                            }
                        </TabPanel>
                    </Tabs>
                </div>
                <div
                    data-title="Recommendations"
                    data-intro="This is where you can find recommendations of professionals close to your home. If you have uploaded a report, you can also get recommendations for specific issues found in the report."
                    className="col-12 col-md-3">
                    <Info places={places} issue={issue} />
                </div>
            </div>
        </section>
    );
}

const domContainer = document.querySelector('#issues-chat');

const root = createRoot(domContainer);
root.render(
    <IssuesChat />
);