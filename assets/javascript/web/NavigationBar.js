import React, {useState, useEffect} from "react";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faHome, faFilePdf, faComments, faUser, faFileAlt, faSignOut } from '@fortawesome/free-solid-svg-icons'

const NavigationBar = function() {
return  (
        <nav className='navbar navbar-items navbar-light navbar-expand-md bg-white shadow-sm'>
            <div className="col-7 container justify-content-space-between">
                <a className="navbar-brand" href="/">
                    <h4>Home Score</h4>
                </a>
            </div>
            <button className="navbar-toggler collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNavAltMarkup" aria-label="Toggle navigation">
                <span className="navbar-toggler-icon"></span>
            </button>
            <div className="col-md-5 col-sm-12 collapse navbar-collapse navbar-items" id="navbarNav">
                <ul className='navbar-ul navbar-nav'>
                    <li className='list-style-none nav-item w-100'>
                        <div className='dropdown-toggle w-100 align-end' data-bs-toggle="dropdown" id='navbarDarkDropdownMenuLink' role="button" aria-expanded="false">
                            Application
                        </div>
                        <ul className="dropdown-menu dropdown-menu-dark float-right" aria-labelledby="navbarDarkDropdownMenuLink">
                            <li className='d-flex'><FontAwesomeIcon icon={faHome} className='nav-item-icon' /><a className="dropdown-item item-pad" href="/">Dashboard</a></li>
                            <li className='d-flex'><FontAwesomeIcon icon={faComments} className='nav-item-icon' /><a className="dropdown-item item-pad" href="/chat">Chat with report</a></li>
                        </ul>
                    </li>
                    <li className='list-style-none nav-item w-100'>
                        <div className='dropdown-toggle w-100 align-end' data-bs-toggle="dropdown" id='navbarLightDropdownMenuLink' role="button" aria-expanded="false">
                            My Account
                        </div>
                        <ul className="dropdown-menu dropdown-menu-dark float-right" aria-labelledby="navbarLightDropdownMenuLink">
                            <li className='d-flex'><FontAwesomeIcon icon={faUser}  className='nav-item-icon'/><a className="dropdown-item item-pad" href="/users/profile">Profile</a></li>
                            <li className='d-flex'><FontAwesomeIcon icon={faFileAlt} className='nav-item-icon'/><a className="dropdown-item item-pad" href="/accounts/password/change">Change Password</a></li>
                            <li className='d-flex'><FontAwesomeIcon icon={faSignOut} className='nav-item-icon'/><a className="dropdown-item item-pad" href="/accounts/logout">Sign out</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </nav>
    );
}

export default NavigationBar;