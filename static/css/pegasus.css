.pg-title,.h3,h2,.h2,h1,.h1{
  margin-top:0;
  margin-bottom:.5rem;
  font-weight:500;
  line-height:1.2;
  color:var(--bs-heading-color)
}
.pg-title,.h3{
  font-size:calc(1.3rem + 0.6vw)
}
.pg-title,.h3{
  font-size:1.75rem
}
.pg-subtitle{
  font-size:1.25rem;
  font-weight:300
}
.pg-columns{
  --bs-gutter-x: 1.5rem;
  --bs-gutter-y: 0;
  display:flex;
  flex-wrap:wrap;
  margin-top:calc(-1*var(--bs-gutter-y));
  margin-right:calc(-0.5*var(--bs-gutter-x));
  margin-left:calc(-0.5*var(--bs-gutter-x))
}
.pg-columns{
  flex-shrink:0;
  width:100%;
  max-width:100%;
  padding-right:calc(var(--bs-gutter-x)*.5);
  padding-left:calc(var(--bs-gutter-x)*.5);
  margin-top:var(--bs-gutter-y)
}
.pg-columns{
  --bs-gutter-y: 1.5rem
}
.pg-column{
  flex:1 0 0%
}
.pg-column-one-quarter{
  flex:0 0 auto;
  width:25%
}
.pg-column-one-third{
  flex:0 0 auto;
  width:33.33333333%
}
.pg-table>tbody>tr:nth-of-type(odd{
  --bs-table-color-type: var(--bs-table-striped-color);
  --bs-table-bg-type: var(--bs-table-striped-bg)
}
.pg-label{
  margin-bottom:.5rem
}
.pg-help{
  margin-top:.25rem;
  font-size:0.875em;
  color:var(--bs-secondary-color)
}
.pg-control{
  display:block;
  width:100%;
  padding:.375rem .75rem;
  font-size:1rem;
  font-weight:400;
  line-height:1.5;
  color:var(--bs-body-color);
  background-color:var(--bs-body-bg);
  background-clip:padding-box;
  border:var(--bs-border-width) solid var(--bs-border-color);
  appearance:none;
  border-radius:var(--bs-border-radius);
  transition:border-color .15s ease-in-out,box-shadow .15s ease-in-out
}
.pg-control{
  transition:none
}
.pg-control{
  overflow:hidden
}
.pg-control:not(:disabled):not([readonly{
  cursor:pointer
}
.pg-control:focus{
  color:var(--bs-body-color);
  background-color:var(--bs-body-bg);
  border-color:#86b7fe;
  outline:0;
  box-shadow:0 0 0 .25rem rgba(13,110,253,.25)
}
.pg-control::-webkit-date-and-time-value{
  min-width:85px;
  height:1.5em;
  margin:0
}
.pg-control::-webkit-datetime-edit{
  display:block;
  padding:0
}
.pg-control::placeholder{
  color:var(--bs-secondary-color);
  opacity:1
}
.pg-control:disabled{
  background-color:var(--bs-secondary-bg);
  opacity:1
}
.pg-control::file-selector-button{
  padding:.375rem .75rem;
  margin:-0.375rem -0.75rem;
  margin-inline-end:.75rem;
  color:var(--bs-body-color);
  background-color:var(--bs-tertiary-bg);
  pointer-events:none;
  border-color:inherit;
  border-style:solid;
  border-width:0;
  border-inline-end-width:var(--bs-border-width);
  border-radius:0;
  transition:color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out
}
.pg-control::file-selector-button{
  transition:none
}
.pg-control:hover:not(:disabled):not([readonly])::file-selector-button{
  background-color:var(--bs-secondary-bg)
}
.pg-control{
  min-height:calc(1.5em + 0.75rem + calc(var(--bs-border-width) * 2))
}
.pg-select select{
  --bs-form-select-bg-img: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%270 0 16 16%27%3e%3cpath fill=%27none%27 stroke=%27%23343a40%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m2 5 6 6 6-6%27/%3e%3c/svg%3e");
  display:block;
  width:100%;
  padding:.375rem 2.25rem .375rem .75rem;
  font-size:1rem;
  font-weight:400;
  line-height:1.5;
  color:var(--bs-body-color);
  background-color:var(--bs-body-bg);
  background-image:var(--bs-form-select-bg-img),var(--bs-form-select-bg-icon, none);
  background-repeat:no-repeat;
  background-position:right .75rem center;
  background-size:16px 12px;
  border:var(--bs-border-width) solid var(--bs-border-color);
  border-radius:var(--bs-border-radius);
  transition:border-color .15s ease-in-out,box-shadow .15s ease-in-out;
  appearance:none
}
.pg-select select{
  transition:none
}
.pg-select select:focus{
  border-color:#86b7fe;
  outline:0;
  box-shadow:0 0 0 .25rem rgba(13,110,253,.25)
}
.pg-select select[multiple],.form-select[size]:not([size="1"]),.pg-select select[size]:not([size="1{
  padding-right:.75rem;
  background-image:none
}
.pg-select select:disabled{
  background-color:var(--bs-secondary-bg)
}
.pg-select select:-moz-focusring{
  color:rgba(0,0,0,0);
  text-shadow:0 0 0 var(--bs-body-color)
}
.pg-select select,.pg-select [data-bs-theme=dark] select{
  --bs-form-select-bg-img: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%270 0 16 16%27%3e%3cpath fill=%27none%27 stroke=%27%23adb5bd%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m2 5 6 6 6-6%27/%3e%3c/svg%3e")
}
.pg-button-light,.btn-check[disabled]+.pg-button-danger,.btn-check[disabled]+.pg-button-secondary,.btn-check[disabled]+.pg-button-primary,.btn-check:disabled+.btn,.btn-check:disabled+.pg-button-light,.btn-check:disabled+.pg-button-danger,.btn-check:disabled+.pg-button-secondary,.btn-check:disabled+.pg-button-primary{
  pointer-events:none;
  filter:none;
  opacity:.65
}
.pg-control,.form-floating>.form-control-plaintext,.form-floating>.form-select,.pg-select .form-floating>select{
  height:calc(3.5rem + calc(var(--bs-border-width) * 2));
  min-height:calc(3.5rem + calc(var(--bs-border-width) * 2));
  line-height:1.25
}
.pg-control,.form-floating>.form-control-plaintext{
  padding:1rem .75rem
}
.pg-control::placeholder,.form-floating>.form-control-plaintext::placeholder{
  color:rgba(0,0,0,0)
}
.pg-control:focus,.form-floating>.form-control:not(:placeholder-shown),.form-floating>.pg-control:not(:placeholder-shown),.form-floating>.form-control-plaintext:focus,.form-floating>.form-control-plaintext:not(:placeholder-shown{
  padding-top:1.625rem;
  padding-bottom:.625rem
}
.pg-control:-webkit-autofill,.form-floating>.form-control-plaintext:-webkit-autofill{
  padding-top:1.625rem;
  padding-bottom:.625rem
}
.pg-select .form-floating>select{
  padding-top:1.625rem;
  padding-bottom:.625rem
}
.pg-control:focus~label,.form-floating>.form-control:not(:placeholder-shown)~label,.form-floating>.pg-control:not(:placeholder-shown)~label,.form-floating>.form-control-plaintext~label,.form-floating>.form-select~label,.pg-select .form-floating>select~label{
  color:rgba(var(--bs-body-color-rgb), 0.65);
  transform:scale(0.85) translateY(-0.5rem) translateX(0.15rem)
}
.pg-control:focus~label::after,.form-floating>.form-control:not(:placeholder-shown)~label::after,.form-floating>.pg-control:not(:placeholder-shown)~label::after,.form-floating>.form-control-plaintext~label::after,.form-floating>.form-select~label::after,.pg-select .form-floating>select~label::after{
  position:absolute;
  inset:1rem .375rem;
  z-index:-1;
  height:1.5em;
  content:"";
  background-color:var(--bs-body-bg);
  border-radius:var(--bs-border-radius)
}
.pg-control:-webkit-autofill~label{
  color:rgba(var(--bs-body-color-rgb), 0.65);
  transform:scale(0.85) translateY(-0.5rem) translateX(0.15rem)
}
.pg-control,.input-group>.form-select,.pg-select .input-group>select,.input-group>.form-floating{
  position:relative;
  flex:1 1 auto;
  width:1%;
  min-width:0
}
.pg-control:focus,.input-group>.form-select:focus,.pg-select .input-group>select:focus,.input-group>.form-floating:focus-within{
  z-index:5
}
.pg-button-light,.input-group .pg-button-danger,.input-group .pg-button-secondary,.input-group .pg-button-primary{
  position:relative;
  z-index:2
}
.pg-button-light:focus,.input-group .pg-button-danger:focus,.input-group .pg-button-secondary:focus,.input-group .pg-button-primary:focus{
  z-index:5
}
.pg-control,.input-group-lg>.form-select,.pg-select .input-group-lg>select,.input-group-lg>.input-group-text,.input-group-lg>.btn,.input-group-lg>.pg-button-light,.input-group-lg>.pg-button-danger,.input-group-lg>.pg-button-secondary,.input-group-lg>.pg-button-primary{
  padding:.5rem 1rem;
  font-size:1.25rem;
  border-radius:var(--bs-border-radius-lg)
}
.pg-control,.input-group-sm>.form-select,.pg-select .input-group-sm>select,.input-group-sm>.input-group-text,.input-group-sm>.btn,.input-group-sm>.pg-button-light,.input-group-sm>.pg-button-danger,.input-group-sm>.pg-button-secondary,.input-group-sm>.pg-button-primary{
  padding:.25rem .5rem;
  font-size:0.875rem;
  border-radius:var(--bs-border-radius-sm)
}
.pg-select .input-group-lg>select,.input-group-sm>.form-select,.pg-select .input-group-sm>select{
  padding-right:3rem
}
.pg-control,.input-group:not(.has-validation)>.form-floating:not(:last-child)>.form-select,.pg-select .input-group:not(.has-validation)>.form-floating:not(:last-child)>select{
  border-top-right-radius:0;
  border-bottom-right-radius:0
}
.pg-control,.input-group.has-validation>.form-floating:nth-last-child(n+3)>.form-select,.pg-select .input-group.has-validation>.form-floating:nth-last-child(n+3)>select{
  border-top-right-radius:0;
  border-bottom-right-radius:0
}
.pg-control,.input-group>.form-floating:not(:first-child)>.form-select,.pg-select .input-group>.form-floating:not(:first-child)>select{
  border-top-left-radius:0;
  border-bottom-left-radius:0
}
.pg-control:valid,.form-control.is-valid,.is-valid.pg-control{
  border-color:var(--bs-form-valid-border-color);
  padding-right:calc(1.5em + 0.75rem);
  background-image:url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%270 0 8 8%27%3e%3cpath fill=%27%23198754%27 d=%27M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z%27/%3e%3c/svg%3e");
  background-repeat:no-repeat;
  background-position:right calc(0.375em + 0.1875rem) center;
  background-size:calc(0.75em + 0.375rem) calc(0.75em + 0.375rem)
}
.pg-control:valid:focus,.form-control.is-valid:focus,.is-valid.pg-control:focus{
  border-color:var(--bs-form-valid-border-color);
  box-shadow:0 0 0 .25rem rgba(var(--bs-success-rgb), 0.25)
}
.pg-control:valid,textarea.form-control.is-valid,textarea.is-valid.pg-control{
  padding-right:calc(1.5em + 0.75rem);
  background-position:top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem)
}
.pg-select select:valid,.pg-select .was-validated select:valid,.form-select.is-valid,.pg-select select.is-valid{
  border-color:var(--bs-form-valid-border-color)
}
.pg-select select:valid:not([multiple]):not([size]),.pg-select .was-validated select:valid:not([multiple]):not([size]),.was-validated .form-select:valid:not([multiple])[size="1"],.was-validated .pg-select select:valid:not([multiple])[size="1"],.pg-select .was-validated select:valid:not([multiple])[size="1"],.form-select.is-valid:not([multiple]):not([size]),.pg-select select.is-valid:not([multiple]):not([size]),.form-select.is-valid:not([multiple])[size="1"],.pg-select select.is-valid:not([multiple])[size="1{
  --bs-form-select-bg-icon: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%270 0 8 8%27%3e%3cpath fill=%27%23198754%27 d=%27M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z%27/%3e%3c/svg%3e");
  padding-right:4.125rem;
  background-position:right .75rem center,center right 2.25rem;
  background-size:16px 12px,calc(0.75em + 0.375rem) calc(0.75em + 0.375rem)
}
.pg-select select:valid:focus,.pg-select .was-validated select:valid:focus,.form-select.is-valid:focus,.pg-select select.is-valid:focus{
  border-color:var(--bs-form-valid-border-color);
  box-shadow:0 0 0 .25rem rgba(var(--bs-success-rgb), 0.25)
}
.pg-control:not(:focus):valid,.input-group>.form-control:not(:focus).is-valid,.input-group>.pg-control:not(:focus).is-valid,.was-validated .input-group>.form-select:not(:focus):valid,.was-validated .pg-select .input-group>select:not(:focus):valid,.pg-select .was-validated .input-group>select:not(:focus):valid,.input-group>.form-select:not(:focus).is-valid,.pg-select .input-group>select:not(:focus).is-valid,.was-validated .input-group>.form-floating:not(:focus-within):valid,.input-group>.form-floating:not(:focus-within).is-valid{
  z-index:3
}
.pg-control:invalid,.form-control.is-invalid,.is-invalid.pg-control{
  border-color:var(--bs-form-invalid-border-color);
  padding-right:calc(1.5em + 0.75rem);
  background-image:url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%270 0 12 12%27 width=%2712%27 height=%2712%27 fill=%27none%27 stroke=%27%23dc3545%27%3e%3ccircle cx=%276%27 cy=%276%27 r=%274.5%27/%3e%3cpath stroke-linejoin=%27round%27 d=%27M5.8 3.6h.4L6 6.5z%27/%3e%3ccircle cx=%276%27 cy=%278.2%27 r=%27.6%27 fill=%27%23dc3545%27 stroke=%27none%27/%3e%3c/svg%3e");
  background-repeat:no-repeat;
  background-position:right calc(0.375em + 0.1875rem) center;
  background-size:calc(0.75em + 0.375rem) calc(0.75em + 0.375rem)
}
.pg-control:invalid:focus,.form-control.is-invalid:focus,.is-invalid.pg-control:focus{
  border-color:var(--bs-form-invalid-border-color);
  box-shadow:0 0 0 .25rem rgba(var(--bs-danger-rgb), 0.25)
}
.pg-control:invalid,textarea.form-control.is-invalid,textarea.is-invalid.pg-control{
  padding-right:calc(1.5em + 0.75rem);
  background-position:top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem)
}
.pg-select select:invalid,.pg-select .was-validated select:invalid,.form-select.is-invalid,.pg-select select.is-invalid{
  border-color:var(--bs-form-invalid-border-color)
}
.pg-select select:invalid:not([multiple]):not([size]),.pg-select .was-validated select:invalid:not([multiple]):not([size]),.was-validated .form-select:invalid:not([multiple])[size="1"],.was-validated .pg-select select:invalid:not([multiple])[size="1"],.pg-select .was-validated select:invalid:not([multiple])[size="1"],.form-select.is-invalid:not([multiple]):not([size]),.pg-select select.is-invalid:not([multiple]):not([size]),.form-select.is-invalid:not([multiple])[size="1"],.pg-select select.is-invalid:not([multiple])[size="1{
  --bs-form-select-bg-icon: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%270 0 12 12%27 width=%2712%27 height=%2712%27 fill=%27none%27 stroke=%27%23dc3545%27%3e%3ccircle cx=%276%27 cy=%276%27 r=%274.5%27/%3e%3cpath stroke-linejoin=%27round%27 d=%27M5.8 3.6h.4L6 6.5z%27/%3e%3ccircle cx=%276%27 cy=%278.2%27 r=%27.6%27 fill=%27%23dc3545%27 stroke=%27none%27/%3e%3c/svg%3e");
  padding-right:4.125rem;
  background-position:right .75rem center,center right 2.25rem;
  background-size:16px 12px,calc(0.75em + 0.375rem) calc(0.75em + 0.375rem)
}
.pg-select select:invalid:focus,.pg-select .was-validated select:invalid:focus,.form-select.is-invalid:focus,.pg-select select.is-invalid:focus{
  border-color:var(--bs-form-invalid-border-color);
  box-shadow:0 0 0 .25rem rgba(var(--bs-danger-rgb), 0.25)
}
.pg-control:not(:focus):invalid,.input-group>.form-control:not(:focus).is-invalid,.input-group>.pg-control:not(:focus).is-invalid,.was-validated .input-group>.form-select:not(:focus):invalid,.was-validated .pg-select .input-group>select:not(:focus):invalid,.pg-select .was-validated .input-group>select:not(:focus):invalid,.input-group>.form-select:not(:focus).is-invalid,.pg-select .input-group>select:not(:focus).is-invalid,.was-validated .input-group>.form-floating:not(:focus-within):invalid,.input-group>.form-floating:not(:focus-within).is-invalid{
  z-index:4
}
.pg-button-light,.pg-button-danger,.pg-button-secondary,.pg-button-primary{
  --bs-btn-padding-x: 0.75rem;
  --bs-btn-padding-y: 0.375rem;
  --bs-btn-font-family: ;
  --bs-btn-font-size:1rem;
  --bs-btn-font-weight: 400;
  --bs-btn-line-height: 1.5;
  --bs-btn-color: var(--bs-body-color);
  --bs-btn-bg: transparent;
  --bs-btn-border-width: var(--bs-border-width);
  --bs-btn-border-color: transparent;
  --bs-btn-border-radius: var(--bs-border-radius);
  --bs-btn-hover-border-color: transparent;
  --bs-btn-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
  --bs-btn-disabled-opacity: 0.65;
  --bs-btn-focus-box-shadow: 0 0 0 0.25rem rgba(var(--bs-btn-focus-shadow-rgb), .5);
  display:inline-block;
  padding:var(--bs-btn-padding-y) var(--bs-btn-padding-x);
  font-family:var(--bs-btn-font-family);
  font-size:var(--bs-btn-font-size);
  font-weight:var(--bs-btn-font-weight);
  line-height:var(--bs-btn-line-height);
  color:var(--bs-btn-color);
  text-align:center;
  text-decoration:none;
  vertical-align:middle;
  cursor:pointer;
  user-select:none;
  border:var(--bs-btn-border-width) solid var(--bs-btn-border-color);
  border-radius:var(--bs-btn-border-radius);
  background-color:var(--bs-btn-bg);
  transition:color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out
}
.pg-button-light,.pg-button-danger,.pg-button-secondary,.pg-button-primary{
  transition:none
}
.pg-button-light:hover,.pg-button-danger:hover,.pg-button-secondary:hover,.pg-button-primary:hover{
  color:var(--bs-btn-hover-color);
  background-color:var(--bs-btn-hover-bg);
  border-color:var(--bs-btn-hover-border-color)
}
.pg-button-light:hover,.btn-check+.pg-button-danger:hover,.btn-check+.pg-button-secondary:hover,.btn-check+.pg-button-primary:hover{
  color:var(--bs-btn-color);
  background-color:var(--bs-btn-bg);
  border-color:var(--bs-btn-border-color)
}
.pg-button-light:focus-visible,.pg-button-danger:focus-visible,.pg-button-secondary:focus-visible,.pg-button-primary:focus-visible{
  color:var(--bs-btn-hover-color);
  background-color:var(--bs-btn-hover-bg);
  border-color:var(--bs-btn-hover-border-color);
  outline:0;
  box-shadow:var(--bs-btn-focus-box-shadow)
}
.pg-button-light,.btn-check:focus-visible+.pg-button-danger,.btn-check:focus-visible+.pg-button-secondary,.btn-check:focus-visible+.pg-button-primary{
  border-color:var(--bs-btn-hover-border-color);
  outline:0;
  box-shadow:var(--bs-btn-focus-box-shadow)
}
.pg-button-light,.btn-check:checked+.pg-button-danger,.btn-check:checked+.pg-button-secondary,.btn-check:checked+.pg-button-primary,:not(.btn-check)+.btn:active,:not(.btn-check)+.pg-button-light:active,:not(.btn-check)+.pg-button-danger:active,:not(.btn-check)+.pg-button-secondary:active,:not(.btn-check)+.pg-button-primary:active,.btn:first-child:active,.pg-button-light:first-child:active,.pg-button-danger:first-child:active,.pg-button-secondary:first-child:active,.pg-button-primary:first-child:active,.btn.active,.pg-breadcrumbs .btn.pg-breadcrumb-active,.active.pg-button-light,.pg-breadcrumbs .pg-button-light.pg-breadcrumb-active,.active.pg-button-danger,.pg-breadcrumbs .pg-button-danger.pg-breadcrumb-active,.active.pg-button-secondary,.pg-breadcrumbs .pg-button-secondary.pg-breadcrumb-active,.active.pg-button-primary,.pg-breadcrumbs .pg-button-primary.pg-breadcrumb-active,.btn.show,.show.pg-button-light,.show.pg-button-danger,.show.pg-button-secondary,.show.pg-button-primary{
  color:var(--bs-btn-active-color);
  background-color:var(--bs-btn-active-bg);
  border-color:var(--bs-btn-active-border-color)
}
.pg-button-light:focus-visible,.btn-check:checked+.pg-button-danger:focus-visible,.btn-check:checked+.pg-button-secondary:focus-visible,.btn-check:checked+.pg-button-primary:focus-visible,:not(.btn-check)+.btn:active:focus-visible,:not(.btn-check)+.pg-button-light:active:focus-visible,:not(.btn-check)+.pg-button-danger:active:focus-visible,:not(.btn-check)+.pg-button-secondary:active:focus-visible,:not(.btn-check)+.pg-button-primary:active:focus-visible,.btn:first-child:active:focus-visible,.pg-button-light:first-child:active:focus-visible,.pg-button-danger:first-child:active:focus-visible,.pg-button-secondary:first-child:active:focus-visible,.pg-button-primary:first-child:active:focus-visible,.btn.active:focus-visible,.pg-breadcrumbs .btn.pg-breadcrumb-active:focus-visible,.active.pg-button-light:focus-visible,.pg-breadcrumbs .pg-button-light.pg-breadcrumb-active:focus-visible,.active.pg-button-danger:focus-visible,.pg-breadcrumbs .pg-button-danger.pg-breadcrumb-active:focus-visible,.active.pg-button-secondary:focus-visible,.pg-breadcrumbs .pg-button-secondary.pg-breadcrumb-active:focus-visible,.active.pg-button-primary:focus-visible,.pg-breadcrumbs .pg-button-primary.pg-breadcrumb-active:focus-visible,.btn.show:focus-visible,.show.pg-button-light:focus-visible,.show.pg-button-danger:focus-visible,.show.pg-button-secondary:focus-visible,.show.pg-button-primary:focus-visible{
  box-shadow:var(--bs-btn-focus-box-shadow)
}
.pg-button-light:disabled,.pg-button-danger:disabled,.pg-button-secondary:disabled,.pg-button-primary:disabled,.btn.disabled,.btn.pg-is-loading,.disabled.pg-button-light,.pg-button-light.pg-is-loading,.disabled.pg-button-danger,.pg-button-danger.pg-is-loading,.disabled.pg-button-secondary,.pg-button-secondary.pg-is-loading,.disabled.pg-button-primary,.pg-button-primary.pg-is-loading,fieldset:disabled .btn,fieldset:disabled .pg-button-light,fieldset:disabled .pg-button-danger,fieldset:disabled .pg-button-secondary,fieldset:disabled .pg-button-primary{
  color:var(--bs-btn-disabled-color);
  pointer-events:none;
  background-color:var(--bs-btn-disabled-bg);
  border-color:var(--bs-btn-disabled-border-color);
  opacity:var(--bs-btn-disabled-opacity)
}
.pg-button-primary{
  --bs-btn-color: #fff;
  --bs-btn-bg: #0d6efd;
  --bs-btn-border-color: #0d6efd;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #0b5ed7;
  --bs-btn-hover-border-color: #0a58ca;
  --bs-btn-focus-shadow-rgb: 49, 132, 253;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #0a58ca;
  --bs-btn-active-border-color: #0a53be;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #fff;
  --bs-btn-disabled-bg: #0d6efd;
  --bs-btn-disabled-border-color: #0d6efd
}
.pg-button-light{
  --bs-btn-color: #000;
  --bs-btn-bg: #f8f9fa;
  --bs-btn-border-color: #f8f9fa;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #d3d4d5;
  --bs-btn-hover-border-color: #c6c7c8;
  --bs-btn-focus-shadow-rgb: 211, 212, 213;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #c6c7c8;
  --bs-btn-active-border-color: #babbbc;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #f8f9fa;
  --bs-btn-disabled-border-color: #f8f9fa
}
.pg-button-secondary{
  --bs-btn-color: #0d6efd;
  --bs-btn-border-color: #0d6efd;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #0d6efd;
  --bs-btn-hover-border-color: #0d6efd;
  --bs-btn-focus-shadow-rgb: 13, 110, 253;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #0d6efd;
  --bs-btn-active-border-color: #0d6efd;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #0d6efd;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #0d6efd;
  --bs-gradient: none
}
.pg-button-danger{
  --bs-btn-color: #dc3545;
  --bs-btn-border-color: #dc3545;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #dc3545;
  --bs-btn-hover-border-color: #dc3545;
  --bs-btn-focus-shadow-rgb: 220, 53, 69;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #dc3545;
  --bs-btn-active-border-color: #dc3545;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #dc3545;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #dc3545;
  --bs-gradient: none
}
.pg-button-light,.btn-group-lg>.pg-button-danger,.btn-group-lg>.pg-button-secondary,.btn-group-lg>.pg-button-primary{
  --bs-btn-padding-y: 0.5rem;
  --bs-btn-padding-x: 1rem;
  --bs-btn-font-size:1.25rem;
  --bs-btn-border-radius: var(--bs-border-radius-lg)
}
.pg-button-light,.btn-group-sm>.pg-button-danger,.btn-group-sm>.pg-button-secondary,.btn-group-sm>.pg-button-primary{
  --bs-btn-padding-y: 0.25rem;
  --bs-btn-padding-x: 0.5rem;
  --bs-btn-font-size:0.875rem;
  --bs-btn-border-radius: var(--bs-border-radius-sm)
}
.pg-breadcrumbs .dropdown-item.pg-breadcrumb-active,.dropdown-item:active{
  color:var(--bs-dropdown-link-active-color);
  text-decoration:none;
  background-color:var(--bs-dropdown-link-active-bg)
}
.pg-is-loading,.dropdown-item:disabled{
  color:var(--bs-dropdown-link-disabled-color);
  pointer-events:none;
  background-color:rgba(0,0,0,0)
}
.pg-button-light,.btn-group>.pg-button-danger,.btn-group>.pg-button-secondary,.btn-group>.pg-button-primary,.btn-group-vertical>.btn,.btn-group-vertical>.pg-button-light,.btn-group-vertical>.pg-button-danger,.btn-group-vertical>.pg-button-secondary,.btn-group-vertical>.pg-button-primary{
  position:relative;
  flex:1 1 auto
}
.pg-button-light,.btn-group>.btn-check:checked+.pg-button-danger,.btn-group>.btn-check:checked+.pg-button-secondary,.btn-group>.btn-check:checked+.pg-button-primary,.btn-group>.btn-check:focus+.btn,.btn-group>.btn-check:focus+.pg-button-light,.btn-group>.btn-check:focus+.pg-button-danger,.btn-group>.btn-check:focus+.pg-button-secondary,.btn-group>.btn-check:focus+.pg-button-primary,.btn-group>.btn:hover,.btn-group>.pg-button-light:hover,.btn-group>.pg-button-danger:hover,.btn-group>.pg-button-secondary:hover,.btn-group>.pg-button-primary:hover,.btn-group>.btn:focus,.btn-group>.pg-button-light:focus,.btn-group>.pg-button-danger:focus,.btn-group>.pg-button-secondary:focus,.btn-group>.pg-button-primary:focus,.btn-group>.btn:active,.btn-group>.pg-button-light:active,.btn-group>.pg-button-danger:active,.btn-group>.pg-button-secondary:active,.btn-group>.pg-button-primary:active,.btn-group>.btn.active,.pg-breadcrumbs .btn-group>.btn.pg-breadcrumb-active,.btn-group>.active.pg-button-light,.pg-breadcrumbs .btn-group>.pg-button-light.pg-breadcrumb-active,.btn-group>.active.pg-button-danger,.pg-breadcrumbs .btn-group>.pg-button-danger.pg-breadcrumb-active,.btn-group>.active.pg-button-secondary,.pg-breadcrumbs .btn-group>.pg-button-secondary.pg-breadcrumb-active,.btn-group>.active.pg-button-primary,.pg-breadcrumbs .btn-group>.pg-button-primary.pg-breadcrumb-active,.btn-group-vertical>.btn-check:checked+.btn,.btn-group-vertical>.btn-check:checked+.pg-button-light,.btn-group-vertical>.btn-check:checked+.pg-button-danger,.btn-group-vertical>.btn-check:checked+.pg-button-secondary,.btn-group-vertical>.btn-check:checked+.pg-button-primary,.btn-group-vertical>.btn-check:focus+.btn,.btn-group-vertical>.btn-check:focus+.pg-button-light,.btn-group-vertical>.btn-check:focus+.pg-button-danger,.btn-group-vertical>.btn-check:focus+.pg-button-secondary,.btn-group-vertical>.btn-check:focus+.pg-button-primary,.btn-group-vertical>.btn:hover,.btn-group-vertical>.pg-button-light:hover,.btn-group-vertical>.pg-button-danger:hover,.btn-group-vertical>.pg-button-secondary:hover,.btn-group-vertical>.pg-button-primary:hover,.btn-group-vertical>.btn:focus,.btn-group-vertical>.pg-button-light:focus,.btn-group-vertical>.pg-button-danger:focus,.btn-group-vertical>.pg-button-secondary:focus,.btn-group-vertical>.pg-button-primary:focus,.btn-group-vertical>.btn:active,.btn-group-vertical>.pg-button-light:active,.btn-group-vertical>.pg-button-danger:active,.btn-group-vertical>.pg-button-secondary:active,.btn-group-vertical>.pg-button-primary:active,.btn-group-vertical>.btn.active,.pg-breadcrumbs .btn-group-vertical>.btn.pg-breadcrumb-active,.btn-group-vertical>.active.pg-button-light,.pg-breadcrumbs .btn-group-vertical>.pg-button-light.pg-breadcrumb-active,.btn-group-vertical>.active.pg-button-danger,.pg-breadcrumbs .btn-group-vertical>.pg-button-danger.pg-breadcrumb-active,.btn-group-vertical>.active.pg-button-secondary,.pg-breadcrumbs .btn-group-vertical>.pg-button-secondary.pg-breadcrumb-active,.btn-group-vertical>.active.pg-button-primary,.pg-breadcrumbs .btn-group-vertical>.pg-button-primary.pg-breadcrumb-active{
  z-index:1
}
.pg-button-light,.btn-group>:not(.btn-check:first-child)+.pg-button-danger,.btn-group>:not(.btn-check:first-child)+.pg-button-secondary,.btn-group>:not(.btn-check:first-child)+.pg-button-primary,.btn-group>.btn-group:not(:first-child{
  margin-left:calc(var(--bs-border-width) * -1)
}
.pg-button-light:not(:last-child):not(.dropdown-toggle),.btn-group>.pg-button-danger:not(:last-child):not(.dropdown-toggle),.btn-group>.pg-button-secondary:not(:last-child):not(.dropdown-toggle),.btn-group>.pg-button-primary:not(:last-child):not(.dropdown-toggle),.btn-group>.btn.dropdown-toggle-split:first-child,.btn-group>.dropdown-toggle-split.pg-button-light:first-child,.btn-group>.dropdown-toggle-split.pg-button-danger:first-child,.btn-group>.dropdown-toggle-split.pg-button-secondary:first-child,.btn-group>.dropdown-toggle-split.pg-button-primary:first-child,.btn-group>.btn-group:not(:last-child)>.btn,.btn-group>.btn-group:not(:last-child)>.pg-button-light,.btn-group>.btn-group:not(:last-child)>.pg-button-danger,.btn-group>.btn-group:not(:last-child)>.pg-button-secondary,.btn-group>.btn-group:not(:last-child)>.pg-button-primary{
  border-top-right-radius:0;
  border-bottom-right-radius:0
}
.pg-button-light:nth-child(n+3),.btn-group>.pg-button-danger:nth-child(n+3),.btn-group>.pg-button-secondary:nth-child(n+3),.btn-group>.pg-button-primary:nth-child(n+3),.btn-group>:not(.btn-check)+.btn,.btn-group>:not(.btn-check)+.pg-button-light,.btn-group>:not(.btn-check)+.pg-button-danger,.btn-group>:not(.btn-check)+.pg-button-secondary,.btn-group>:not(.btn-check)+.pg-button-primary,.btn-group>.btn-group:not(:first-child)>.btn,.btn-group>.btn-group:not(:first-child)>.pg-button-light,.btn-group>.btn-group:not(:first-child)>.pg-button-danger,.btn-group>.btn-group:not(:first-child)>.pg-button-secondary,.btn-group>.btn-group:not(:first-child)>.pg-button-primary{
  border-top-left-radius:0;
  border-bottom-left-radius:0
}
.pg-button-light+.dropdown-toggle-split,.btn-group-sm>.pg-button-danger+.dropdown-toggle-split,.btn-group-sm>.pg-button-secondary+.dropdown-toggle-split,.btn-group-sm>.pg-button-primary+.dropdown-toggle-split{
  padding-right:.375rem;
  padding-left:.375rem
}
.pg-button-light+.dropdown-toggle-split,.btn-group-lg>.pg-button-danger+.dropdown-toggle-split,.btn-group-lg>.pg-button-secondary+.dropdown-toggle-split,.btn-group-lg>.pg-button-primary+.dropdown-toggle-split{
  padding-right:.75rem;
  padding-left:.75rem
}
.pg-button-light,.btn-group-vertical>.pg-button-danger,.btn-group-vertical>.pg-button-secondary,.btn-group-vertical>.pg-button-primary,.btn-group-vertical>.btn-group{
  width:100%
}
.pg-button-light:not(:first-child),.btn-group-vertical>.pg-button-danger:not(:first-child),.btn-group-vertical>.pg-button-secondary:not(:first-child),.btn-group-vertical>.pg-button-primary:not(:first-child),.btn-group-vertical>.btn-group:not(:first-child{
  margin-top:calc(var(--bs-border-width) * -1)
}
.pg-button-light:not(:last-child):not(.dropdown-toggle),.btn-group-vertical>.pg-button-danger:not(:last-child):not(.dropdown-toggle),.btn-group-vertical>.pg-button-secondary:not(:last-child):not(.dropdown-toggle),.btn-group-vertical>.pg-button-primary:not(:last-child):not(.dropdown-toggle),.btn-group-vertical>.btn-group:not(:last-child)>.btn,.btn-group-vertical>.btn-group:not(:last-child)>.pg-button-light,.btn-group-vertical>.btn-group:not(:last-child)>.pg-button-danger,.btn-group-vertical>.btn-group:not(:last-child)>.pg-button-secondary,.btn-group-vertical>.btn-group:not(:last-child)>.pg-button-primary{
  border-bottom-right-radius:0;
  border-bottom-left-radius:0
}
.pg-button-light~.btn,.btn-group-vertical>.btn~.pg-button-light,.btn-group-vertical>.pg-button-light~.pg-button-light,.btn-group-vertical>.pg-button-danger~.btn,.btn-group-vertical>.pg-button-danger~.pg-button-light,.btn-group-vertical>.btn~.pg-button-danger,.btn-group-vertical>.pg-button-light~.pg-button-danger,.btn-group-vertical>.pg-button-danger~.pg-button-danger,.btn-group-vertical>.pg-button-secondary~.btn,.btn-group-vertical>.pg-button-secondary~.pg-button-light,.btn-group-vertical>.pg-button-secondary~.pg-button-danger,.btn-group-vertical>.btn~.pg-button-secondary,.btn-group-vertical>.pg-button-light~.pg-button-secondary,.btn-group-vertical>.pg-button-danger~.pg-button-secondary,.btn-group-vertical>.pg-button-secondary~.pg-button-secondary,.btn-group-vertical>.pg-button-primary~.btn,.btn-group-vertical>.pg-button-primary~.pg-button-light,.btn-group-vertical>.pg-button-primary~.pg-button-danger,.btn-group-vertical>.pg-button-primary~.pg-button-secondary,.btn-group-vertical>.btn~.pg-button-primary,.btn-group-vertical>.pg-button-light~.pg-button-primary,.btn-group-vertical>.pg-button-danger~.pg-button-primary,.btn-group-vertical>.pg-button-secondary~.pg-button-primary,.btn-group-vertical>.pg-button-primary~.pg-button-primary,.btn-group-vertical>.btn-group:not(:first-child)>.btn,.btn-group-vertical>.btn-group:not(:first-child)>.pg-button-light,.btn-group-vertical>.btn-group:not(:first-child)>.pg-button-danger,.btn-group-vertical>.btn-group:not(:first-child)>.pg-button-secondary,.btn-group-vertical>.btn-group:not(:first-child)>.pg-button-primary{
  border-top-left-radius:0;
  border-top-right-radius:0
}
.pg-is-loading{
  color:var(--bs-nav-link-disabled-color);
  pointer-events:none;
  cursor:default
}
.pg-is-loading,.nav-tabs .nav-link:disabled{
  color:var(--bs-nav-link-disabled-color);
  background-color:rgba(0,0,0,0);
  border-color:rgba(0,0,0,0)
}
.pg-breadcrumbs .nav-link.pg-breadcrumb-active,.pg-breadcrumbs .nav-tabs .nav-link.pg-breadcrumb-active,.nav-tabs .nav-item.show .nav-link{
  color:var(--bs-nav-tabs-link-active-color);
  background-color:var(--bs-nav-tabs-link-active-bg);
  border-color:var(--bs-nav-tabs-link-active-border-color)
}
.pg-breadcrumbs .nav-link.pg-breadcrumb-active,.pg-breadcrumbs .nav-pills .nav-link.pg-breadcrumb-active,.nav-pills .show>.nav-link{
  color:var(--bs-nav-pills-link-active-color);
  background-color:var(--bs-nav-pills-link-active-bg)
}
.pg-breadcrumbs .nav-link.pg-breadcrumb-active,.pg-breadcrumbs .nav-underline .nav-link.pg-breadcrumb-active,.nav-underline .show>.nav-link{
  font-weight:700;
  color:var(--bs-nav-underline-link-active-color);
  border-bottom-color:currentcolor
}
.pg-breadcrumbs .tab-content>.pg-breadcrumb-active{
  display:block
}
.pg-breadcrumbs .nav-link.pg-breadcrumb-active,.pg-breadcrumbs .navbar-nav .nav-link.pg-breadcrumb-active,.navbar-nav .nav-link.show{
  color:var(--bs-navbar-active-color)
}
.pg-card{
  --bs-card-spacer-y: 1rem;
  --bs-card-spacer-x: 1rem;
  --bs-card-title-spacer-y: 0.5rem;
  --bs-card-title-color: ;
  --bs-card-subtitle-color: ;
  --bs-card-border-width: var(--bs-border-width);
  --bs-card-border-color: var(--bs-border-color-translucent);
  --bs-card-border-radius: var(--bs-border-radius);
  --bs-card-box-shadow: ;
  --bs-card-inner-border-radius: calc(var(--bs-border-radius) - (var(--bs-border-width)));
  --bs-card-cap-padding-y: 0.5rem;
  --bs-card-cap-padding-x: 1rem;
  --bs-card-cap-bg: rgba(var(--bs-body-color-rgb), 0.03);
  --bs-card-cap-color: ;
  --bs-card-height: ;
  --bs-card-color: ;
  --bs-card-bg: var(--bs-body-bg);
  --bs-card-img-overlay-padding: 1rem;
  --bs-card-group-margin: 0.75rem;
  position:relative;
  display:flex;
  flex-direction:column;
  min-width:0;
  height:var(--bs-card-height);
  color:var(--bs-body-color);
  word-wrap:break-word;
  background-color:var(--bs-card-bg);
  background-clip:border-box;
  border:var(--bs-card-border-width) solid var(--bs-card-border-color);
  border-radius:var(--bs-card-border-radius)
}
.pg-card>hr{
  margin-right:0;
  margin-left:0
}
.pg-card>.list-group{
  border-top:inherit;
  border-bottom:inherit
}
.pg-card>.list-group:first-child{
  border-top-width:0;
  border-top-left-radius:var(--bs-card-inner-border-radius);
  border-top-right-radius:var(--bs-card-inner-border-radius)
}
.pg-card>.list-group:last-child{
  border-bottom-width:0;
  border-bottom-right-radius:var(--bs-card-inner-border-radius);
  border-bottom-left-radius:var(--bs-card-inner-border-radius)
}
.pg-card>.card-header+.list-group,.card>.list-group+.card-footer,.pg-card>.list-group+.card-footer{
  border-top:0
}
.pg-card-body{
  flex:1 1 auto;
  padding:var(--bs-card-spacer-y) var(--bs-card-spacer-x);
  color:var(--bs-card-color)
}
.pg-breadcrumbs .nav-link.pg-breadcrumb-active,.pg-breadcrumbs .card-header-tabs .nav-link.pg-breadcrumb-active{
  background-color:var(--bs-card-bg);
  border-bottom-color:var(--bs-card-bg)
}
.pg-card-image,.card-img-bottom{
  width:100%
}
.pg-card-image{
  border-top-left-radius:var(--bs-card-inner-border-radius);
  border-top-right-radius:var(--bs-card-inner-border-radius)
}
.pg-card{
  margin-bottom:var(--bs-card-group-margin)
}
.pg-card{
  flex:1 0 0%;
  margin-bottom:0
}
.pg-card+.card,.card-group>.card+.pg-card,.card-group>.pg-card+.pg-card{
  margin-left:0;
  border-left:0
}
.pg-card:not(:last-child{
  border-top-right-radius:0;
  border-bottom-right-radius:0
}
.pg-card-image,.card-group>.pg-card:not(:last-child) .card-img-top,.card-group>.pg-card:not(:last-child) .pg-card-image,.card-group>.card:not(:last-child) .card-header,.card-group>.pg-card:not(:last-child) .card-header{
  border-top-right-radius:0
}
.pg-card:not(:last-child) .card-img-bottom,.card-group>.card:not(:last-child) .card-footer,.card-group>.pg-card:not(:last-child) .card-footer{
  border-bottom-right-radius:0
}
.pg-card:not(:first-child{
  border-top-left-radius:0;
  border-bottom-left-radius:0
}
.pg-card-image,.card-group>.pg-card:not(:first-child) .card-img-top,.card-group>.pg-card:not(:first-child) .pg-card-image,.card-group>.card:not(:first-child) .card-header,.card-group>.pg-card:not(:first-child) .card-header{
  border-top-left-radius:0
}
.pg-card:not(:first-child) .card-img-bottom,.card-group>.card:not(:first-child) .card-footer,.card-group>.pg-card:not(:first-child) .card-footer{
  border-bottom-left-radius:0
}
.pg-breadcrumbs{
  --bs-breadcrumb-padding-x: 0;
  --bs-breadcrumb-padding-y: 0;
  --bs-breadcrumb-margin-bottom: 1rem;
  --bs-breadcrumb-bg: ;
  --bs-breadcrumb-border-radius: ;
  --bs-breadcrumb-divider-color: var(--bs-secondary-color);
  --bs-breadcrumb-item-padding-x: 0.5rem;
  --bs-breadcrumb-item-active-color: var(--bs-secondary-color);
  display:flex;
  flex-wrap:wrap;
  padding:var(--bs-breadcrumb-padding-y) var(--bs-breadcrumb-padding-x);
  margin-bottom:var(--bs-breadcrumb-margin-bottom);
  font-size:var(--bs-breadcrumb-font-size);
  list-style:none;
  background-color:var(--bs-breadcrumb-bg);
  border-radius:var(--bs-breadcrumb-border-radius)
}
.pg-breadcrumbs li+.breadcrumb-item,.pg-breadcrumbs .breadcrumb-item+li,.pg-breadcrumbs li+li{
  padding-left:var(--bs-breadcrumb-item-padding-x)
}
.pg-breadcrumbs li+.breadcrumb-item::before,.pg-breadcrumbs .breadcrumb-item+li::before,.pg-breadcrumbs li+li::before{
  float:left;
  padding-right:var(--bs-breadcrumb-item-padding-x);
  color:var(--bs-breadcrumb-divider-color);
  content:var(--bs-breadcrumb-divider, "/")
  /* rtl: var(--bs-breadcrumb-divider, "/") */
}
.pg-breadcrumbs .breadcrumb-item.pg-breadcrumb-active,.pg-breadcrumbs li.active,.pg-breadcrumbs li.pg-breadcrumb-active{
  color:var(--bs-breadcrumb-item-active-color)
}
.pg-breadcrumbs .page-link.pg-breadcrumb-active,.active>.page-link,.pg-breadcrumbs .pg-breadcrumb-active>.page-link{
  z-index:3;
  color:var(--bs-pagination-active-color);
  background-color:var(--bs-pagination-active-bg);
  border-color:var(--bs-pagination-active-border-color)
}
.pg-is-loading,.disabled>.page-link,.pg-is-loading>.page-link{
  color:var(--bs-pagination-disabled-color);
  pointer-events:none;
  background-color:var(--bs-pagination-disabled-bg);
  border-color:var(--bs-pagination-disabled-border-color)
}
.pg-badge-success,.pg-badge-default{
  --bs-badge-padding-x: 0.65em;
  --bs-badge-padding-y: 0.35em;
  --bs-badge-font-size:0.75em;
  --bs-badge-font-weight: 700;
  --bs-badge-color: #fff;
  --bs-badge-border-radius: var(--bs-border-radius);
  display:inline-block;
  padding:var(--bs-badge-padding-y) var(--bs-badge-padding-x);
  font-size:var(--bs-badge-font-size);
  font-weight:var(--bs-badge-font-weight);
  line-height:1;
  color:var(--bs-badge-color);
  text-align:center;
  white-space:nowrap;
  vertical-align:baseline;
  border-radius:var(--bs-badge-border-radius)
}
.pg-badge-success:empty,.pg-badge-default:empty{
  display:none
}
.pg-badge-success,.btn .pg-badge-default,.pg-button-light .badge,.pg-button-light .pg-badge-success,.pg-button-light .pg-badge-default,.pg-button-danger .badge,.pg-button-danger .pg-badge-success,.pg-button-danger .pg-badge-default,.pg-button-secondary .badge,.pg-button-secondary .pg-badge-success,.pg-button-secondary .pg-badge-default,.pg-button-primary .badge,.pg-button-primary .pg-badge-success,.pg-button-primary .pg-badge-default{
  position:relative;
  top:-1px
}
.pg-is-loading,.list-group-item:disabled{
  color:var(--bs-list-group-disabled-color);
  pointer-events:none;
  background-color:var(--bs-list-group-disabled-bg)
}
.pg-breadcrumbs .list-group-item.pg-breadcrumb-active{
  z-index:2;
  color:var(--bs-list-group-active-color);
  background-color:var(--bs-list-group-active-bg);
  border-color:var(--bs-list-group-active-border-color)
}
.pg-breadcrumbs .list-group-item+.list-group-item.pg-breadcrumb-active{
  margin-top:calc(-1*var(--bs-list-group-border-width));
  border-top-width:var(--bs-list-group-border-width)
}
.pg-breadcrumbs .list-group-horizontal>.list-group-item.pg-breadcrumb-active{
  margin-top:0
}
.pg-breadcrumbs .list-group-horizontal>.list-group-item+.list-group-item.pg-breadcrumb-active{
  margin-left:calc(-1*var(--bs-list-group-border-width));
  border-left-width:var(--bs-list-group-border-width)
}
.pg-breadcrumbs .list-group-horizontal-sm>.list-group-item.pg-breadcrumb-active{
  margin-top:0
}
.pg-breadcrumbs .list-group-horizontal-sm>.list-group-item+.list-group-item.pg-breadcrumb-active{
  margin-left:calc(-1*var(--bs-list-group-border-width));
  border-left-width:var(--bs-list-group-border-width)
}
.pg-breadcrumbs .list-group-horizontal-md>.list-group-item.pg-breadcrumb-active{
  margin-top:0
}
.pg-breadcrumbs .list-group-horizontal-md>.list-group-item+.list-group-item.pg-breadcrumb-active{
  margin-left:calc(-1*var(--bs-list-group-border-width));
  border-left-width:var(--bs-list-group-border-width)
}
.pg-breadcrumbs .list-group-horizontal-lg>.list-group-item.pg-breadcrumb-active{
  margin-top:0
}
.pg-breadcrumbs .list-group-horizontal-lg>.list-group-item+.list-group-item.pg-breadcrumb-active{
  margin-left:calc(-1*var(--bs-list-group-border-width));
  border-left-width:var(--bs-list-group-border-width)
}
.pg-breadcrumbs .list-group-horizontal-xl>.list-group-item.pg-breadcrumb-active{
  margin-top:0
}
.pg-breadcrumbs .list-group-horizontal-xl>.list-group-item+.list-group-item.pg-breadcrumb-active{
  margin-left:calc(-1*var(--bs-list-group-border-width));
  border-left-width:var(--bs-list-group-border-width)
}
.pg-breadcrumbs .list-group-horizontal-xxl>.list-group-item.pg-breadcrumb-active{
  margin-top:0
}
.pg-breadcrumbs .list-group-horizontal-xxl>.list-group-item+.list-group-item.pg-breadcrumb-active{
  margin-left:calc(-1*var(--bs-list-group-border-width));
  border-left-width:var(--bs-list-group-border-width)
}
.pg-is-loading{
  pointer-events:none;
  user-select:none;
  opacity:var(--bs-btn-close-disabled-opacity)
}
.pg-breadcrumbs .carousel-item.pg-breadcrumb-active,.carousel-item-next,.carousel-item-prev{
  display:block
}
.pg-breadcrumbs .carousel-item-end.pg-breadcrumb-active{
  transform:translateX(100%)
}
.pg-breadcrumbs .carousel-item-start.pg-breadcrumb-active{
  transform:translateX(-100%)
}
.pg-breadcrumbs .carousel-item.pg-breadcrumb-active,.pg-breadcrumbs .carousel-fade .carousel-item.pg-breadcrumb-active,.carousel-fade .carousel-item-next.carousel-item-start,.carousel-fade .carousel-item-prev.carousel-item-end{
  z-index:1;
  opacity:1
}
.pg-breadcrumbs .carousel-item-start.pg-breadcrumb-active,.pg-breadcrumbs .carousel-fade .carousel-item-start.pg-breadcrumb-active,.carousel-fade .active.carousel-item-end,.carousel-fade .pg-breadcrumbs .carousel-item-end.pg-breadcrumb-active,.pg-breadcrumbs .carousel-fade .carousel-item-end.pg-breadcrumb-active{
  z-index:0;
  opacity:0;
  transition:opacity 0s .6s
}
.pg-breadcrumbs .carousel-item-start.pg-breadcrumb-active,.pg-breadcrumbs .carousel-fade .carousel-item-start.pg-breadcrumb-active,.carousel-fade .active.carousel-item-end,.carousel-fade .pg-breadcrumbs .carousel-item-end.pg-breadcrumb-active,.pg-breadcrumbs .carousel-fade .carousel-item-end.pg-breadcrumb-active{
  transition:none
}
.pg-breadcrumbs .pg-breadcrumb-active,.pg-breadcrumbs .carousel-indicators .pg-breadcrumb-active{
  opacity:1
}
.pg-badge-default{
  color:#fff !important;
  background-color:RGBA(108, 117, 125, var(--bs-bg-opacity, 1)) !important
}
.pg-badge-success{
  color:#fff !important;
  background-color:RGBA(25, 135, 84, var(--bs-bg-opacity, 1)) !important
}
.pg-ratio-3x2{
  position:relative;
  width:100%
}
.pg-ratio-3x2::before{
  display:block;
  padding-top:var(--bs-aspect-ratio);
  content:""
}
.pg-ratio-3x2{
  position:absolute;
  top:0;
  left:0;
  width:100%;
  height:100%
}
.pg-table{
  vertical-align:middle !important
}
.pg-chat-pane{
  overflow:auto !important
}
.pg-inline-buttons,.pg-chat-input-bar,.pg-chat-message,.pg-chat-message-user,.pg-chat-message-system,.pg-chat-pane,.pg-chat-wrapper{
  display:flex !important
}
.pg-hidden-mobile-inline{
  display:none !important
}
.pg-card,.app-card{
  box-shadow:0 .125rem .25rem rgba(0,0,0,.075) !important
}
.pg-chat-input-bar{
  border-top:var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important
}
.pg-chat-pane,.pg-chat-wrapper{
  flex-direction:column !important
}
.pg-columns-reversed{
  flex-direction:row-reverse !important
}
.pg-chat-pane{
  flex-grow:1 !important
}
.pg-justify-content-end{
  justify-content:flex-end !important
}
.pg-align-items-center{
  align-items:center !important
}
.pg-message-contents{
  margin-right:.25rem !important;
  margin-left:.25rem !important
}
.pg-message-contents p{
  margin-top:0 !important;
  margin-bottom:0 !important
}
.pg-chat-message,.pg-chat-message-user,.pg-chat-message-system{
  margin-top:.5rem !important;
  margin-bottom:.5rem !important
}
.pg-input-group{
  margin-bottom:1rem !important
}
.pg-ml{
  margin-left:.25rem !important
}
.pg-chat-input-bar,.pg-chat-message,.pg-chat-message-user,.pg-chat-message-system{
  padding:.5rem !important
}
.pg-chat-pane{
  padding:1.5rem !important
}
.pg-text-left{
  text-align:left !important
}
.pg-text-right{
  text-align:right !important
}
.pg-text-centered,.pg-chat-icon{
  text-align:center !important
}
.pg-text-primary{
  --bs-text-opacity: 1;
  color:rgba(var(--bs-primary-rgb), var(--bs-text-opacity)) !important
}
.pg-text-success{
  --bs-text-opacity: 1;
  color:rgba(var(--bs-success-rgb), var(--bs-text-opacity)) !important
}
.pg-text-info{
  --bs-text-opacity: 1;
  color:rgba(var(--bs-info-rgb), var(--bs-text-opacity)) !important
}
.pg-text-danger{
  --bs-text-opacity: 1;
  color:rgba(var(--bs-danger-rgb), var(--bs-text-opacity)) !important
}
.pg-text-muted{
  --bs-text-opacity: 1;
  color:var(--bs-secondary-color) !important
}
.pg-chat-message,.pg-chat-message-user,.pg-chat-message-system{
  border-radius:var(--bs-border-radius-lg) !important
}
.pg-badge-success,.pg-badge-default{
  border-radius:var(--bs-border-radius-pill) !important
}
.pg-hidden-mobile-inline{
  display:inline-block !important
}
.pg-chat-wrapper{
  height:100vh
}
.pg-chat-icon svg{
  width:1.5rem;
  height:1.5rem
}
.pg-chat-message-system{
  background-color:#dfdfdf;
  color:#212529
}
.pg-chat-message-user{
  background-color:#0b0b0b;
  color:#fff
}
.pg-icon{
  display:inline-flex;
  align-items:center;
  justify-content:center;
  height:1.5rem;
  width:1.5rem
}
.pg-table .td,.pg-table .th{
  padding:.5rem .5rem;
  border-bottom:var(--bs-border-width) solid var(--bs-border-color)
}
.pg-table .th{
  font-weight:bold
}
.pg-ratio-3x2{
  --bs-aspect-ratio: 66.6666%
}
