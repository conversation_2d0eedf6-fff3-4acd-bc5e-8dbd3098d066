FROM python:3.11-slim-buster
ENV PYTHONUNBUFFERED=1

# Install only required system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
  gettext \
  && rm -rf /var/lib/apt/lists/*

# Create and set work directory
WORKDIR /code

# Install Python dependencies first to leverage Docker cache
COPY requirements.txt requirements.txt
COPY requirements/ requirements/
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . /code/

# Create required directories and set permissions
RUN mkdir -p /code/static_root /code/static /var/www && \
  chmod +x /code/deploy/docker_startup_do.sh && \
  chown -R www-data:www-data /code

# Switch to non-root user
USER www-data
