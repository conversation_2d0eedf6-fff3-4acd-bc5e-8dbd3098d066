# This file controls the behavior of Trunk: https://docs.trunk.io/cli
# To learn more about the format of this file, see https://docs.trunk.io/reference/trunk-yaml
version: 0.1
cli:
  version: 1.19.0
# Trunk provides extensibility via plugins. (https://docs.trunk.io/plugins)
plugins:
  sources:
    - id: trunk
      ref: v1.4.2
      uri: https://github.com/trunk-io/plugins
# Many linters and tools depend on runtimes - configure them here. (https://docs.trunk.io/runtimes)
runtimes:
  enabled:
    - go@1.21.0
    - node@18.12.1
    - python@3.10.8
# This is the section where you manage your linters. (https://docs.trunk.io/check/configuration)
lint:
  enabled:
    - actionlint@1.6.26
    - bandit@1.7.7
    - black@24.1.1
    - checkov@3.2.19
    - git-diff-check
    - hadolint@2.12.0
    - isort@5.13.2
    - markdownlint@0.39.0
    - osv-scanner@1.6.2
    - oxipng@9.0.0
    - prettier@3.2.5
    - ruff@0.2.1
    - shellcheck@0.9.0
    - shfmt@3.6.0
    - svgo@3.2.0
    - taplo@0.8.1
    - terrascan@1.18.11
    - trivy@0.49.1
    - trufflehog@3.67.5
    - yamllint@1.34.0
actions:
  disabled:
    - trunk-announce
    - trunk-check-pre-push
    - trunk-fmt-pre-commit
  enabled:
    - trunk-upgrade-available
