services:
  db:
    image: postgres
    # persist data beyond lifetime of container
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_DB=homescore
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    healthcheck:
      test: pg_isready -d $${POSTGRES_DB} -U $${POSTGRES_USER}
      interval: 2s
      retries: 10
    ports:
      - "5432:5432"
  redis:
    image: redis
    # persistent storage
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: bash -c 'exec 6<>/dev/tcp/redis/6379'
      interval: 2s
      retries: 10
  web:
    platform: linux/amd64
    build:
      context: .
      dockerfile: Dockerfile.dev
    command: python manage.py runserver 0.0.0.0:8000
    volumes:
      - .:/code
    ports:
      - "8000:8000"
    env_file:
      - ./.env.docker
    restart: unless-stopped
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
  celery:
    platform: linux/amd64
    build:
      context: .
      dockerfile: Dockerfile.dev
    command: celery -A homescore worker -l INFO --concurrency 2 -Q default
    volumes:
      - .:/code
    env_file:
      - ./.env.docker
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy

  celery-task-queue:
    platform: linux/amd64
    build:
      context: .
      dockerfile: Dockerfile.dev
    command: celery -A homescore worker -l INFO --concurrency 2 -Q report_task
    volumes:
      - .:/code
    env_file:
      - ./.env.docker
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
  
  celery-image-queue:
    platform: linux/amd64
    build:
      context: .
      dockerfile: Dockerfile.dev
    command: celery -A homescore worker -l INFO --concurrency 2 -Q image_task
    volumes:
      - .:/code
    env_file:
      - ./.env.docker
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy

volumes:
  postgres_data:
  redis_data:
