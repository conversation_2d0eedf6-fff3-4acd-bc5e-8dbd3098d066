#!/usr/bin/env python3
"""
Test script to verify the extract_tool_response function behavior
"""

import sys
import os
import django

# Add the project root to the Python path
sys.path.insert(0, '/Users/<USER>/Documents/job_related/projects/homescore_app/homescore')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'homescore.settings')
django.setup()

from apps.checker.helpers import extract_tool_response
import logging

# Set up logging to see the output
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger("homescore")

# Mock response with no tool calls (simulating room summary scenario)
mock_response_no_tools = {
    "choices": [
        {
            "message": {
                "content": "This is a regular text response without tool calls",
                "tool_calls": []
            }
        }
    ]
}

# Mock response with tool calls (simulating image processing scenario)
mock_response_with_tools = {
    "choices": [
        {
            "message": {
                "tool_calls": [
                    {
                        "function": {
                            "arguments": '{"test": "success"}'
                        }
                    }
                ]
            }
        }
    ]
}

print("Testing extract_tool_response function...")
print("=" * 50)

print("\n1. Testing with 'critical' context (should log ERROR for no tool calls):")
result1 = extract_tool_response(mock_response_no_tools, context="critical")
print(f"Result: {result1}")

print("\n2. Testing with 'optional' context (should log DEBUG for no tool calls):")
result2 = extract_tool_response(mock_response_no_tools, context="optional")
print(f"Result: {result2}")

print("\n3. Testing with tool calls present (should work normally):")
result3 = extract_tool_response(mock_response_with_tools, context="critical")
print(f"Result: {result3}")

print("\n4. Testing with tool calls present and optional context:")
result4 = extract_tool_response(mock_response_with_tools, context="optional")
print(f"Result: {result4}")

print("\nTest completed!")
