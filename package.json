{"name": "homescore", "version": "1.0.0", "description": "", "main": "webpack.config.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "NODE_ENV=production webpack --mode production", "dev": "webpack --mode development", "dev-watch": "webpack --mode development --watch", "type-check": "tsc --noEmit", "type-check-watch": "npm run type-check -- --watch"}, "author": "", "homepage": "https://homescore.co/", "devDependencies": {"@babel/cli": "^7.21.0", "@babel/compat-data": "^7.21.4", "@babel/core": "^7.21.4", "@babel/preset-env": "^7.21.4", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.21.4", "babel-loader": "^9.1.2", "css-loader": "^6.7.3", "mini-css-extract-plugin": "^2.7.5", "sass": "^1.62.0", "sass-loader": "^13.2.2", "style-loader": "^3.3.2", "typescript": "^5.0.4", "webpack": "^5.79.0", "webpack-cli": "^5.0.1"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/react-fontawesome": "^0.2.0", "@popperjs/core": "^2.11.7", "alpinejs": "^3.12.0", "bootstrap": "^5.3.2", "chart.js": "^4.4.0", "chartjs": "^0.3.24", "intro.js": "^7.2.0", "js-cookie": "^3.0.1", "react": "^18.2.0", "react-bootstrap": "^2.9.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-google-autocomplete": "^2.7.3", "react-router-dom": "^6.10.0", "react-star-ratings": "^2.3.0", "react-tabs": "^6.0.2", "recharts": "^2.8.0"}}