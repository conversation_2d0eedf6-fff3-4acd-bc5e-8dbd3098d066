previews:
  generation: manual
services:
  # Server
  - type: web
    name: homescore
    env: python
    buildCommand: "./deploy/build.sh"
    startCommand: "gunicorn --bind 0.0.0.0:$PORT --workers 1 --threads 8 --timeout 0 homescore.wsgi:application"
    region: oregon
    plan: standard
    numInstances: 1
    envVars:
      - fromGroup: Azure Open AI
      - key: PYTHON_VERSION
        value: 3.11.3
      - key: DJANGO_SETTINGS_MODULE
        value: homescore.settings_production
      - key: DJANGO_DATABASE_NAME
        fromDatabase:
          name: homescore
          property: database
      - key: DJANGO_DATABASE_USER
        fromDatabase:
          name: homescore
          property: user
      - key: DJANGO_DATABASE_PASSWORD
        fromDatabase:
          name: homescore
          property: password
      - key: DJANGO_DATABASE_HOST
        fromDatabase:
          name: homescore
          property: host
      - key: DJANGO_DATABASE_PORT
        fromDatabase:
          name: homescore
          property: port
      - key: REDIS_HOST
        fromService:
          type: redis
          name: homescore-redis
          property: host
      - key: REDIS_PORT
        fromService:
          type: redis
          name: homescore-redis
          property: port
      - key: SECRET_KEY
        sync: false
      - key: GOOGLE_ANALYTICS_ID
        sync: false
      - key: STRIPE_LIVE_MODE
        value: false
      - key: STRIPE_LIVE_PUBLIC_KEY
        sync: false
      - key: STRIPE_LIVE_SECRET_KEY
        sync: false
      - key: DJSTRIPE_WEBHOOK_SECRET
        sync: false
      - key: SUPABASE_URL
        sync: false
      - key: SUPABASE_KEY
        sync: false
      - key: REPORT_BUCKET
        value: reports
      - key: FR_ENDPOINT
        value: https://homescore-report-analysis.cognitiveservices.azure.com/
      - key: FR_KEY
        sync: false
      - key: PINECONE_API_KEY
        sync: false
      - key: PINECONE_ENV
        sync: false
      - key: PINECONE_INDEX
        sync: false
      - key: ANYMAIL_SENDGRID_API_KEY
        sync: false
      - key: GOOGLE_API_KEY
        sync: false
      - key: APIFY_KEY
        sync: false
      - key: SENTRY_DSN
        sync: false
      - key: SENTRY_ENV
        sync: false
# Celery task worker (not available on 'free' plan)
  - type: worker
    name: homescore_celery
    env: python
    buildCommand: "./deploy/build_celery.sh"
    startCommand: celery -A homescore worker -l INFO --beat --concurrency 2 -Q default
    region: oregon
    plan: standard
    numInstances: 1
    envVars:
      - fromGroup: Azure Open AI
      - key: PYTHON_VERSION
        value: 3.11.3
      - key: DJANGO_SETTINGS_MODULE
        value: homescore.settings_production
      - key: DJANGO_DATABASE_NAME
        fromDatabase:
          name: homescore
          property: database
      - key: DJANGO_DATABASE_USER
        fromDatabase:
          name: homescore
          property: user
      - key: DJANGO_DATABASE_PASSWORD
        fromDatabase:
          name: homescore
          property: password
      - key: DJANGO_DATABASE_HOST
        fromDatabase:
          name: homescore
          property: host
      - key: DJANGO_DATABASE_PORT
        fromDatabase:
          name: homescore
          property: port
      - key: REDIS_HOST
        fromService:
          type: redis
          name: homescore-redis
          property: host
      - key: REDIS_PORT
        fromService:
          type: redis
          name: homescore-redis
          property: port
      - key: SUPABASE_URL
        fromService:
          name: homescore
          type: web
          envVarKey: SUPABASE_URL
      - key: SUPABASE_KEY
        fromService:
          name: homescore
          type: web
          envVarKey: SUPABASE_KEY
      - key: FR_ENDPOINT
        fromService:
          name: homescore
          type: web
          envVarKey: FR_ENDPOINT
      - key: FR_KEY
        fromService:
          name: homescore
          type: web
          envVarKey: FR_KEY
      - key: PINECONE_API_KEY
        fromService:
          name: homescore
          type: web
          envVarKey: PINECONE_API_KEY
      - key: PINECONE_ENV
        fromService:
          name: homescore
          type: web
          envVarKey: PINECONE_ENV
      - key: PINECONE_INDEX
        fromService:
          name: homescore
          type: web
          envVarKey: PINECONE_INDEX
      - key: ANYMAIL_SENDGRID_API_KEY
        fromService:
          name: homescore
          type: web
          envVarKey: ANYMAIL_SENDGRID_API_KEY
      - key: GOOGLE_API_KEY
        fromService:
          name: homescore
          type: web
          envVarKey: GOOGLE_API_KEY
      - key: APIFY_KEY
        fromService:
          name: homescore
          type: web
          envVarKey: APIFY_KEY
      - key: SENTRY_DSN
        fromService:
          name: homescore
          type: web
          envVarKey: SENTRY_DSN
      - key: SENTRY_ENV
        fromService:
          name: homescore
          type: web
          envVarKey: SENTRY_ENV
      - key: SECRET_KEY
        sync: false
      - key: GOOGLE_ANALYTICS_ID
        sync: false
      - key: STRIPE_LIVE_PUBLIC_KEY
        sync: false
      - key: STRIPE_LIVE_SECRET_KEY
        sync: false
      - key: DJSTRIPE_WEBHOOK_SECRET
        sync: false
# Celery for interactive tasks
  - type: worker
    name: homescore_batch_celery
    env: python
    buildCommand: "./deploy/build_celery.sh"
    startCommand: celery -A homescore worker -l INFO --beat --concurrency 2 -Q report_task
    region: oregon
    plan: standard
    numInstances: 1
    envVars:
      - fromGroup: Azure Open AI
      - key: PYTHON_VERSION
        value: 3.11.3
      - key: DJANGO_SETTINGS_MODULE
        value: homescore.settings_production
      - key: DJANGO_DATABASE_NAME
        fromDatabase:
          name: homescore
          property: database
      - key: DJANGO_DATABASE_USER
        fromDatabase:
          name: homescore
          property: user
      - key: DJANGO_DATABASE_PASSWORD
        fromDatabase:
          name: homescore
          property: password
      - key: DJANGO_DATABASE_HOST
        fromDatabase:
          name: homescore
          property: host
      - key: DJANGO_DATABASE_PORT
        fromDatabase:
          name: homescore
          property: port
      - key: REDIS_HOST
        fromService:
          type: redis
          name: homescore-redis
          property: host
      - key: REDIS_PORT
        fromService:
          type: redis
          name: homescore-redis
          property: port
      - key: SUPABASE_URL
        fromService:
          name: homescore
          type: web
          envVarKey: SUPABASE_URL
      - key: SUPABASE_KEY
        fromService:
          name: homescore
          type: web
          envVarKey: SUPABASE_KEY
      - key: FR_ENDPOINT
        fromService:
          name: homescore
          type: web
          envVarKey: FR_ENDPOINT
      - key: FR_KEY
        fromService:
          name: homescore
          type: web
          envVarKey: FR_KEY
      - key: PINECONE_API_KEY
        fromService:
          name: homescore
          type: web
          envVarKey: PINECONE_API_KEY
      - key: PINECONE_ENV
        fromService:
          name: homescore
          type: web
          envVarKey: PINECONE_ENV
      - key: PINECONE_INDEX
        fromService:
          name: homescore
          type: web
          envVarKey: PINECONE_INDEX
      - key: ANYMAIL_SENDGRID_API_KEY
        fromService:
          name: homescore
          type: web
          envVarKey: ANYMAIL_SENDGRID_API_KEY
      - key: GOOGLE_API_KEY
        fromService:
          name: homescore
          type: web
          envVarKey: GOOGLE_API_KEY
      - key: APIFY_KEY
        fromService:
          name: homescore
          type: web
          envVarKey: APIFY_KEY
      - key: SENTRY_DSN
        fromService:
          name: homescore
          type: web
          envVarKey: SENTRY_DSN
      - key: SENTRY_ENV
        fromService:
          name: homescore
          type: web
          envVarKey: SENTRY_ENV
      - key: SECRET_KEY
        sync: false
      - key: GOOGLE_ANALYTICS_ID
        sync: false
      - key: STRIPE_LIVE_PUBLIC_KEY
        sync: false
      - key: STRIPE_LIVE_SECRET_KEY
        sync: false
      - key: DJSTRIPE_WEBHOOK_SECRET
        sync: false


# Celery for image processing tasks
  - type: worker
    name: homescore_batch_images
    env: python
    buildCommand: "./deploy/build_celery.sh"
    startCommand: celery -A homescore worker -l INFO --beat --concurrency 2 -Q image_task
    region: oregon
    plan: standard
    numInstances: 1
    envVars:
      - fromGroup: Azure Open AI
      - key: PYTHON_VERSION
        value: 3.11.3
      - key: DJANGO_SETTINGS_MODULE
        value: homescore.settings_production
      - key: DJANGO_DATABASE_NAME
        fromDatabase:
          name: homescore
          property: database
      - key: DJANGO_DATABASE_USER
        fromDatabase:
          name: homescore
          property: user
      - key: DJANGO_DATABASE_PASSWORD
        fromDatabase:
          name: homescore
          property: password
      - key: DJANGO_DATABASE_HOST
        fromDatabase:
          name: homescore
          property: host
      - key: DJANGO_DATABASE_PORT
        fromDatabase:
          name: homescore
          property: port
      - key: REDIS_HOST
        fromService:
          type: redis
          name: homescore-redis
          property: host
      - key: REDIS_PORT
        fromService:
          type: redis
          name: homescore-redis
          property: port
      - key: SUPABASE_URL
        fromService:
          name: homescore
          type: web
          envVarKey: SUPABASE_URL
      - key: SUPABASE_KEY
        fromService:
          name: homescore
          type: web
          envVarKey: SUPABASE_KEY
      - key: FR_ENDPOINT
        fromService:
          name: homescore
          type: web
          envVarKey: FR_ENDPOINT
      - key: FR_KEY
        fromService:
          name: homescore
          type: web
          envVarKey: FR_KEY
      - key: PINECONE_API_KEY
        fromService:
          name: homescore
          type: web
          envVarKey: PINECONE_API_KEY
      - key: PINECONE_ENV
        fromService:
          name: homescore
          type: web
          envVarKey: PINECONE_ENV
      - key: PINECONE_INDEX
        fromService:
          name: homescore
          type: web
          envVarKey: PINECONE_INDEX
      - key: ANYMAIL_SENDGRID_API_KEY
        fromService:
          name: homescore
          type: web
          envVarKey: ANYMAIL_SENDGRID_API_KEY
      - key: GOOGLE_API_KEY
        fromService:
          name: homescore
          type: web
          envVarKey: GOOGLE_API_KEY
      - key: APIFY_KEY
        fromService:
          name: homescore
          type: web
          envVarKey: APIFY_KEY
      - key: SENTRY_DSN
        fromService:
          name: homescore
          type: web
          envVarKey: SENTRY_DSN
      - key: SENTRY_ENV
        fromService:
          name: homescore
          type: web
          envVarKey: SENTRY_ENV
      - key: SECRET_KEY
        sync: false
      - key: GOOGLE_ANALYTICS_ID
        sync: false
      - key: STRIPE_LIVE_PUBLIC_KEY
        sync: false
      - key: STRIPE_LIVE_SECRET_KEY
        sync: false
      - key: DJSTRIPE_WEBHOOK_SECRET
        sync: false


# Redis instance (for celery and caching)
  - type: redis
    name: homescore-redis
    ipAllowList: []
    plan: standard
    region: oregon

  # Server
  - type: web
    name: homescore-east
    env: python
    buildCommand: "./deploy/build.sh"
    startCommand: "gunicorn --bind 0.0.0.0:$PORT --workers 1 --threads 8 --timeout 0 homescore.wsgi:application"
    region: virginia
    plan: standard
    numInstances: 1
    envVars:
      - fromGroup: Azure Open AI
      - fromGroup: Production_env_variables
      - key: PYTHON_VERSION
        value: 3.11.3
      - key: DJANGO_SETTINGS_MODULE
        value: homescore.settings_production
      - key: DJANGO_DATABASE_NAME
        fromDatabase:
          name: homescore-east
          property: database
      - key: DJANGO_DATABASE_USER
        fromDatabase:
          name: homescore-east
          property: user
      - key: DJANGO_DATABASE_PASSWORD
        fromDatabase:
          name: homescore-east
          property: password
      - key: DJANGO_DATABASE_HOST
        fromDatabase:
          name: homescore-east
          property: host
      - key: DJANGO_DATABASE_PORT
        fromDatabase:
          name: homescore-east
          property: port
      - key: REDIS_HOST
        fromService:
          type: redis
          name: homescore-redis-east
          property: host
      - key: REDIS_PORT
        fromService:
          type: redis
          name: homescore-redis-east
          property: port
# Celery task worker (not available on 'free' plan)
  - type: worker
    name: homescore_celery-east
    env: python
    buildCommand: "./deploy/build_celery.sh"
    startCommand: celery -A homescore worker -l INFO --beat --concurrency 2 -Q default
    region: virginia
    plan: standard
    numInstances: 1
    envVars:
      - fromGroup: Azure Open AI
      - fromGroup: Production_env_variables
      - key: PYTHON_VERSION
        value: 3.11.3
      - key: DJANGO_SETTINGS_MODULE
        value: homescore.settings_production
      - key: DJANGO_DATABASE_NAME
        fromDatabase:
          name: homescore-east
          property: database
      - key: DJANGO_DATABASE_USER
        fromDatabase:
          name: homescore-east
          property: user
      - key: DJANGO_DATABASE_PASSWORD
        fromDatabase:
          name: homescore-east
          property: password
      - key: DJANGO_DATABASE_HOST
        fromDatabase:
          name: homescore-east
          property: host
      - key: DJANGO_DATABASE_PORT
        fromDatabase:
          name: homescore-east
          property: port
      - key: REDIS_HOST
        fromService:
          type: redis
          name: homescore-redis-east
          property: host
      - key: REDIS_PORT
        fromService:
          type: redis
          name: homescore-redis-east
          property: port

# Celery for interactive tasks
  - type: worker
    name: homescore_batch_celery-east
    env: python
    buildCommand: "./deploy/build_celery.sh"
    startCommand: celery -A homescore worker -l INFO --beat --concurrency 2 -Q report_task
    region: virginia
    plan: standard
    numInstances: 1
    envVars:
      - fromGroup: Azure Open AI
      - fromGroup: Production_env_variables
      - key: PYTHON_VERSION
        value: 3.11.3
      - key: DJANGO_SETTINGS_MODULE
        value: homescore.settings_production
      - key: DJANGO_DATABASE_NAME
        fromDatabase:
          name: homescore-east
          property: database
      - key: DJANGO_DATABASE_USER
        fromDatabase:
          name: homescore-east
          property: user
      - key: DJANGO_DATABASE_PASSWORD
        fromDatabase:
          name: homescore-east
          property: password
      - key: DJANGO_DATABASE_HOST
        fromDatabase:
          name: homescore-east
          property: host
      - key: DJANGO_DATABASE_PORT
        fromDatabase:
          name: homescore-east
          property: port
      - key: REDIS_HOST
        fromService:
          type: redis
          name: homescore-redis-east
          property: host
      - key: REDIS_PORT
        fromService:
          type: redis
          name: homescore-redis-east
          property: port


# Celery for image processing tasks
  - type: worker
    name: homescore_batch_images-east
    env: python
    buildCommand: "./deploy/build_celery.sh"
    startCommand: celery -A homescore worker -l INFO --beat --concurrency 2 -Q image_task
    region: virginia
    plan: standard
    numInstances: 1
    envVars:
      - fromGroup: Azure Open AI
      - fromGroup: Production_env_variables
      - key: PYTHON_VERSION
        value: 3.11.3
      - key: DJANGO_SETTINGS_MODULE
        value: homescore.settings_production
      - key: DJANGO_DATABASE_NAME
        fromDatabase:
          name: homescore-east
          property: database
      - key: DJANGO_DATABASE_USER
        fromDatabase:
          name: homescore-east
          property: user
      - key: DJANGO_DATABASE_PASSWORD
        fromDatabase:
          name: homescore-east
          property: password
      - key: DJANGO_DATABASE_HOST
        fromDatabase:
          name: homescore-east
          property: host
      - key: DJANGO_DATABASE_PORT
        fromDatabase:
          name: homescore-east
          property: port
      - key: REDIS_HOST
        fromService:
          type: redis
          name: homescore-redis-east
          property: host
      - key: REDIS_PORT
        fromService:
          type: redis
          name: homescore-redis-east
          property: port



# Redis instance (for celery and caching)
  - type: redis
    name: homescore-redis-east
    ipAllowList: []
    plan: standard
    region: virginia

# Postgres database
databases:
  - name: homescore
    plan: standard
    previewPlan: basic-1gb
    databaseName: homescore
    user: homescore
    ipAllowList: [] # only allow internal connections
    region: oregon
  
  - name: homescore-east
    plan: basic-1gb
    previewPlan: basic-1gb
    databaseName: homescore
    user: homescore
    ipAllowList: [] # only allow internal connections
    region: virginia