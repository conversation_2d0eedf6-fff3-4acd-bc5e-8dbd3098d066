#!/bin/bash

set -o errexit
set -o pipefail
set -o nounset

# Configuration
MAX_RETRIES=30        # Maximum number of retries
RETRY_INTERVAL=2      # Seconds between retries
TIMEOUT=$((MAX_RETRIES * RETRY_INTERVAL))  # Total timeout in seconds
COLLECT_STATIC=${DJANGO_COLLECT_STATIC:-false}  # Default to not collecting static files since frontend is separate

# Function to check if required environment variables are set
check_required_env() {
    local required_vars=(
        "DATABASE_URL"
        "REDIS_URL"
        "DJANGO_SETTINGS_MODULE"
        "PORT"
    )

    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            echo "Error: Required environment variable $var is not set"
            exit 1
        fi
    done

    # Print important environment variables for debugging
    local cluster_name=$(echo "$DATABASE_URL" | sed -E 's/.*@([^:]+):.*/\1/')
    local db_name=$(echo "$DATABASE_URL" | sed -E 's/.*\/([^\/]+)\?.*/\1/')
    echo "DATABASE_URL is set (cluster: $cluster_name, db: $db_name)"
    local redis_cluster=$(echo "$REDIS_URL" | sed -E 's/.*@([^:]+):.*/\1/')
    echo "REDIS_URL is set (cluster: $redis_cluster)"
    echo "DJANGO_SETTINGS_MODULE: $DJANGO_SETTINGS_MODULE"
    echo "PORT: $PORT"
}

# Function to wait for database with timeout and retry mechanism
wait_for_db() {
    echo "Waiting for database... (timeout: ${TIMEOUT}s)"
    local retries=0
    local start_time=$(date +%s)

    while true; do
        if python manage.py check --database default > /dev/null 2>&1; then
            echo "Database is ready!"
            return 0
        fi

        retries=$((retries + 1))
        current_time=$(date +%s)
        elapsed=$((current_time - start_time))

        if [ $retries -ge $MAX_RETRIES ] || [ $elapsed -ge $TIMEOUT ]; then
            echo "Error: Database connection timeout after ${elapsed} seconds (${retries} attempts)"
            echo "Please check:"
            echo "  1. Database container is running"
            echo "  2. Database URL is correct"
            echo "  3. Network connectivity"
            echo "  4. Database service is accepting connections"
            return 1
        fi

        echo "Attempt ${retries}/${MAX_RETRIES} failed. Retrying in ${RETRY_INTERVAL} seconds..."
        sleep $RETRY_INTERVAL
    done
}

# Function to perform database migration with timeout
run_migrations() {
    echo "Running database migrations..."
    if timeout ${TIMEOUT} python manage.py migrate --noinput; then
        echo "Migrations completed successfully"
    else
        local exit_code=$?
        echo "Migration failed with exit code: ${exit_code}"
        if [ $exit_code -eq 124 ]; then
            echo "Error: Migration timed out after ${TIMEOUT} seconds"
        fi
        exit 1
    fi
}

# Function to collect static files with proper error handling
collect_static() {
    echo "Collecting static files..."
    # Clean the static root directory first
    rm -rf /code/static_root/*

    # Collect static files with timeout
    if ! timeout ${TIMEOUT} python manage.py collectstatic --noinput --clear; then
        local exit_code=$?
        echo "Static file collection failed!"
        if [ $exit_code -eq 124 ]; then
            echo "Error: Static file collection timed out after ${TIMEOUT} seconds"
        fi
        # Don't exit on static file collection failure since frontend is separate
        echo "WARNING: Static file collection failed, but continuing since frontend is separate"
        return 1
    fi
    echo "Static files collected successfully"

    # Verify static root is not empty
    if [ ! "$(ls -A /code/static_root)" ]; then
        echo "Warning: Static root directory is empty after collection!"
        # Don't exit on empty static root since frontend is separate
        echo "WARNING: Static root is empty, but continuing since frontend is separate"
        return 1
    fi

    return 0
}

# Main execution
main() {
    echo "Starting application initialization..."

    # Check required environment variables
    check_required_env

    # Wait for database with error handling
    if ! wait_for_db; then
        echo "Failed to establish database connection. Exiting..."
        exit 1
    fi

    # Run migrations
    run_migrations

    # Conditionally collect static files based on environment variable
    if [ "${COLLECT_STATIC}" = "true" ]; then
        echo "Static file collection enabled"
        collect_static
        # Note: We continue even if static file collection fails since frontend is separate
    else
        echo "Static file collection disabled (API-only mode)"
    fi

    # Start Gunicorn with proper settings
    echo "Starting Gunicorn..."
    exec gunicorn \
        --bind 0.0.0.0:${PORT} \
        --workers ${GUNICORN_WORKERS:-1} \
        --threads ${GUNICORN_THREADS:-8} \
        --timeout ${GUNICORN_TIMEOUT:-0} \
        --worker-class ${GUNICORN_WORKER_CLASS:-sync} \
        --worker-tmp-dir /dev/shm \
        --log-level ${GUNICORN_LOG_LEVEL:-info} \
        --access-logfile - \
        --error-logfile - \
        homescore.wsgi:application
}

# Run main function
main