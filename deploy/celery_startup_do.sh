#!/bin/bash

set -o errexit
set -o pipefail
set -o nounset

# Configuration for Celery startup script
MAX_RETRIES=30        # Maximum number of retries (matching docker_startup_do.sh)
RETRY_INTERVAL=2      # Seconds between retries
TIMEOUT=$((MAX_RETRIES * RETRY_INTERVAL))  # Total timeout in seconds
QUEUE=${QUEUE_NAME:-default}  # Default queue if not specified

# Log prefix for visibility
LOG_PREFIX="[CELERY-VALIDATE]"

echo "$LOG_PREFIX Starting Celery validation for queue: $QUEUE ($(date))"

# Function to check if required environment variables are set
check_required_env() {
    echo "$LOG_PREFIX Checking required environment variables"
    local required_vars=(
        "REDIS_URL"
        "CELERY_BROKER_URL"
        "DATABASE_URL"
        "DJANGO_SETTINGS_MODULE"
    )

    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            echo "$LOG_PREFIX ERROR: Required environment variable $var is not set"
            exit 1
        else
            # Print specific details based on the variable name for better debugging
            val="${!var}"
            if [[ "$var" == "DATABASE_URL" ]]; then
                local cluster_name; cluster_name=$(echo "$val" | sed -E 's/.*@([^:]+):.*/\1/')
                local db_name; db_name=$(echo "$val" | sed -E 's/.*\/([^\/]+)\?.*/\1/')
                echo "$LOG_PREFIX $var is set (cluster: $cluster_name, db: $db_name)"
            elif [[ "$var" == "REDIS_URL" || "$var" == "CELERY_BROKER_URL" ]]; then
                local redis_cluster; redis_cluster=$(echo "$val" | sed -E 's/.*@([^:]+):.*/\1/')
                echo "$LOG_PREFIX $var is set (cluster: $redis_cluster)"
            else
                # For other variables, just confirm they are set without showing value
                echo "$LOG_PREFIX $var is set (value hidden)"
            fi
        fi
    done

    # Check optional vars that might cause issues if empty
    local optional_vars=(
        "TOPK"
        "SIMILARITY_THRESHOLD"
        "PINECONE_ENV"
        "PINECONE_INDEX"
        "OPENAI_MODEL"
    )

    for var in "${optional_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            echo "$LOG_PREFIX WARNING: Optional variable $var is not set or empty"
        else
            val="${!var}"
            echo "$LOG_PREFIX $var is set (value hidden for security, length: $(echo -n "$val" | wc -c))"
        fi
    done
}

# Function to check system resources
check_system_resources() {
    echo "$LOG_PREFIX Checking system resources"
    
    # Get hostname (non-critical)
    hostname 2>/dev/null && echo "$LOG_PREFIX Hostname: $(hostname)" || echo "$LOG_PREFIX Warning: Could not get hostname"
    
    # Get system info (non-critical)
    uname -a 2>/dev/null && echo "$LOG_PREFIX System: $(uname -a)" || echo "$LOG_PREFIX Warning: Could not get system info"
    
    # Check disk space (non-critical)
    echo "$LOG_PREFIX Disk space:"
    df -h / 2>/dev/null || echo "$LOG_PREFIX Warning: Could not get disk space info"
    
    # Check memory info (non-critical)
    echo "$LOG_PREFIX Memory info:"
    if command -v free >/dev/null 2>&1; then
        free -m 2>/dev/null || echo "$LOG_PREFIX Warning: free command failed"
    else
        echo "$LOG_PREFIX Warning: free command not available"
        cat /proc/meminfo 2>/dev/null | grep -E 'MemTotal|MemFree|MemAvailable' || \
            echo "$LOG_PREFIX Warning: Could not read memory info from /proc/meminfo"
    fi
}

# Function to check Python and Celery
check_python_environment() {
    echo "$LOG_PREFIX Checking Python environment"
    
    # Check Python version
    echo "$LOG_PREFIX Python version:"
    python --version
    
    # Check Celery is installed
    if ! which celery > /dev/null; then
        echo "$LOG_PREFIX ERROR: Celery executable not found"
        exit 1
    fi
    
    echo "$LOG_PREFIX Celery version:"
    celery --version
}

# Function to test database connection
check_database() {
    echo "$LOG_PREFIX Testing database connection"
    
    local retries=0
    while [ $retries -lt $MAX_RETRIES ]; do
        if python -c "
import os, django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', '$DJANGO_SETTINGS_MODULE')
django.setup()
from django.db import connections
connections['default'].ensure_connection()
print('Database connection successful')
        " 2>/dev/null; then
            echo "$LOG_PREFIX Database connection successful"
            return 0
        fi
        
        retries=$((retries + 1))
        echo "$LOG_PREFIX Database connection attempt $retries failed, retrying in $RETRY_INTERVAL seconds..."
        sleep $RETRY_INTERVAL
    done
    
    echo "$LOG_PREFIX ERROR: Failed to connect to database after $MAX_RETRIES attempts"
    return 1
}

# Function to test broker connection
check_broker() {
    echo "$LOG_PREFIX Testing Celery broker connection"
    
    if python -c "import os, django; os.environ.update({'DJANGO_SETTINGS_MODULE': '$DJANGO_SETTINGS_MODULE', 'REDIS_URL': '$REDIS_URL', 'CELERY_BROKER_URL': '$CELERY_BROKER_URL'}); django.setup(); from homescore.celery import app; conn = app.connection(); conn.connect(); print('Broker connection successful')"; then
        echo "$LOG_PREFIX Broker connection successful"
        return 0
    else
        echo "$LOG_PREFIX ERROR: Failed to connect to Celery broker"
        return 1
    fi
}

# Function to validate settings
validate_settings() {
    echo "$LOG_PREFIX Validating Django settings"
    
    python3 -c "import os, django, sys; os.environ.update({'DJANGO_SETTINGS_MODULE': '$DJANGO_SETTINGS_MODULE', 'REDIS_URL': '$REDIS_URL', 'CELERY_BROKER_URL': '$CELERY_BROKER_URL', 'TOPK': '$TOPK', 'SIMILARITY_THRESHOLD': '$SIMILARITY_THRESHOLD', 'PINECONE_ENV': '$PINECONE_ENV', 'PINECONE_INDEX': '$PINECONE_INDEX', 'OPENAI_MODEL': '$OPENAI_MODEL'}); django.setup(); from django.conf import settings; required_settings = ['REDIS_URL', 'CELERY_BROKER_URL']; optional_settings = ['TOPK', 'SIMILARITY_THRESHOLD', 'PINECONE_ENV', 'PINECONE_INDEX', 'OPENAI_MODEL']; [sys.exit(1) if not hasattr(settings, setting) else print(f'Required setting {setting} is set (value hidden for security)') for setting in required_settings]; [print(f'WARNING: Optional setting {setting} is not set') if not hasattr(settings, setting) else print(f'Optional setting {setting} = {getattr(settings, setting)}') for setting in optional_settings]; print('Django settings validation complete')"
    
    if [ $? -ne 0 ]; then
        echo "$LOG_PREFIX ERROR: Django settings validation failed"
        return 1
    fi
    
    echo "$LOG_PREFIX Django settings validation successful"
    return 0
}

# Main function
main() {
    echo "$LOG_PREFIX Starting validation for Celery worker"
    
    # Run non-critical checks (these can fail without stopping deployment)
    check_system_resources || true
    
    # Run critical validation checks
    if ! check_required_env; then
        echo "$LOG_PREFIX CRITICAL ERROR: Required environment variables are missing or invalid"
        exit 1
    fi
    
    if ! check_python_environment; then
        echo "$LOG_PREFIX CRITICAL ERROR: Python environment validation failed"
        exit 1
    fi
    
    if ! check_database; then
        echo "$LOG_PREFIX CRITICAL ERROR: Could not connect to database. Check DATABASE_URL and database status"
        exit 1
    fi
    
    if ! check_broker; then
        echo "$LOG_PREFIX CRITICAL ERROR: Could not connect to Redis broker. Check REDIS_URL and Redis status"
        exit 1
    fi
    
    if ! validate_settings; then
        echo "$LOG_PREFIX CRITICAL ERROR: Django settings validation failed. Check required settings are properly configured"
        exit 1
    fi
    
    echo "$LOG_PREFIX All validation checks passed successfully"
    echo "$LOG_PREFIX Starting Celery worker with queue: $QUEUE"
    
    # Set up PATH to include necessary directories
    export PATH="$PATH:/usr/local/bin:/home/<USER>/.local/bin"
    echo "$LOG_PREFIX Current PATH: $PATH"
    which celery || echo "$LOG_PREFIX Celery not found"
    
    # Start Celery worker with the specified queue
    exec python -m celery -A homescore worker \
        --pool=gevent \
        -l ${LOG_LEVEL:-INFO} \
        --concurrency ${CONCURRENCY:-2} \
        -Q ${QUEUE}
}

# Run main function
main