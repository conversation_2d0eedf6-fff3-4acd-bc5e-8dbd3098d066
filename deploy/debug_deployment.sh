#!/bin/bash

# Colors for better visibility
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Print functions
print_section() { echo -e "\n${YELLOW}=== $1 ===${NC}"; }
print_success() { echo -e "${GREEN}✓ $1${NC}"; }
print_error() { echo -e "${RED}✗ $1${NC}"; HAS_ERRORS=1; }
print_warning() { echo -e "${YELLOW}! $1${NC}"; }

# Exit if any command fails
set -e

# Global variables
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
REPO_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
VALID_ENVIRONMENTS=("dev" "prod")
HAS_ERRORS=0

# Validate environment name
validate_environment() {
    local env=$1
    for valid_env in "${VALID_ENVIRONMENTS[@]}"; do
        if [ "$env" = "$valid_env" ]; then
            return 0
        fi
    done
    print_error "Invalid environment: $env. Must be one of: ${VALID_ENVIRONMENTS[*]}"
    return 1
}

# Validate required variables
validate_variables() {
    local missing=false
    
    if [ -z "$MY_DO_ACCESS_TOKEN" ]; then
        print_error "Missing MY_DO_ACCESS_TOKEN environment variable"
        missing=true
    fi
    
    if [ -z "$DO_APP_ID" ]; then
        print_warning "DO_APP_ID is not set. This is okay for first deployment."
        print_warning "After deployment, get the APP_ID from Digital Ocean and set it in GitHub variables."
    else
        print_success "DO_APP_ID is set to: $DO_APP_ID"
    fi
    
    if [ "$missing" = true ]; then
        return 1
    fi
    return 0
}

# Check container images
check_container_images() {
    local environment=$1
    local web_image="${TAG_WEB:-ghcr.io/homescore-ai/homescore-web:${environment}}"
    local celery_image="${TAG_CELERY:-ghcr.io/homescore-ai/homescore-celery:${environment}}"
    
    print_section "Container Images"
    echo "Web Image: $web_image"
    echo "Celery Image: $celery_image"
    
    # In GitHub Actions, just verify the image names
    if [ -n "$GITHUB_ACTIONS" ]; then
        print_warning "Running in GitHub Actions - skipping actual image pull test"
        print_warning "Images will be verified during the actual deployment"
        return 0
    fi
    
    # For local runs, try to pull the images
    if command -v docker &> /dev/null; then
        if ! docker pull "$web_image" &>/dev/null; then
            print_error "Web image not found: $web_image"
        else
            print_success "Web image is available"
        fi
        
        if ! docker pull "$celery_image" &>/dev/null; then
            print_error "Celery image not found: $celery_image"
        else
            print_success "Celery image is available"
        fi
    else
        print_warning "Docker not available - skipping image verification"
    fi
}

# Validate app spec file
validate_app_spec() {
    local spec_file=$1
    
    if [ ! -f "$spec_file" ]; then
        print_error "App spec file not found: $spec_file"
        return 1
    fi
    
    # Simple validation with grep
    if ! grep -q "name:" "$spec_file"; then
        print_warning "App spec might be missing 'name' field"
    fi
    
    if ! grep -q "services:" "$spec_file"; then
        print_warning "App spec might be missing 'services' section"
    fi
    
    print_success "App spec file exists and contains basic required fields"
    return 0
}

# Main debug function
debug_deployment() {
    local environment=$1
    local app_id=$2
    
    # Change to repo root directory
    cd "$REPO_ROOT" || {
        print_error "Failed to change to repository root directory"
        exit 1
    }
    
    print_section "Environment Check"
    echo "Current Environment: $environment"
    echo "App ID: $app_id"
    echo "GitHub Actions: ${GITHUB_ACTIONS:-No}"
    
    # Validate environment and variables
    validate_environment "$environment" || return 1
    validate_variables || return 1
    
    # Check for required tools
    print_section "Required Tools"
    if ! command -v doctl &> /dev/null; then
        print_error "doctl not found in PATH - ensure digitalocean/action-doctl@v2 action runs before this script"
        return 1
    fi
    print_success "doctl is available"
    
    print_section "Environment Variables"
    # Only print non-sensitive variables
    echo "DEBUG: ${DEBUG:-False}"
    echo "ENABLE_DEBUG_TOOLBAR: ${ENABLE_DEBUG_TOOLBAR:-False}"
    echo "STRIPE_LIVE_MODE: ${STRIPE_LIVE_MODE:-False}"
    echo "OPENAI_MAX_TOKENS: ${OPENAI_MAX_TOKENS:-4096}"
    echo "HOMESCORE_LOG_LEVEL: ${HOMESCORE_LOG_LEVEL:-DEBUG}"
    echo "DJANGO_LOG_LEVEL: ${DJANGO_LOG_LEVEL:-INFO}"
    
    print_section "App Specification"
    local spec_file
    if [ "$environment" = "prod" ]; then
        spec_file="$REPO_ROOT/.do/production.yaml"
    else
        spec_file="$REPO_ROOT/.do/staging.yaml"
    fi
    echo "Using app spec file: $spec_file"
    validate_app_spec "$spec_file" || return 1
    
    check_container_images "$environment"
    
    print_section "Deployment Status"
    
    # Simple approach: Initialize doctl with token
    echo "Authenticating doctl..."
    doctl auth init -t "$MY_DO_ACCESS_TOKEN" || {
        print_error "Failed to initialize doctl with token"
        return 1
    }
    
    # Show available contexts
    echo "Available contexts:"
    doctl auth list || echo "No contexts available"
    
    # Execute deployment status command without specifying format
    if [ "$app_id" != "CREATE_NEW" ]; then
        # Check if it's a valid UUID before using it
        if ! [[ "$app_id" =~ ^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$ ]]; then
            print_error "DO_APP_ID ('$app_id') is not 'CREATE_NEW' and not a valid UUID."
            return 1
        fi
        echo "Checking deployment status for existing app: $app_id"
        if doctl apps list-deployments "$app_id"; then
            print_success "Successfully retrieved deployment status"
        else
            print_error "Failed to retrieve deployment status for existing app"
            echo "Command attempted: doctl apps list-deployments $app_id"
            return 1
        fi
    else
        print_warning "DO_APP_ID is 'CREATE_NEW'. Skipping deployment status check for existing app."
    fi
    
    print_section "App Status"
    if [ "$app_id" != "CREATE_NEW" ]; then
        # Check if it's a valid UUID before getting status
        if ! [[ "$app_id" =~ ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$ ]]; then
            # Error already printed in the previous block, just return
            return 1
        fi
        echo "Checking app status for existing app: $app_id"
        if doctl apps get "$app_id"; then
            print_success "Successfully retrieved app status"
        else
            print_error "Failed to retrieve app status"
            return 1
        fi
    else
        print_warning "DO_APP_ID is 'CREATE_NEW'. Skipping app status check for existing app."
    fi
    
    print_section "Database Connections"
    # Use environment-specific cluster names
    if [ "$ENVIRONMENT" = "prod" ]; then
        if [ -z "$POSTGRES_CLUSTER_NAME" ]; then
            print_warning "PROD_POSTGRES_CLUSTER_NAME not set in GitHub variables"
            echo "Expected: homescore-prod-db"
        else
            echo "Postgres Cluster: $POSTGRES_CLUSTER_NAME"
        fi
        if [ -z "$REDIS_CLUSTER_NAME" ]; then
            print_warning "PROD_REDIS_CLUSTER_NAME not set in GitHub variables"
            echo "Expected: homescore-prod-redis"
        else
            echo "Redis Cluster: $REDIS_CLUSTER_NAME"
        fi
    else
        if [ -z "$POSTGRES_CLUSTER_NAME" ]; then
            print_warning "STAGING_POSTGRES_CLUSTER_NAME not set in GitHub variables"
            echo "Expected: homescore-staging-db"
        else
            echo "Postgres Cluster: $POSTGRES_CLUSTER_NAME"
        fi
        if [ -z "$REDIS_CLUSTER_NAME" ]; then
            print_warning "STAGING_REDIS_CLUSTER_NAME not set in GitHub variables"
            echo "Expected: homescore-staging-redis"
        else
            echo "Redis Cluster: $REDIS_CLUSTER_NAME"
        fi
    fi
    
    print_section "Deployment Summary"
    if [ $HAS_ERRORS -eq 0 ]; then
        print_success "All checks passed successfully"
    else
        print_error "Some checks failed - please review the errors above"
        return 1
    fi
}

# Main execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    ENVIRONMENT=${1:-dev}
    APP_ID=${2:-$DO_APP_ID}
    
    if [ -z "$APP_ID" ]; then
        print_warning "App ID is not set. This is okay for first deployment."
        print_warning "After deployment, get the APP_ID from Digital Ocean and set it in GitHub variables."
    fi
    
    debug_deployment "$ENVIRONMENT" "$APP_ID"
    exit $?
fi