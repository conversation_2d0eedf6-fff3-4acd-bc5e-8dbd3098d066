import unittest
import random
import string
from django.test import Client, TestCase
from django.contrib.auth import get_user_model
from apps.reports.models import Report

# Define the correct API endpoints based on the OpenAPI specification
REGISTRATION_URL = "/auth/registration/"
LOGIN_URL = "/auth/login/"
LOGOUT_URL = "/auth/logout/"
DELETE_USER_URL_TEMPLATE = "/api/auth/delete_user/{user_id}/"  # Assuming this is a valid endpoint for deletion

class HomeScoreBaseTest(TestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.client = Client()
        cls.create_test_user()
        cls.login()

        # If there are any setups like creating reports
        cls.REPORT = None
        cls.NEW_REPORT = None
        cls.create_new_report()
        cls.set_existing_report()

    @classmethod
    def create_test_user(cls):
        # Generate a random username and password
        random_string = "".join(random.choices(string.ascii_lowercase + string.digits, k=10))
        cls.test_username = f"testuser_{random_string}@example.com"
        cls.test_password = "".join(random.choices(string.ascii_letters + string.digits, k=12))

        # Create a new user via registration endpoint
        signup_data = {
            "username": cls.test_username,
            "email": cls.test_username,
            "password1": cls.test_password,
            "password2": cls.test_password,
        }

        # Use HTTPS since we require secure cookies
        response = cls.client.post(REGISTRATION_URL, signup_data, secure=True)
        
        if response.status_code != 201:
            raise Exception(f"Failed to create test user: {response.content}")
        
        cls.user = response.json().get("user")
        assert cls.user is not None, "Failed to get user ID after creation"

    @classmethod
    def login(cls):
        status = cls.client.login(username=cls.test_username, password=cls.test_password)
        if not status:
            raise Exception("Login failed.")
        # Debug: Check if session is set correctly

    @classmethod
    def tearDownClass(cls):
        cls.logout()
        cls.delete_test_user()
        super().tearDownClass()

    @classmethod
    def logout(cls):
        response = cls.client.post(LOGOUT_URL)
        assert response.status_code == 200, f"Logout failed: {response.content}"

    @classmethod
    def create_new_report(cls):
        """Create a new report for testing."""
        url = "/reports/upload/"
        data = {"address": "123 Test St, Testville, TX"}
        response = cls.client.post(url, data)

        assert response.status_code == 200, f"Failed to create report: {response.content}"
        cls.NEW_REPORT = response.json().get('id')  # Assume the response contains the new report ID

    @classmethod
    def set_existing_report(cls):
        """Set an existing report with has_report=True."""
        existing_report = Report.objects.filter(has_report=True).first()
        if existing_report:
            cls.REPORT = existing_report.id
        else:
            raise Exception("No report with has_report=True found in the database.")

    @classmethod
    def delete_test_user(cls):
        # Assuming Django's ORM for user deletion
        from django.contrib.auth import get_user_model
        User = get_user_model()
        User.objects.filter(username=cls.test_username).delete()

    def get_json_response(self, url, method="get", **kwargs):
        cls = self.__class__
        response = getattr(cls.client, method)(url, **kwargs)
        if response.status_code >= 400:
            raise Exception(f"Request failed with status {response.status_code}: {response.content}")
        return response.json()
