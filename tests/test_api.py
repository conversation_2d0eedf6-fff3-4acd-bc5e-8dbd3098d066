import unittest
from .base_test import HomeScoreBaseTest

# Define API endpoint URLs as constants
WHOAMI_URL = "/users/whoami/"
GET_ALL_HOMES_URL = "/reports/reports/"

class TestHomeScoreAPI(HomeScoreBaseTest):
    def test_whoami(self):
        """Test the WhoAMI endpoint"""
        data = self.get_json_response(WHOAMI_URL)
        print(data)

        self.assertIn('id', data)
        self.assertIn('username', data)
        self.assertIn('email', data)
        self.assertIsInstance(data['is_superuser'], bool)

    def test_get_all_homes(self):
        """Test the Get All Homes endpoint"""
        data = self.get_json_response(GET_ALL_HOMES_URL)

        self.assertIsInstance(data, list)
        for home in data:
            self.assertIn('id', home)
            self.assertIn('name', home)
            self.assertIn('has_report', home)
            self.assertIn('file_name', home)
            self.assertIn('summary_status', home)
            self.assertIn('chat_status', home)
            self.assertIn('address', home)

if __name__ == '__main__':
    unittest.main()
