FROM python:3.11-slim-buster
ENV PYTHONUNBUFFERED=1

# Install only required system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    gettext \
    procps \
    && rm -rf /var/lib/apt/lists/*

# Create and set work directory
WORKDIR /code

# Install Python dependencies first to leverage Docker cache
COPY requirements.txt requirements.txt
COPY requirements/ requirements/
RUN pip install --no-cache-dir -r requirements.txt

# Explicitly install celery to ensure it's in the PATH
RUN pip install --no-cache-dir celery[redis,gevent]

# Copy the application code first
COPY . /code/

# Create required directories and set permissions
RUN mkdir -p /var/www && \
    chown -R www-data:www-data /code /var/www && \
    chmod +x /code/deploy/*.sh

# Ensure deploy scripts are executable by copying them again
COPY --chmod=0755 deploy/celery_startup_do.sh /code/deploy/
COPY --chmod=0755 deploy/debug_deployment.sh /code/deploy/

# Switch to non-root user
USER www-data

# Set PATH to include user's local bin directory
ENV PATH="/home/<USER>/.local/bin:${PATH}"